import { deLocalizeUrl } from '$lib/paraglide/runtime';

export const reroute = (request) => {
  try {
    // Make sure request.url is defined and is a URL object
    if (request && request.url) {
      return deLocalizeUrl(request.url).pathname;
    }
    // Fallback if request.url is not defined
    return '/';
  } catch (error) {
    console.error('Error in reroute function:', error);
    // Fallback in case of error
    return '/';
  }
};
