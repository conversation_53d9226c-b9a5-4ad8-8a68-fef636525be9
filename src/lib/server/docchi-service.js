/**
 * Service for communicating with the docchi.pl API
 * Based on the TowarzyszFatCat/doccli project
 */

/**
 * Get list of players for a specific episode
 * @param {string} slug - The anime slug
 * @param {number} episodeNumber - The episode number
 * @returns {Promise<Array>} - List of players
 */
export async function getPlayersList(slug, episodeNumber) {
    try {
        console.log(`[Docchi Service] Getting players for ${slug}, episode ${episodeNumber}`);
        
        const response = await fetch(`https://api.docchi.pl/v1/episodes/find/${slug}/${episodeNumber}`);
        
        if (!response.ok) {
            console.error(`[Docchi Service] API Error: ${response.status}`);
            return { error: `API Error: ${response.status}` };
        }
        
        const data = await response.json();
        return { players: data };
    } catch (error) {
        console.error('[Docchi Service] Error getting players:', error);
        return { error: error.message };
    }
}

/**
 * Get the number of episodes for a series
 * @param {string} slug - The anime slug
 * @returns {Promise<number>} - Number of episodes
 */
export async function getEpisodesCount(slug) {
    try {
        console.log(`[Docchi Service] Getting episode count for ${slug}`);
        
        const response = await fetch(`https://api.docchi.pl/v1/episodes/count/${slug}`);
        
        if (!response.ok) {
            console.error(`[Docchi Service] API Error: ${response.status}`);
            return { error: `API Error: ${response.status}` };
        }
        
        const data = await response.json();
        return { count: data.length };
    } catch (error) {
        console.error('[Docchi Service] Error getting episode count:', error);
        return { error: error.message };
    }
}

/**
 * Get list of all anime series
 * @returns {Promise<Array>} - List of series
 */
export async function getSeriesList() {
    try {
        console.log('[Docchi Service] Getting series list');
        
        const response = await fetch('https://api.docchi.pl/v1/series/list');
        
        if (!response.ok) {
            console.error(`[Docchi Service] API Error: ${response.status}`);
            return { error: `API Error: ${response.status}` };
        }
        
        const data = await response.json();
        return { series: data };
    } catch (error) {
        console.error('[Docchi Service] Error getting series list:', error);
        return { error: error.message };
    }
}

/**
 * Get detailed information about a series
 * @param {string} slug - The anime slug
 * @returns {Promise<Object>} - Series details
 */
export async function getSeriesDetails(slug) {
    try {
        console.log(`[Docchi Service] Getting details for ${slug}`);
        
        const response = await fetch(`https://api.docchi.pl/v1/series/find/${slug}`);
        
        if (!response.ok) {
            console.error(`[Docchi Service] API Error: ${response.status}`);
            return { error: `API Error: ${response.status}` };
        }
        
        const data = await response.json();
        return { details: data };
    } catch (error) {
        console.error('[Docchi Service] Error getting series details:', error);
        return { error: error.message };
    }
}

/**
 * Get skip times for openings and endings from AniSkip API
 * @param {string} malId - MyAnimeList ID
 * @param {number} episodeNumber - Episode number
 * @returns {Promise<Array>} - Skip times [openingStart, openingEnd, endingStart, endingEnd]
 */
export async function getSkipTimes(malId, episodeNumber) {
    try {
        console.log(`[Docchi Service] Getting skip times for MAL ID ${malId}, episode ${episodeNumber}`);
        
        const response = await fetch(
            `https://api.aniskip.com/v2/skip-times/${malId}/${episodeNumber}?types=op&types=ed&types=mixed-op&types=mixed-ed&types=recap&episodeLength=0`
        );
        
        if (!response.ok) {
            console.error(`[Docchi Service] AniSkip API Error: ${response.status}`);
            return { skipTimes: [-1, -1, -1, -1] };
        }
        
        const data = await response.json();
        const skipTimes = [-1, -1, -1, -1]; // [openingStart, openingEnd, endingStart, endingEnd]
        
        if (data.found) {
            for (const result of data.results) {
                const times = result.interval;
                
                // Handle openings
                if (result.skipType === 'op') {
                    skipTimes[0] = times.startTime;
                    skipTimes[1] = times.endTime;
                }
                
                // Handle endings
                if (result.skipType === 'ed') {
                    skipTimes[2] = times.startTime;
                    skipTimes[3] = times.endTime;
                }
            }
        }
        
        return { skipTimes };
    } catch (error) {
        console.error('[Docchi Service] Error getting skip times:', error);
        return { skipTimes: [-1, -1, -1, -1], error: error.message };
    }
}

/**
 * Search for anime by title
 * @param {string} query - Search query
 * @returns {Promise<Array>} - Search results
 */
export async function searchAnime(query) {
    try {
        console.log(`[Docchi Service] Searching for anime: ${query}`);
        
        // Get all series first
        const { series, error } = await getSeriesList();
        
        if (error) {
            return { error };
        }
        
        // Filter series by title (case insensitive)
        const lowerQuery = query.toLowerCase();
        const results = series.filter(anime => 
            anime.title.toLowerCase().includes(lowerQuery) || 
            (anime.title_en && anime.title_en.toLowerCase().includes(lowerQuery))
        );
        
        console.log(`[Docchi Service] Found ${results.length} results for query: ${query}`);
        
        return { results };
    } catch (error) {
        console.error('[Docchi Service] Error searching anime:', error);
        return { error: error.message };
    }
}
