/**
 * Direct integration with nxbrew-dl code
 * This module uses nxbrew-dl's code directly instead of running the Qt app
 *
 * IMPORTANT: This module should only be imported dynamically at runtime,
 * never during build time to prevent the GUI from opening.
 */

import fs from 'fs/promises';
import path from 'path';

// Only import child_process and util in a server environment
const isServer = typeof window === 'undefined';
let execAsync;

// Dynamically initialize execAsync only in a server environment
if (isServer) {
  const { exec } = await import('child_process');
  const { promisify } = await import('util');
  execAsync = promisify(exec);
} else {
  // Mock implementation for client-side
  execAsync = async () => ({ stdout: '', stderr: 'Not available in browser' });
}

/**
 * Get the configuration for nxbrew-dl
 * @returns {Promise<Object>} Configuration object
 */
export async function getNxbrewConfig() {
    // Skip during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew config retrieval in non-server environment');
        return {
            preferred_format: 'NSP',
            download_updates: true,
            download_dlc: true,
            preferred_regions: ['USA', 'EUR'],
            preferred_languages: ['English'],
            base_url: 'https://nxbrew.net/'
        };
    }

    try {
        const configPath = path.join(process.cwd(), '.config', 'nxbrew-dl', 'config.json');
        const configData = await fs.readFile(configPath, 'utf-8');
        return JSON.parse(configData);
    } catch (error) {
        console.error('Error reading nxbrew-dl config:', error);
        // Return default config
        return {
            preferred_format: 'NSP',
            download_updates: true,
            download_dlc: true,
            preferred_regions: ['USA', 'EUR'],
            preferred_languages: ['English'],
            base_url: 'https://nxbrew.net/'
        };
    }
}

/**
 * Save the configuration for nxbrew-dl
 * @param {Object} config Configuration object to save
 * @returns {Promise<Object>} Result of the operation
 */
export async function saveNxbrewConfig(config) {
    // Skip during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew config save in non-server environment');
        return {
            success: false,
            message: 'Cannot save configuration in non-server environment'
        };
    }

    try {
        // Create config directory if it doesn't exist
        const configDir = path.join(process.cwd(), '.config', 'nxbrew-dl');
        await fs.mkdir(configDir, { recursive: true });

        // Write config file
        const configPath = path.join(configDir, 'config.json');
        await fs.writeFile(configPath, JSON.stringify(config, null, 2));

        return { success: true, message: 'Configuration saved successfully' };
    } catch (error) {
        console.error('Error saving nxbrew-dl config:', error);
        return {
            success: false,
            message: `Failed to save configuration: ${error.message}`
        };
    }
}

/**
 * Search for games using nxbrew-dl's code directly
 * @param {string} query Search query
 * @returns {Promise<Array>} Search results
 */
export async function searchGames(query) {
    // Skip during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew game search in non-server environment');
        return [];
    }

    try {
        // Use the existing nxbrew_search.py script instead of generating a new one
        const scriptPath = path.join(process.cwd(), 'nxbrew_search.py');

        // Execute the Python script
        const { stdout, stderr } = await execAsync(`python3 ${scriptPath} "${query}"`);

        if (stderr) {
            console.error('Debug info from Python script:', stderr);
        }

        // Parse the results
        const results = JSON.parse(stdout);
        console.log(`Found ${results.length} results for query "${query}"`);
        return results;
    } catch (error) {
        console.error('Error searching games:', error);
        return [];
    }
}

/**
 * Download a game using nxbrew-dl's code directly
 * @param {string} gameUrl URL of the game to download
 * @returns {Promise<Object>} Download result
 */
export async function downloadGame(gameUrl) {
    // Skip during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew game download in non-server environment');
        return {
            success: false,
            message: 'Cannot download games in non-server environment'
        };
    }

    try {
        console.log(`Getting download links for game URL: ${gameUrl}`);
        const config = await getNxbrewConfig();
        console.log('Using config:', JSON.stringify(config, null, 2));

        // Use the new nxbrew_direct.py script that doesn't use JDownloader
        const scriptPath = path.join(process.cwd(), 'nxbrew_direct.py');

        console.log('Executing Python script to get download links...');

        // Execute the Python script with the game URL and config as arguments
        const { stdout, stderr } = await execAsync(`python3 ${scriptPath} "${gameUrl}" '${JSON.stringify(config)}'`);

        if (stderr) {
            console.log('Debug info from Python script:');
            console.log(stderr);
        }

        try {
            // Parse the results
            const result = JSON.parse(stdout);
            console.log('Download links result:', JSON.stringify(result, null, 2));
            return result;
        } catch (parseError) {
            console.error('Error parsing Python script output:', parseError);
            console.error('Raw output:', stdout);
            return {
                success: false,
                message: `Error parsing script output: ${parseError.message}`,
                debug: {
                    stdout,
                    stderr
                }
            };
        }
    } catch (error) {
        console.error('Error downloading game:', error);
        return {
            success: false,
            message: `Error downloading game: ${error.message}`,
            error: error.toString()
        };
    }
}
