/**
 * Service for searching and fetching ROM/ISO games from blueroms.ws
 */

import fetch from 'node-fetch';
import { load } from 'cheerio';
import { URL } from 'url';

// Base URL for blueroms.ws
const BASE_URL = 'https://www.blueroms.ws';

/**
 * Convert a relative URL to an absolute URL
 * @param {string} url - The URL to convert
 * @param {string} baseUrl - The base URL to use
 * @returns {string} - The absolute URL
 */
function toAbsoluteUrl(url, baseUrl = BASE_URL) {
    if (!url) return url;

    try {
        // Check if it's already an absolute URL
        new URL(url);
        return url;
    } catch (e) {
        // It's a relative URL, make it absolute
        return url.startsWith('/')
            ? `${baseUrl}${url}`
            : `${baseUrl}/${url}`;
    }
}

/**
 * Search for games on blueroms.ws
 * @param {string} query - The search query
 * @returns {Promise<Object>} - Search results or error
 */
export async function searchGames(query) {
    try {
        console.log(`[BlueRoms Service] Searching for games with query: ${query}`);

        // Format the search URL
        const searchUrl = `https://www.blueroms.ws/search?g=0&p=0&q=${encodeURIComponent(query)}`;
        console.log(`[BlueRoms Service] Search URL: ${searchUrl}`);

        // Fetch the search results page
        const response = await fetch(searchUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (!response.ok) {
            console.error(`[BlueRoms Service] Search request failed with status: ${response.status}`);
            return {
                error: `Search request failed with status: ${response.status}`,
                results: []
            };
        }

        const html = await response.text();

        // Parse the HTML using cheerio
        const $ = load(html);
        const results = [];

        // Find all game cards
        $('.card').each((index, element) => {
            try {
                // Extract game information
                const title = $(element).find('.card-title').text().trim();

                // Skip if no title found
                if (!title) return;

                // Extract game details
                const detailsText = $(element).find('.card-text').text().trim();

                // Parse platform, size, and release date from details text
                let platform = '';
                let size = '';
                let releaseDate = '';

                // Extract platform, size, and release date using regex
                const platformMatch = detailsText.match(/Platform: ([^\n]+)/);
                if (platformMatch) platform = platformMatch[1].trim();

                const sizeMatch = detailsText.match(/Size: ([^\n]+)/);
                if (sizeMatch) size = sizeMatch[1].trim();

                const releaseDateMatch = detailsText.match(/Release Date: ([^\n]+)/);
                if (releaseDateMatch) releaseDate = releaseDateMatch[1].trim();

                // Get the game URL and make it absolute
                const url = toAbsoluteUrl($(element).find('a.btn-primary').attr('href'));

                // Get the image URL and make it absolute
                const imageUrl = toAbsoluteUrl($(element).find('.card-image a img').attr('src'));

                // Add to results
                results.push({
                    title,
                    platform,
                    size,
                    releaseDate,
                    url,
                    imageUrl
                });
            } catch (err) {
                console.error(`[BlueRoms Service] Error parsing game card: ${err.message}`);
            }
        });

        console.log(`[BlueRoms Service] Found ${results.length} results`);
        return { results };
    } catch (error) {
        console.error('[BlueRoms Service] Search error:', error);
        return {
            error: `An error occurred during search: ${error.message}`,
            results: []
        };
    }
}

/**
 * Get game details from its page
 * @param {string} url - The game page URL
 * @returns {Promise<Object>} - Game details or error
 */
export async function getGameDetails(url) {
    try {
        console.log(`[BlueRoms Service] Getting game details from: ${url}`);

        // Fetch the game page
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (!response.ok) {
            console.error(`[BlueRoms Service] Game page request failed with status: ${response.status}`);
            return {
                error: `Game page request failed with status: ${response.status}`
            };
        }

        const html = await response.text();

        // Parse the HTML using cheerio
        const $ = load(html);

        // Extract game information
        const title = $('.card-title').first().text().trim();
        const description = $('.card-text').first().text().trim();

        // Extract download links and make it absolute
        const downloadUrl = toAbsoluteUrl($('.btn-primary').filter((i, el) => $(el).text().includes('Download')).attr('href'));

        // Extract image URL if available and make it absolute
        const imageUrl = toAbsoluteUrl($('.card-image img').attr('src'));

        // Extract download page URL from card-footer
        const downloadPageUrl = toAbsoluteUrl($('.card-footer a').attr('href'));

        return {
            title,
            description,
            downloadUrl,
            imageUrl,
            downloadPageUrl
        };
    } catch (error) {
        console.error('[BlueRoms Service] Error getting game details:', error);
        return {
            error: `An error occurred while getting game details: ${error.message}`
        };
    }
}

/**
 * Get magnet link from download page
 * @param {string} url - The download page URL
 * @returns {Promise<Object>} - Magnet link or error
 */
export async function getMagnetLink(url) {
    try {
        console.log(`[BlueRoms Service] Getting magnet link from: ${url}`);

        // Fetch the download page
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (!response.ok) {
            console.error(`[BlueRoms Service] Download page request failed with status: ${response.status}`);
            return {
                error: `Download page request failed with status: ${response.status}`
            };
        }

        const html = await response.text();

        // Parse the HTML using cheerio
        const $ = load(html);

        // Look for magnet link - typically it's an anchor with href starting with "magnet:"
        let magnetLink = '';
        $('a').each((i, el) => {
            const href = $(el).attr('href');
            if (href && href.startsWith('magnet:')) {
                magnetLink = href;
                return false; // Break the loop once found
            }
        });

        if (!magnetLink) {
            console.error('[BlueRoms Service] No magnet link found on the download page');
            return {
                error: 'No magnet link found on the download page'
            };
        }

        console.log(`[BlueRoms Service] Found magnet link: ${magnetLink.substring(0, 50)}...`);

        return {
            magnetLink
        };
    } catch (error) {
        console.error('[BlueRoms Service] Error getting magnet link:', error);
        return {
            error: `An error occurred while getting magnet link: ${error.message}`
        };
    }
}
