/**
 * Watch service for managing watched search queries
 * This service handles storing and retrieving watched items,
 * as well as checking for new results
 */

import fs from 'fs/promises';
import path from 'path';
import { searchPcGames } from './pc-search-discord.js';

// Path to the watched items file
const WATCH_FILE_PATH = path.join(process.cwd(), '.config', 'watched-items.json');

/**
 * Get all watched items
 * @returns {Promise<Array>} List of watched items
 */
export async function getWatchedItems() {
    try {
        // Ensure the config directory exists
        await fs.mkdir(path.join(process.cwd(), '.config'), { recursive: true });

        // Try to read the watched items file
        try {
            const data = await fs.readFile(WATCH_FILE_PATH, 'utf-8');
            return JSON.parse(data);
        } catch (error) {
            // If file doesn't exist or is invalid, return empty array
            if (error.code === 'ENOENT' || error instanceof SyntaxError) {
                return [];
            }
            throw error;
        }
    } catch (error) {
        console.error('Error getting watched items:', error);
        return [];
    }
}

/**
 * Save watched items to file
 * @param {Array} items Watched items to save
 * @returns {Promise<boolean>} Success status
 */
export async function saveWatchedItems(items) {
    try {
        // Ensure the config directory exists
        await fs.mkdir(path.join(process.cwd(), '.config'), { recursive: true });

        // Write the items to file
        await fs.writeFile(WATCH_FILE_PATH, JSON.stringify(items, null, 2), 'utf-8');
        return true;
    } catch (error) {
        console.error('Error saving watched items:', error);
        return false;
    }
}

/**
 * Add a new watched item
 * @param {string} query Search query to watch
 * @returns {Promise<Object>} Result object with success status
 */
export async function addWatchedItem(query) {
    try {
        if (!query || query.trim().length < 3) {
            return {
                success: false,
                message: 'Query must be at least 3 characters long'
            };
        }

        // Get existing items
        const items = await getWatchedItems();

        // Check if query already exists
        const normalizedQuery = query.trim().toLowerCase();
        if (items.some(item => item.query.toLowerCase() === normalizedQuery)) {
            return {
                success: false,
                message: 'This query is already being watched'
            };
        }

        // Add new item
        const newItem = {
            id: generateId(),
            query: query.trim(),
            dateAdded: new Date().toISOString(),
            lastChecked: null,
            lastNotified: null,
            hasResults: false
        };

        items.push(newItem);

        // Save updated items
        const saveResult = await saveWatchedItems(items);

        if (saveResult) {
            return {
                success: true,
                item: newItem
            };
        } else {
            return {
                success: false,
                message: 'Failed to save watched item'
            };
        }
    } catch (error) {
        console.error('Error adding watched item:', error);
        return {
            success: false,
            message: `Error: ${error.message}`
        };
    }
}

/**
 * Remove a watched item
 * @param {string} id ID of the item to remove
 * @returns {Promise<Object>} Result object with success status
 */
export async function removeWatchedItem(id) {
    try {
        // Get existing items
        const items = await getWatchedItems();

        // Find the item index
        const itemIndex = items.findIndex(item => item.id === id);

        if (itemIndex === -1) {
            return {
                success: false,
                message: 'Watched item not found'
            };
        }

        // Remove the item
        items.splice(itemIndex, 1);

        // Save updated items
        const saveResult = await saveWatchedItems(items);

        if (saveResult) {
            return {
                success: true
            };
        } else {
            return {
                success: false,
                message: 'Failed to remove watched item'
            };
        }
    } catch (error) {
        console.error('Error removing watched item:', error);
        return {
            success: false,
            message: `Error: ${error.message}`
        };
    }
}

/**
 * Check for new results for all watched items
 * @returns {Promise<Object>} Result object with items that have new results
 */
export async function checkWatchedItems() {
    try {
        // Get all watched items
        const items = await getWatchedItems();

        if (items.length === 0) {
            return {
                success: true,
                newResults: []
            };
        }

        const itemsWithNewResults = [];
        const updatedItems = [];

        // Check each item for new results
        for (const item of items) {
            try {
                console.log(`Checking watched item: ${item.query}`);

                // Search for the query
                const results = await searchPcGames(item.query, {
                    maxResults: 5,
                    sources: {
                        fitgirl: true,
                        dodi: true,
                        gog: true,
                        gamedrive: true
                    }
                });

                // Update the item
                const updatedItem = {
                    ...item,
                    lastChecked: new Date().toISOString(),
                    hasResults: results.length > 0
                };

                // If there are results and the item hasn't been notified yet
                // or was notified but has new results since last notification
                if (results.length > 0) {
                    // If the item hasn't been notified yet or was notified before the last check
                    if (!item.lastNotified ||
                        (item.lastNotified && new Date(item.lastNotified) < new Date(item.lastChecked))) {

                        // Don't update lastNotified here - we'll do that when the user acknowledges

                        itemsWithNewResults.push({
                            item: updatedItem,
                            results
                        });
                    }
                }

                updatedItems.push(updatedItem);
            } catch (error) {
                console.error(`Error checking watched item ${item.query}:`, error);
                // Keep the item unchanged
                updatedItems.push(item);
            }
        }

        // Save the updated items
        await saveWatchedItems(updatedItems);

        return {
            success: true,
            newResults: itemsWithNewResults
        };
    } catch (error) {
        console.error('Error checking watched items:', error);
        return {
            success: false,
            message: `Error: ${error.message}`,
            newResults: []
        };
    }
}

/**
 * Mark a watched item as notified
 * @param {string} id ID of the item to mark
 * @returns {Promise<Object>} Result object with success status
 */
export async function markWatchedItemAsNotified(id) {
    try {
        // Get existing items
        const items = await getWatchedItems();

        // Find the item
        const itemIndex = items.findIndex(item => item.id === id);

        if (itemIndex === -1) {
            return {
                success: false,
                message: 'Watched item not found'
            };
        }

        // Update the item
        items[itemIndex] = {
            ...items[itemIndex],
            lastNotified: new Date().toISOString()
        };

        // Save updated items
        const saveResult = await saveWatchedItems(items);

        if (saveResult) {
            return {
                success: true,
                item: items[itemIndex]
            };
        } else {
            return {
                success: false,
                message: 'Failed to update watched item'
            };
        }
    } catch (error) {
        console.error('Error marking watched item as notified:', error);
        return {
            success: false,
            message: `Error: ${error.message}`
        };
    }
}

/**
 * Generate a unique ID for a watched item
 * @returns {string} Unique ID
 */
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 9);
}
