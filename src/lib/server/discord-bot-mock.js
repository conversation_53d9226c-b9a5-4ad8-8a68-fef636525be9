// Mock dla discord.js, k<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> u<PERSON><PERSON>wany podczas budowania
// Ten plik jest używany tylko podczas budowania, a nie w czasie wykonania

export class Collection extends Map {
    constructor() {
        super();
    }
}

export const Client = class Client {
    constructor() {
        this.user = {
            tag: 'MockBot',
            setActivity: () => {}
        };
        this.commands = new Collection();
    }

    login() {
        return Promise.resolve();
    }

    destroy() {
        return;
    }

    isReady() {
        return false;
    }

    once() {
        return this;
    }

    on() {
        return this;
    }
};

export const GatewayIntentBits = {
    Guilds: 1,
    GuildMessages: 2,
    MessageContent: 4,
    DirectMessages: 8
};

export const ActivityType = {
    Playing: 0,
    Streaming: 1,
    Listening: 2,
    Watching: 3,
    Competing: 4
};

export const ChannelType = {
    DM: 1,
    GuildText: 0
};

export const ApplicationCommandOptionType = {
    String: 3,
    Integer: 4,
    Boolean: 5,
    User: 6,
    Channel: 7,
    Role: 8,
    Mentionable: 9,
    Number: 10,
    Attachment: 11
};

export const Events = {
    ClientReady: 'ready',
    MessageCreate: 'messageCreate',
    InteractionCreate: 'interactionCreate'
};

export class SlashCommandBuilder {
    constructor() {
        this.name = '';
        this.description = '';
        this.options = [];
    }

    setName(name) {
        this.name = name;
        return this;
    }

    setDescription(description) {
        this.description = description;
        return this;
    }

    addStringOption(callback) {
        const option = {
            type: ApplicationCommandOptionType.String,
            name: '',
            description: '',
            required: false,
            setName: function(name) {
                this.name = name;
                return this;
            },
            setDescription: function(description) {
                this.description = description;
                return this;
            },
            setRequired: function(required) {
                this.required = required;
                return this;
            },
            setMinLength: function(minLength) {
                this.minLength = minLength;
                return this;
            }
        };

        this.options.push(callback(option));
        return this;
    }

    addIntegerOption(callback) {
        const option = {
            type: ApplicationCommandOptionType.Integer,
            name: '',
            description: '',
            required: false,
            setName: function(name) {
                this.name = name;
                return this;
            },
            setDescription: function(description) {
                this.description = description;
                return this;
            },
            setRequired: function(required) {
                this.required = required;
                return this;
            },
            setMinValue: function(minValue) {
                this.minValue = minValue;
                return this;
            },
            setMaxValue: function(maxValue) {
                this.maxValue = maxValue;
                return this;
            }
        };

        this.options.push(callback(option));
        return this;
    }

    toJSON() {
        return {
            name: this.name,
            description: this.description,
            options: this.options
        };
    }
}

export const REST = class REST {
    constructor() {}

    setToken() {
        return this;
    }

    put() {
        return Promise.resolve();
    }
};

export const Routes = {
    applicationCommands: (clientId) => `/applications/${clientId}/commands`
};
