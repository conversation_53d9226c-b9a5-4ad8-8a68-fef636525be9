/**
 * Torrentio service for movie and TV show search
 * Handles communication with Torrentio API
 */

import { cleanMediaTitle } from '../utils/apiUtils';
import { getMoviePosterByTitle } from '../services/omdbService';
import fetch from 'node-fetch';
import { OMDB_CONFIG } from '../config/apiConfig';

// Torrentio API base URL
export const TORRENTIO_API_BASE = 'https://torrentio.strem.fun';
const OMDB_API_KEY = OMDB_CONFIG.API_KEY;

/**
 * Search for movies and TV shows using OMDB first, then Torrentio
 * @param {string} query Search query
 * @param {boolean} showCompleteSeasons Whether to prioritize complete seasons
 * @returns {Promise<Array>} Search results
 */
export async function searchMedia(query) {
    try {
        console.log(`[Torrentio] Searching for media with query: ${query}`);

        if (!query || query.trim().length < 3) {
            console.log('[Torrentio] Query too short, minimum 3 characters required');
            return [];
        }

        // Check if the query is an IMDB ID
        const imdbIdMatch = query.match(/^(tt\d+)$/i);
        if (imdbIdMatch) {
            console.log(`[Torrentio] Direct IMDB ID search: ${imdbIdMatch[1]}`);
            // Create a mock OMDB item with the IMDB ID
            const mockOmdbItem = {
                imdbID: imdbIdMatch[1],
                Title: "Loading...",
                Year: "",
                Type: "movie", // We'll try both movie and series
                Poster: "N/A"
            };

            // Try as movie first
            let torrentioResults = await getTorrentioStreams(mockOmdbItem);

            // If no results, try as series
            if (torrentioResults.length === 0) {
                mockOmdbItem.Type = "series";
                torrentioResults = await getTorrentioStreams(mockOmdbItem);
            }

            // Sort episodes by season and episode (descending)
            torrentioResults.sort((a, b) => {
                // If both are episodes
                if (a.episodeInfo && b.episodeInfo) {
                    // Sort by season (descending)
                    if (b.episodeInfo.season !== a.episodeInfo.season) {
                        return b.episodeInfo.season - a.episodeInfo.season;
                    }
                    // Then by episode (descending)
                    return b.episodeInfo.episode - a.episodeInfo.episode;
                }
                // Base TV show entries first
                if (a.isBaseTVShow) return -1;
                if (b.isBaseTVShow) return 1;
                // Otherwise sort by seeders
                return b.seeders - a.seeders;
            });

            return torrentioResults;
        }

        // First, search for the movie/show in OMDB to get IMDB IDs
        const omdbResults = await searchOMDB(query);
        console.log(`[Torrentio] Found ${omdbResults.length} results from OMDB`);

        if (omdbResults.length === 0) {
            return [];
        }

        // For each OMDB result, get torrents from Torrentio
        const allResults = [];
        const processedImdbIds = new Set(); // Track processed IMDB IDs to avoid duplicates

        for (const omdbItem of omdbResults) {
            // Skip items without IMDB ID
            if (!omdbItem.imdbID) continue;

            // Skip if we've already processed this IMDB ID
            if (processedImdbIds.has(omdbItem.imdbID)) continue;

            processedImdbIds.add(omdbItem.imdbID);

            const torrentioResults = await getTorrentioStreams(omdbItem);

            if (torrentioResults.length > 0) {
                allResults.push(...torrentioResults);
            }
        }

        // Sort results: TV shows first, then movies by seeders
        allResults.sort((a, b) => {
            // Base TV show entries first
            if (a.isBaseTVShow && !b.isBaseTVShow) return -1;
            if (!a.isBaseTVShow && b.isBaseTVShow) return 1;

            // If both are episodes
            if (a.episodeInfo && b.episodeInfo) {
                // Sort by season (descending)
                if (b.episodeInfo.season !== a.episodeInfo.season) {
                    return b.episodeInfo.season - a.episodeInfo.season;
                }
                // Then by episode (descending)
                return b.episodeInfo.episode - a.episodeInfo.episode;
            }

            // Otherwise sort by seeders
            return b.seeders - a.seeders;
        });

        return allResults;
    } catch (error) {
        console.error('[Torrentio] Error searching media:', error);
        return [];
    }
}

/**
 * Process results to prioritize and mark complete seasons
 * @param {Array} results Array of torrent results
 * @returns {Array} Processed results with complete seasons prioritized
 */
function processCompleteSeasons(results) {
    // Create a copy of the results
    const processedResults = [...results];

    // Find complete season torrents
    const completeSeasonResults = processedResults.filter(result => {
        const title = result.originalTitle.toLowerCase();
        return (
            result.type === 'tvshow' &&
            (title.includes('complete') ||
             title.includes('season') ||
             title.includes('s01') ||
             title.includes('s1 ') ||
             title.includes('season 1') ||
             title.includes('season.1') ||
             title.includes('season01') ||
             title.includes('season.01'))
        );
    });

    // Mark complete seasons
    completeSeasonResults.forEach(result => {
        result.isCompleteSeason = true;
        result.title = `[COMPLETE] ${result.title}`;
    });

    // Move complete seasons to the top
    const nonCompleteResults = processedResults.filter(result => !result.isCompleteSeason);

    return [...completeSeasonResults, ...nonCompleteResults];
}

/**
 * Verify IMDB ID with Trakt.tv API
 * @param {string} title Show title
 * @param {string} imdbId IMDB ID to verify
 * @returns {Promise<string|null>} Verified IMDB ID or null if not found
 */
async function verifyImdbIdWithTrakt(title, imdbId) {
    try {
        console.log(`[Torrentio] Verifying IMDB ID ${imdbId} for "${title}" with Trakt.tv`);

        // Search Trakt.tv for the show
        const traktSearchUrl = `https://api.trakt.tv/search/show?query=${encodeURIComponent(title)}`;

        const response = await fetch(traktSearchUrl, {
            headers: {
                'Content-Type': 'application/json',
                'trakt-api-key': '01d887218070ff644527e1fb9524ce51b096fd15faef26fd6c6f60ef7dac7978',
                'trakt-api-version': '2'
            }
        });

        if (!response.ok) {
            console.error(`[Torrentio] Trakt API Error: ${response.status}`);
            return null;
        }

        const data = await response.json();

        if (!data || data.length === 0) {
            console.log(`[Torrentio] No results found on Trakt.tv for "${title}"`);
            return null;
        }

        // Find the best match
        const bestMatch = data.find(item =>
            item.show.title.toLowerCase() === title.toLowerCase() ||
            item.show.ids.imdb === imdbId
        ) || data[0];

        const traktImdbId = bestMatch.show.ids.imdb;

        if (traktImdbId && traktImdbId !== imdbId) {
            console.log(`[Torrentio] IMDB ID mismatch: OMDB=${imdbId}, Trakt=${traktImdbId} for "${title}"`);
            return traktImdbId;
        }

        return imdbId; // Return original if no better match found
    } catch (error) {
        console.error('[Torrentio] Error verifying IMDB ID with Trakt:', error);
        return null;
    }
}

/**
 * Search for movies and TV shows using OMDB API
 * @param {string} query Search query
 * @returns {Promise<Array>} Search results from OMDB
 */
async function searchOMDB(query) {
    try {
        const cleanQuery = encodeURIComponent(query.trim());
        const searchUrl = `http://www.omdbapi.com/?apikey=${OMDB_API_KEY}&s=${cleanQuery}`;

        console.log(`[Torrentio] Searching OMDB with: ${cleanQuery}`);

        const response = await fetch(searchUrl);

        if (!response.ok) {
            throw new Error(`OMDB API Error: ${response.status}`);
        }

        const data = await response.json();

        if (data.Response === 'False') {
            console.log(`[Torrentio] OMDB found no results: ${data.Error}`);
            return [];
        }

        // For TV shows, verify IMDB IDs with Trakt.tv
        const results = data.Search || [];
        const verifiedResults = [];

        for (const result of results) {
            if (result.Type === 'series') {
                // Try to verify the IMDB ID with Trakt.tv
                const verifiedImdbId = await verifyImdbIdWithTrakt(result.Title, result.imdbID);

                if (verifiedImdbId && verifiedImdbId !== result.imdbID) {
                    console.log(`[Torrentio] Using verified IMDB ID ${verifiedImdbId} for "${result.Title}" instead of ${result.imdbID}`);
                    verifiedResults.push({
                        ...result,
                        imdbID: verifiedImdbId,
                        _originalImdbID: result.imdbID // Keep the original for reference
                    });
                } else {
                    verifiedResults.push(result);
                }
            } else {
                verifiedResults.push(result);
            }
        }

        return verifiedResults;
    } catch (error) {
        console.error('[Torrentio] Error searching OMDB:', error);
        return [];
    }
}

/**
 * Fetch TV show seasons and episodes from Trakt.tv
 * @param {string} title TV show title
 * @param {string} imdbId IMDB ID of the TV show
 * @returns {Promise<Array>} Array of seasons with episodes
 */
async function getTraktTVShowData(title, imdbId) {
    try {
        console.log(`[Torrentio] Fetching TV show data from Trakt.tv for ${title} (${imdbId})`);

        // First, search for the show to get the Trakt ID or slug
        const searchUrl = `https://api.trakt.tv/search/show?query=${encodeURIComponent(title)}`;

        const searchResponse = await fetch(searchUrl, {
            headers: {
                'Content-Type': 'application/json',
                'trakt-api-key': '01d887218070ff644527e1fb9524ce51b096fd15faef26fd6c6f60ef7dac7978',
                'trakt-api-version': '2'
            }
        });

        if (!searchResponse.ok) {
            console.error(`[Torrentio] Trakt API Error: ${searchResponse.status}`);
            return [];
        }

        const searchData = await searchResponse.json();

        if (!searchData || searchData.length === 0) {
            console.log(`[Torrentio] No results found on Trakt.tv for "${title}"`);
            return [];
        }

        // Find the best match
        const bestMatch = searchData.find(item =>
            item.show.title.toLowerCase() === title.toLowerCase() ||
            item.show.ids.imdb === imdbId
        ) || searchData[0];

        const showSlug = bestMatch.show.ids.slug;

        // Now get the seasons with episodes
        const seasonsUrl = `https://api.trakt.tv/shows/${showSlug}/seasons?extended=episodes`;

        const seasonsResponse = await fetch(seasonsUrl, {
            headers: {
                'Content-Type': 'application/json',
                'trakt-api-key': '01d887218070ff644527e1fb9524ce51b096fd15faef26fd6c6f60ef7dac7978',
                'trakt-api-version': '2'
            }
        });

        if (!seasonsResponse.ok) {
            console.error(`[Torrentio] Trakt API Error: ${seasonsResponse.status}`);
            return [];
        }

        const seasonsData = await seasonsResponse.json();

        console.log(`[Torrentio] Found ${seasonsData.length} seasons on Trakt.tv for "${title}"`);

        return seasonsData;
    } catch (error) {
        console.error('[Torrentio] Error fetching TV show data from Trakt.tv:', error);
        return [];
    }
}

/**
 * Create synthetic episode entries from Trakt.tv data
 * @param {Object} omdbItem OMDB item
 * @param {Array} traktSeasons Seasons data from Trakt.tv
 * @returns {Array} Processed streams with synthetic episode entries
 */
function createSyntheticEpisodes(omdbItem, traktSeasons) {
    const processedStreams = [];

    // Create a base TV show entry for search results
    const baseTVShow = {
        title: omdbItem.Title,
        year: omdbItem.Year,
        imdbID: omdbItem.imdbID,
        poster: omdbItem.Poster !== 'N/A' ? omdbItem.Poster : null,
        type: 'tvshow',
        infoHash: null,
        fileIdx: 0,
        quality: '',
        size: '',
        seeders: null,
        languages: [],
        filename: '',
        episodeInfo: null,
        magnetLink: '',
        source: 'Trakt.tv',
        originalTitle: omdbItem.Title,
        isBaseTVShow: true,
        isSynthetic: true
    };

    processedStreams.push(baseTVShow);

    console.log(`[Torrentio] Creating synthetic episodes for ${omdbItem.Title} from Trakt.tv data`);

    // Process each season and its episodes
    for (const season of traktSeasons) {
        if (!season.episodes || season.episodes.length === 0) continue;

        for (const episode of season.episodes) {
            const episodeInfo = {
                season: episode.season,
                episode: episode.number
            };

            const episodeKey = `S${episodeInfo.season}E${episodeInfo.episode}`;

            // Create a synthetic episode entry
            const syntheticEpisode = {
                title: omdbItem.Title,
                year: omdbItem.Year,
                imdbID: omdbItem.imdbID,
                poster: omdbItem.Poster !== 'N/A' ? omdbItem.Poster : null,
                type: 'tvshow',
                infoHash: null,
                fileIdx: 0,
                quality: '',
                size: '',
                seeders: null,
                languages: [],
                filename: '',
                episodeInfo: episodeInfo,
                magnetLink: '',
                source: 'Trakt.tv',
                originalTitle: `${omdbItem.Title} ${episodeKey}`,
                episodeTitle: episode.title || `Episode ${episode.number}`,
                isEpisode: true,
                episodeKey: episodeKey,
                isSynthetic: true,
                episodeImdbID: episode.ids?.imdb || null
            };

            processedStreams.push(syntheticEpisode);
        }
    }

    console.log(`[Torrentio] Created ${processedStreams.length - 1} synthetic episodes for ${omdbItem.Title}`);

    return processedStreams;
}

// Function removed to fix duplicate declaration

/**
 * Create episode entries from Trakt.tv data
 * @param {Object} omdbItem OMDB item
 * @param {Array} traktSeasons Seasons data from Trakt.tv
 * @returns {Array} Processed streams with episode entries
 */
function createEpisodesFromTraktData(omdbItem, traktSeasons) {
    const processedStreams = [];

    // Create a base TV show entry for search results
    const baseTVShow = {
        title: omdbItem.Title,
        year: omdbItem.Year,
        imdbID: omdbItem.imdbID,
        poster: omdbItem.Poster !== 'N/A' ? omdbItem.Poster : null,
        type: 'tvshow',
        infoHash: null,
        fileIdx: 0,
        quality: '',
        size: '',
        seeders: null,
        languages: [],
        filename: '',
        episodeInfo: null,
        magnetLink: '',
        source: 'Torrentio',
        originalTitle: omdbItem.Title,
        isBaseTVShow: true
    };

    processedStreams.push(baseTVShow);

    console.log(`[Torrentio] Creating episode entries for ${omdbItem.Title} from Trakt.tv data`);

    // Process each season and its episodes
    for (const season of traktSeasons) {
        // Skip season 0 (specials) for now
        if (season.number === 0) continue;

        if (!season.episodes || season.episodes.length === 0) continue;

        for (const episode of season.episodes) {
            const episodeInfo = {
                season: episode.season,
                episode: episode.number
            };

            const episodeKey = `S${episodeInfo.season}E${episodeInfo.episode}`;

            // Create an episode entry
            const episodeEntry = {
                title: omdbItem.Title,
                year: omdbItem.Year,
                imdbID: omdbItem.imdbID,
                poster: omdbItem.Poster !== 'N/A' ? omdbItem.Poster : null,
                type: 'tvshow',
                infoHash: null,
                fileIdx: 0,
                quality: '',
                size: '',
                seeders: 1, // Default value for sorting
                languages: [],
                filename: '',
                episodeInfo: episodeInfo,
                magnetLink: '',
                source: 'Torrentio',
                originalTitle: `${omdbItem.Title} ${episodeKey}`,
                episodeTitle: episode.title || `Episode ${episode.number}`,
                isEpisode: true,
                episodeKey: episodeKey,
                needsSpecificUrl: true, // Flag to indicate this needs a specific URL format
                episodeImdbID: episode.ids?.imdb || null
            };

            processedStreams.push(episodeEntry);
        }
    }

    console.log(`[Torrentio] Created ${processedStreams.length - 1} episode entries for ${omdbItem.Title}`);

    return processedStreams;
}

/**
 * Get streams from Torrentio for a specific IMDB item
 * @param {Object} omdbItem Item from OMDB search results
 * @param {number} season Optional season number for TV shows
 * @param {number} episode Optional episode number for TV shows
 * @returns {Promise<Array>} Processed Torrentio streams
 */
export async function getTorrentioStreams(omdbItem, season = null, episode = null) {
    try {
        const imdbId = omdbItem.imdbID;
        const type = omdbItem.Type === 'series' ? 'series' : 'movie';
        const sort = 'size';

        // Build the URL path based on whether we're looking for a specific episode
        let urlPath = imdbId;
        const specificEpisode = type === 'series' && season !== null && episode !== null;

        if (specificEpisode) {
            // For specific episodes, use the direct episode URL format
            urlPath = `${imdbId}:${season}:${episode}`;
            console.log(`[Torrentio] Looking for specific episode: S${season}E${episode}`);
        }

        // Format URL according to actual Torrentio API format
        const url = `${TORRENTIO_API_BASE}/sort=${sort}/stream/${type}/${urlPath}.json`;

        console.log(`[Torrentio] Fetching streams for ${type} ${imdbId} (${omdbItem.Title})`);
        console.log(`[Torrentio] URL: ${url}`);

        const response = await fetch(url);

        if (!response.ok) {
            console.error(`[Torrentio] API Error for ${imdbId}: ${response.status}`);
            return [];
        }

        const data = await response.json();
        let streams = data.streams || [];

        console.log(`[Torrentio] Found ${streams.length} streams for ${imdbId}`);

        // If we have streams, process them normally
        if (streams.length > 0) {
            return processStreams(streams, omdbItem, season, episode);
        }

        // If we're looking for a TV show and no streams were found, use Trakt.tv to get the episode list
        if (type === 'series' && !specificEpisode) {
            console.log(`[Torrentio] No streams found for ${omdbItem.Title}, getting episode list from Trakt.tv`);

            // Get TV show data from Trakt.tv
            const traktSeasons = await getTraktTVShowData(omdbItem.Title, imdbId);

            if (traktSeasons.length > 0) {
                // Create episode entries from Trakt.tv data
                return createEpisodesFromTraktData(omdbItem, traktSeasons);
            }
        }

        // If we're looking for a specific episode and no streams were found, try to get the episode from Trakt.tv
        if (specificEpisode) {
            console.log(`[Torrentio] No streams found for ${omdbItem.Title} S${season}E${episode}, trying Trakt.tv fallback`);

            // Get TV show data from Trakt.tv
            const traktSeasons = await getTraktTVShowData(omdbItem.Title, imdbId);

            if (traktSeasons.length > 0) {
                // Find the specific episode
                const targetSeason = traktSeasons.find(s => s.number === season);

                if (targetSeason && targetSeason.episodes) {
                    const targetEpisode = targetSeason.episodes.find(e => e.number === episode);

                    if (targetEpisode) {
                        console.log(`[Torrentio] Found episode on Trakt.tv: ${targetEpisode.title}`);

                        // Try to get streams for this specific episode using its IMDB ID
                        if (targetEpisode.ids && targetEpisode.ids.imdb) {
                            console.log(`[Torrentio] Trying to get streams for episode IMDB ID: ${targetEpisode.ids.imdb}`);

                            const episodeUrl = `${TORRENTIO_API_BASE}/sort=${sort}/stream/series/${targetEpisode.ids.imdb}.json`;

                            try {
                                const episodeResponse = await fetch(episodeUrl);

                                if (episodeResponse.ok) {
                                    const episodeData = await episodeResponse.json();
                                    const episodeStreams = episodeData.streams || [];

                                    if (episodeStreams.length > 0) {
                                        console.log(`[Torrentio] Found ${episodeStreams.length} streams for episode IMDB ID: ${targetEpisode.ids.imdb}`);

                                        // Create a modified OMDB item with the episode's IMDB ID
                                        const episodeOmdbItem = {
                                            ...omdbItem,
                                            imdbID: targetEpisode.ids.imdb,
                                            Title: `${omdbItem.Title} S${season}E${episode} - ${targetEpisode.title}`
                                        };

                                        return processStreams(episodeStreams, episodeOmdbItem, season, episode);
                                    }
                                }
                            } catch (episodeError) {
                                console.error(`[Torrentio] Error fetching episode streams: ${episodeError}`);
                            }
                        }
                    }
                }
            }
        }

        // If all fallbacks fail, return empty array
        return [];
    } catch (error) {
        console.error(`[Torrentio] Error getting Torrentio streams for ${omdbItem.imdbID}:`, error);
        return [];
    }
}

/**
 * Process streams from Torrentio into our format
 * @param {Array} streams Array of streams from Torrentio
 * @param {Object} omdbItem Item from OMDB search results
 * @param {number} season Optional season number for TV shows
 * @param {number} episode Optional episode number for TV shows
 * @returns {Array} Processed streams
 */
function processStreams(streams, omdbItem, season = null, episode = null) {
    const processedStreams = [];
    const isTV = omdbItem.Type === 'series';

    // If we're looking for a specific episode, we'll return all quality options
    if (isTV && season !== null && episode !== null) {
        // Process all streams for the specific episode
        const episodeStreams = [];

        for (const stream of streams) {
            try {
                // Extract data from the stream
                const { infoHash, fileIdx, name, title, behaviorHints } = stream;

                // Skip if missing essential data
                if (!infoHash) continue;

                // Get filename from behaviorHints if available
                const filename = behaviorHints?.filename || '';

                // Create episode info object
                const episodeInfo = {
                    season: season,
                    episode: episode
                };

                // Extract quality, size, and seeders from the title
                const { quality, size, seeders, languages } = extractMetadataFromTitle(title);

                const result = {
                    title: omdbItem.Title,
                    year: omdbItem.Year,
                    imdbID: omdbItem.imdbID,
                    poster: omdbItem.Poster !== 'N/A' ? omdbItem.Poster : null,
                    type: 'tvshow',
                    infoHash,
                    fileIdx: fileIdx || 0,
                    quality,
                    size,
                    seeders,
                    languages,
                    filename,
                    episodeInfo,
                    magnetLink: createMagnetLink(infoHash, omdbItem.Title),
                    source: 'Torrentio',
                    originalTitle: title,
                    isEpisodeStream: true
                };

                episodeStreams.push(result);
            } catch (error) {
                console.error('[Torrentio] Error processing stream:', error);
                continue;
            }
        }

        // Sort by seeders (highest first)
        episodeStreams.sort((a, b) => b.seeders - a.seeders);

        return episodeStreams;
    }

    // If we're looking for all episodes of a TV show
    if (isTV && !season && !episode) {
        // Create a base TV show entry for search results
        const baseTVShow = {
            title: omdbItem.Title,
            year: omdbItem.Year,
            imdbID: omdbItem.imdbID,
            poster: omdbItem.Poster !== 'N/A' ? omdbItem.Poster : null,
            type: 'tvshow',
            infoHash: null,
            fileIdx: 0,
            quality: '',  // Empty instead of 'Various'
            size: '',     // Empty instead of 'Various'
            seeders: null, // null instead of 0
            languages: [],
            filename: '',
            episodeInfo: null,
            magnetLink: '',
            source: 'Torrentio',
            originalTitle: omdbItem.Title,
            isBaseTVShow: true
        };

        // Add the base TV show to processed streams
        processedStreams.push(baseTVShow);

        console.log(`[Torrentio] Processing ${streams.length} streams for TV show: ${omdbItem.Title} (${omdbItem.imdbID})`);

        // Track unique episodes to avoid duplicates
        const uniqueEpisodes = new Map();

        for (const stream of streams) {
            try {
                // Extract data from the stream
                const { infoHash, fileIdx, name, title, behaviorHints } = stream;

                // Skip if missing essential data
                if (!infoHash) {
                    console.log(`[Torrentio] Skipping stream with missing infoHash: ${title}`);
                    continue;
                }

                // Get filename from behaviorHints if available
                const filename = behaviorHints?.filename || '';

                console.log(`[Torrentio] Processing stream: ${title}`);
                console.log(`[Torrentio] Filename: ${filename}`);

                // Extract episode info from title or filename
                let extractedEpisodeInfo = extractEpisodeInfo(title);

                if (!extractedEpisodeInfo && filename) {
                    extractedEpisodeInfo = extractEpisodeInfo(filename);
                    console.log(`[Torrentio] Extracted episode info from filename: ${JSON.stringify(extractedEpisodeInfo)}`);
                } else if (extractedEpisodeInfo) {
                    console.log(`[Torrentio] Extracted episode info from title: ${JSON.stringify(extractedEpisodeInfo)}`);
                }

                // If still no episode info, try to extract from the title using more aggressive patterns
                if (!extractedEpisodeInfo) {
                    // Try to find any numbers that might be season/episode
                    const numberMatches = title.match(/\b(\d{1,2})[\s.]*(\d{1,2})\b/);
                    if (numberMatches) {
                        extractedEpisodeInfo = {
                            season: parseInt(numberMatches[1], 10),
                            episode: parseInt(numberMatches[2], 10)
                        };
                        console.log(`[Torrentio] Extracted episode info using fallback pattern: ${JSON.stringify(extractedEpisodeInfo)}`);
                    }

                    // Look for episode numbers in the filename
                    if (!extractedEpisodeInfo && filename) {
                        const episodeMatch = filename.match(/E(\d{1,2})/i);
                        if (episodeMatch) {
                            // If we have just an episode number, assume season 1
                            extractedEpisodeInfo = {
                                season: 1,
                                episode: parseInt(episodeMatch[1], 10)
                            };
                            console.log(`[Torrentio] Extracted episode info using episode-only pattern: ${JSON.stringify(extractedEpisodeInfo)}`);
                        }
                    }
                }

                // Skip if no episode info
                if (!extractedEpisodeInfo) {
                    console.log(`[Torrentio] No episode info found for: ${title}`);
                    continue;
                }

                // Extract quality, size, and seeders from the title
                const { quality, size, seeders, languages } = extractMetadataFromTitle(title);

                const episodeKey = `S${extractedEpisodeInfo.season}E${extractedEpisodeInfo.episode}`;
                console.log(`[Torrentio] Created episode key: ${episodeKey}`);

                // For episodes, we'll create a simplified entry that just shows season/episode info
                // We'll use this for the episode list view
                const result = {
                    title: omdbItem.Title,
                    year: omdbItem.Year,
                    imdbID: omdbItem.imdbID,
                    poster: omdbItem.Poster !== 'N/A' ? omdbItem.Poster : null,
                    type: 'tvshow',
                    infoHash: infoHash, // Include infoHash for reference
                    fileIdx: fileIdx || 0,
                    quality: quality,
                    size: size,
                    seeders: seeders,
                    languages: languages,
                    filename: filename,
                    episodeInfo: extractedEpisodeInfo,
                    magnetLink: createMagnetLink(infoHash, `${omdbItem.Title} ${episodeKey}`),
                    source: 'Torrentio',
                    originalTitle: `${omdbItem.Title} ${episodeKey}`,
                    isEpisode: true, // This flag is critical for the UI to recognize it as an episode
                    episodeKey: episodeKey // Add this to help with grouping
                };

                // Only add if we don't already have this episode or if this one has more seeders
                if (!uniqueEpisodes.has(episodeKey) || uniqueEpisodes.get(episodeKey).seeders < seeders) {
                    console.log(`[Torrentio] Adding episode: ${episodeKey} with ${seeders} seeders`);
                    uniqueEpisodes.set(episodeKey, result);
                }
            } catch (error) {
                console.error('[Torrentio] Error processing stream for episode list:', error);
                continue;
            }
        }

        // Add all unique episodes to the processed streams
        uniqueEpisodes.forEach(episode => {
            processedStreams.push(episode);
        });

        console.log(`[Torrentio] Found ${uniqueEpisodes.size} unique episodes for ${omdbItem.Title}`);

        // Sort episodes by season and episode (ascending)
        processedStreams.sort((a, b) => {
            if (!a.episodeInfo || !b.episodeInfo) return 0;

            // Sort by season first
            if (a.episodeInfo.season !== b.episodeInfo.season) {
                return a.episodeInfo.season - b.episodeInfo.season;
            }

            // Then by episode
            return a.episodeInfo.episode - b.episodeInfo.episode;
        });

        return processedStreams;
    }

    // For movies, group by quality
    if (!isTV) {
        const groupedByQuality = {};

        for (const stream of streams) {
            try {
                // Extract data from the stream
                const { infoHash, fileIdx, name, title, behaviorHints } = stream;

                // Skip if missing essential data
                if (!infoHash) continue;

                // Extract quality, size, and seeders from the title
                const { quality, size, seeders, languages } = extractMetadataFromTitle(title);

                // Get filename from behaviorHints if available
                const filename = behaviorHints?.filename || '';

                const result = {
                    title: omdbItem.Title,
                    year: omdbItem.Year,
                    imdbID: omdbItem.imdbID,
                    poster: omdbItem.Poster !== 'N/A' ? omdbItem.Poster : null,
                    type: 'movie',
                    infoHash,
                    fileIdx: fileIdx || 0,
                    quality,
                    size,
                    seeders,
                    languages,
                    filename,
                    episodeInfo: null,
                    magnetLink: createMagnetLink(infoHash, omdbItem.Title),
                    source: 'Torrentio',
                    originalTitle: title
                };

                // Group by quality, keeping the one with most seeders
                const qualityKey = quality.toLowerCase();
                if (!groupedByQuality[qualityKey] || seeders > groupedByQuality[qualityKey].seeders) {
                    groupedByQuality[qualityKey] = result;
                }
            } catch (error) {
                console.error('[Torrentio] Error processing stream:', error);
                continue;
            }
        }

        // Add all movie qualities to processed streams
        return Object.values(groupedByQuality);
    }

    return processedStreams;
}

/**
 * Extract metadata from the Torrentio stream title
 * @param {string} title Stream title
 * @returns {Object} Extracted metadata
 */
function extractMetadataFromTitle(title) {
    let quality = 'Unknown';
    let size = 'Unknown';
    let seeders = 0;
    let languages = [];

    if (!title) {
        return { quality, size, seeders, languages };
    }

    // Extract quality - look for common quality indicators
    if (title.includes('2160p') || title.includes('4K') || title.includes('4k') || title.includes('UHD')) {
        quality = '4K';
    } else if (title.includes('1080p') || title.includes('FHD')) {
        quality = '1080p';
    } else if (title.includes('720p') || title.includes('HD')) {
        quality = '720p';
    } else if (title.includes('480p') || title.includes('SD')) {
        quality = '480p';
    }

    // Check for HDR/DV - order matters here for proper display
    // First check for both HDR and DV
    if ((title.includes('HDR') || title.includes('HDR10')) &&
        (title.includes('DV') || title.includes('Dolby Vision') || title.includes('DoVi'))) {
        quality += ' HDR DV';
    }
    // Then check for HDR only
    else if (title.includes('HDR') || title.includes('HDR10')) {
        quality += ' HDR';
    }
    // Then check for DV only
    else if (title.includes('DV') || title.includes('Dolby Vision') || title.includes('DoVi')) {
        quality += ' DV';
    }

    // Extract size - look for GB or MB patterns
    const sizeMatch = title.match(/💾\s*([0-9.]+\s*[GM]B)/i);
    if (sizeMatch) {
        size = sizeMatch[1];
    } else {
        // Alternative size pattern
        const altSizeMatch = title.match(/\b(\d+(?:\.\d+)?\s*[GM]B)\b/i);
        if (altSizeMatch) {
            size = altSizeMatch[1];
        }
    }

    // Extract seeders - look for the seeders emoji or text
    const seedersMatch = title.match(/👤\s*(\d+)/);
    if (seedersMatch) {
        seeders = parseInt(seedersMatch[1], 10);
    } else {
        // Alternative seeders pattern
        const altSeedersMatch = title.match(/\bSeeders:?\s*(\d+)\b/i);
        if (altSeedersMatch) {
            seeders = parseInt(altSeedersMatch[1], 10);
        }
    }

    // Extract languages - look for language flags or codes
    const languageFlags = {
        '🇬🇧': 'English',
        '🇺🇸': 'English',
        '🇪🇸': 'Spanish',
        '🇷🇺': 'Russian',
        '🇺🇦': 'Ukrainian',
        '🇫🇷': 'French',
        '🇩🇪': 'German',
        '🇮🇹': 'Italian',
        '🇯🇵': 'Japanese',
        '🇰🇷': 'Korean',
        '🇨🇳': 'Chinese',
        '🇵🇱': 'Polish'
    };

    // Check for language flags
    Object.entries(languageFlags).forEach(([flag, language]) => {
        if (title.includes(flag)) {
            languages.push(language);
        }
    });

    // Check for "Multi" or "Dual Audio"
    if (title.includes('Multi Audio') || title.includes('Dual Audio')) {
        if (!languages.includes('Multi Audio')) {
            languages.push('Multi Audio');
        }
    }

    return { quality, size, seeders, languages };
}

/**
 * Create a magnet link from info hash and title
 * @param {string} infoHash Torrent info hash
 * @param {string} title Title for the magnet link
 * @returns {string} Magnet link
 */
function createMagnetLink(infoHash, title) {
    const encodedTitle = encodeURIComponent(title);
    return `magnet:?xt=urn:btih:${infoHash}&dn=${encodedTitle}&tr=udp%3A%2F%2Ftracker.opentrackr.org%3A1337%2Fannounce&tr=udp%3A%2F%2Fexodus.desync.com%3A6969%2Fannounce&tr=udp%3A%2F%2Ftracker.torrent.eu.org%3A451%2Fannounce`;
}

/**
 * Detect if the media is a movie or TV show
 * @param {string} title Media title
 * @returns {string} Media type ('movie' or 'tvshow')
 */
function detectMediaType(title) {
    // Check for common TV show patterns like S01E01, Season 1, etc.
    const tvShowPatterns = [
        /\bS\d{1,2}E\d{1,2}\b/i,  // S01E01 format
        /\bSeason\s+\d+\b/i,      // "Season 1" format
        /\bEpisode\s+\d+\b/i,     // "Episode 1" format
        /\b\d{1,2}x\d{1,2}\b/     // 1x01 format
    ];

    for (const pattern of tvShowPatterns) {
        if (pattern.test(title)) {
            return 'tvshow';
        }
    }

    return 'movie';
}

/**
 * Extract season and episode information from a string
 * @param {string} text Text to extract from
 * @returns {Object|null} Season and episode information or null if not found
 */
function extractEpisodeInfo(text) {
    if (!text) return null;

    console.log(`[Torrentio] Extracting episode info from: ${text}`);

    // Try different patterns for extracting season and episode

    // S01E01 format (with or without leading zeros)
    const sXeXPattern = /S(\d{1,2})E(\d{1,2})/i;
    const sXeXMatch = text.match(sXeXPattern);
    if (sXeXMatch) {
        const season = parseInt(sXeXMatch[1], 10);
        const episode = parseInt(sXeXMatch[2], 10);
        console.log(`[Torrentio] Found S${season}E${episode} pattern`);
        return { season, episode };
    }

    // Alternative S3E3 format (without leading zeros, with possible spaces)
    const altSXeXPattern = /[Ss]\s*(\d{1,2})\s*[Ee]\s*(\d{1,2})/i;
    const altSXeXMatch = text.match(altSXeXPattern);
    if (altSXeXMatch) {
        const season = parseInt(altSXeXMatch[1], 10);
        const episode = parseInt(altSXeXMatch[2], 10);
        console.log(`[Torrentio] Found S${season}E${episode} alternative pattern`);
        return { season, episode };
    }

    // 1x01 format
    const xPattern = /(\d{1,2})x(\d{1,2})/i;
    const xMatch = text.match(xPattern);
    if (xMatch) {
        const season = parseInt(xMatch[1], 10);
        const episode = parseInt(xMatch[2], 10);
        console.log(`[Torrentio] Found ${season}x${episode} pattern`);
        return { season, episode };
    }

    // "Season 1 Episode 1" format
    const seasonEpisodePattern = /Season\s+(\d+).*?Episode\s+(\d+)/i;
    const seasonEpisodeMatch = text.match(seasonEpisodePattern);
    if (seasonEpisodeMatch) {
        const season = parseInt(seasonEpisodeMatch[1], 10);
        const episode = parseInt(seasonEpisodeMatch[2], 10);
        console.log(`[Torrentio] Found Season ${season} Episode ${episode} pattern`);
        return { season, episode };
    }

    // "S3 E3" format (with space between S and E)
    const spacedSEPattern = /S\s*(\d{1,2})\s+E\s*(\d{1,2})/i;
    const spacedSEMatch = text.match(spacedSEPattern);
    if (spacedSEMatch) {
        const season = parseInt(spacedSEMatch[1], 10);
        const episode = parseInt(spacedSEMatch[2], 10);
        console.log(`[Torrentio] Found S${season} E${episode} pattern`);
        return { season, episode };
    }

    // "Сезон: 3 / Серии: 1-8" format (Russian/Ukrainian)
    const russianPattern = /Сезон:\s*(\d+).*?Серии?:.*?(\d+)/i;
    const russianMatch = text.match(russianPattern);
    if (russianMatch) {
        const season = parseInt(russianMatch[1], 10);
        const episode = parseInt(russianMatch[2], 10);
        console.log(`[Torrentio] Found Russian pattern: Season ${season} Episode ${episode}`);
        return { season, episode };
    }

    // Look for patterns like "S03E03" in filenames
    const filenamePattern = /\.S(\d{1,2})E(\d{1,2})\./i;
    const filenameMatch = text.match(filenamePattern);
    if (filenameMatch) {
        const season = parseInt(filenameMatch[1], 10);
        const episode = parseInt(filenameMatch[2], 10);
        console.log(`[Torrentio] Found filename pattern: S${season}E${episode}`);
        return { season, episode };
    }

    // Look for patterns like "S03.E03" in filenames
    const dotPattern = /\.S(\d{1,2})\.E(\d{1,2})\./i;
    const dotMatch = text.match(dotPattern);
    if (dotMatch) {
        const season = parseInt(dotMatch[1], 10);
        const episode = parseInt(dotMatch[2], 10);
        console.log(`[Torrentio] Found dot pattern: S${season}.E${episode}`);
        return { season, episode };
    }

    // Look for patterns like "3x03" in filenames
    const filenameXPattern = /\.(\d{1,2})x(\d{1,2})\./i;
    const filenameXMatch = text.match(filenameXPattern);
    if (filenameXMatch) {
        const season = parseInt(filenameXMatch[1], 10);
        const episode = parseInt(filenameXMatch[2], 10);
        console.log(`[Torrentio] Found filename x pattern: ${season}x${episode}`);
        return { season, episode };
    }

    // Look for patterns like "E03" at the end of the filename (assuming season from context)
    const episodeOnlyPattern = /E(\d{1,2})(?:\.|$)/i;
    const episodeOnlyMatch = text.match(episodeOnlyPattern);
    if (episodeOnlyMatch) {
        // We'll use the provided season or default to 1
        const episode = parseInt(episodeOnlyMatch[1], 10);
        console.log(`[Torrentio] Found episode-only pattern: E${episode}`);
        return { season: 1, episode };
    }

    // Look for patterns like "Cap.101" (Spanish format where first digit is season, rest is episode)
    const spanishPattern = /Cap\.(\d)(\d{2})/i;
    const spanishMatch = text.match(spanishPattern);
    if (spanishMatch) {
        const season = parseInt(spanishMatch[1], 10);
        const episode = parseInt(spanishMatch[2], 10);
        console.log(`[Torrentio] Found Spanish pattern: S${season}E${episode}`);
        return { season, episode };
    }

    // Look for patterns like "S01" in the filename and assume episode 1 if not specified
    const seasonOnlyPattern = /\.S(\d{1,2})\./i;
    const seasonOnlyMatch = text.match(seasonOnlyPattern);
    if (seasonOnlyMatch) {
        const season = parseInt(seasonOnlyMatch[1], 10);
        console.log(`[Torrentio] Found season-only pattern: S${season}, assuming E1`);
        return { season, episode: 1 };
    }

    // Look for patterns like "01" at the beginning of the filename (assuming it's episode 1 of season 1)
    const numberOnlyPattern = /^(\d{1,2})/;
    const numberOnlyMatch = text.match(numberOnlyPattern);
    if (numberOnlyMatch) {
        const episode = parseInt(numberOnlyMatch[1], 10);
        console.log(`[Torrentio] Found number-only pattern at start: ${episode}, assuming S1`);
        return { season: 1, episode };
    }

    // Look for patterns like "Born.Again.S01E01" where dots separate words
    const dottedPattern = /\.S(\d{1,2})E(\d{1,2})/i;
    const dottedMatch = text.match(dottedPattern);
    if (dottedMatch) {
        const season = parseInt(dottedMatch[1], 10);
        const episode = parseInt(dottedMatch[2], 10);
        console.log(`[Torrentio] Found dotted pattern: S${season}E${episode}`);
        return { season, episode };
    }

    console.log(`[Torrentio] No episode pattern found in: ${text}`);
    return null;
}

/**
 * Get detailed information for a specific media item
 * @param {Object} mediaItem Media item object
 * @returns {Promise<Object>} Enhanced media item with additional information
 */
export async function getMediaDetails(mediaItem) {
    try {
        // Add poster from OMDB if available
        const poster = await getMoviePosterByTitle(mediaItem.title);

        return {
            ...mediaItem,
            poster: poster || null
        };
    } catch (error) {
        console.error('[Torrentio] Error getting media details:', error);
        return mediaItem;
    }
}
