import fs from 'fs/promises';
import path from 'path';
import { searchGamesForDiscord, processGameLinks, formatLinksForDiscord } from './discord-helper.js';
import { DEFAULT_DISCORD_CONFIG } from '$lib/config/serverConfig.js';

// <PERSON><PERSON><PERSON><PERSON> try-catch, aby o<PERSON><PERSON><PERSON><PERSON>, gdy discord.js nie jest dostępny podczas budowania
let Client, GatewayIntentBits, Events, REST, Routes, SlashCommandBuilder, Collection, ActivityType;
let ApplicationCommandOptionType, ChannelType;
try {
    // Próbujemy zaimportowa<PERSON> discord.js
    const discord = await import('discord.js');
    Client = discord.Client;
    GatewayIntentBits = discord.GatewayIntentBits;
    Events = discord.Events;
    REST = discord.REST;
    Routes = discord.Routes;
    SlashCommandBuilder = discord.SlashCommandBuilder;
    Collection = discord.Collection;
    ActivityType = discord.ActivityType;
    ApplicationCommandOptionType = discord.ApplicationCommandOptionType;
    ChannelType = discord.ChannelType;
} catch (error) {
    console.warn('Could not import discord.js, using mock instead:', error);
    // Używamy mocka podczas budowania
    const mock = await import('./discord-bot-mock.js');
    Client = mock.Client;
    GatewayIntentBits = mock.GatewayIntentBits;
    Events = mock.Events;
    REST = mock.REST;
    Routes = mock.Routes;
    SlashCommandBuilder = mock.SlashCommandBuilder;
    Collection = mock.Collection;
    ActivityType = mock.ActivityType;
    ApplicationCommandOptionType = mock.ApplicationCommandOptionType;
    ChannelType = mock.ChannelType;
}

// Path to Discord bot configuration
const CONFIG_PATH = path.join(process.cwd(), '.config', 'discord', 'config.json');

// Bot instance
let botInstance = null;
let isInitialized = false;

// Load Discord bot configuration
async function loadConfig() {
    try {
        try {
            const configData = await fs.readFile(CONFIG_PATH, 'utf-8');
            return JSON.parse(configData);
        } catch (error) {
            console.log('Discord bot config file not found, using environment variables');
            return DEFAULT_DISCORD_CONFIG;
        }
    } catch (error) {
        console.error('Error loading Discord bot config:', error);
        return DEFAULT_DISCORD_CONFIG;
    }
}

// Initialize Discord bot
export async function initializeBot() {
    try {
        // Load config from file or environment variables
        const config = await loadConfig();

        console.log('Discord bot config loaded:', JSON.stringify({
            enabled: config.enabled,
            hasApiKey: !!config.apiKey,
            hasChannelId: !!config.channelId,
            hasClientId: !!config.clientId,
            hasClientSecret: !!config.clientSecret
        }));

        if (!config.enabled) {
            console.log('Discord bot is disabled in config');
            return false;
        }

        if (!config.apiKey) {
            console.log('Discord bot API key is missing');
            return false;
        }

        if (!config.channelId) {
            console.log('Discord bot channel ID is missing');
            return false;
        }

        // Create a new client instance
        console.log('Creating Discord client instance...');
        const client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.DirectMessages // Add intent for DMs
            ]
        });

        // Create a collection for storing commands
        client.commands = new Collection();

        // Define slash commands
        const commands = [
            {
                data: new SlashCommandBuilder()
                    .setName('search')
                    .setDescription('Wyszukaj grę')
                    .addStringOption(option =>
                        option.setName('query')
                            .setDescription('Nazwa gry do wyszukania')
                            .setRequired(true)
                            .setMinLength(3)
                    ),
                async execute(interaction) {
                    const query = interaction.options.getString('query');

                    await interaction.deferReply();

                    try {
                        // Search for games using our helper function
                        const results = await searchGamesForDiscord(query, 5);

                        if (!results || results.length === 0) {
                            await interaction.editReply(`Nie znaleziono wyników dla "${query}"`);
                            return;
                        }

                        // Format results
                        let response = `Znaleziono ${results.length} wyników dla "${query}":\n\n`;

                        results.forEach((game, index) => {
                            response += `**${index + 1}. ${game.title}**\n`;
                            if (game.description) {
                                response += `${game.description.substring(0, 80)}${game.description.length > 80 ? '...' : ''}\n`;
                            }
                            response += `Źródło: ${game.source}\n\n`;
                        });

                        response += 'Użyj komendy `/download <numer>` aby pobrać grę.';

                        // Check if the message is too long for Discord
                        if (response.length > 1900) {
                            response = response.substring(0, 1900) + '...';
                        }

                        await interaction.editReply(response);

                        // Store results in memory for later use
                        client.lastSearchResults = results;
                        client.lastSearchUsers = client.lastSearchUsers || new Map();
                        client.lastSearchUsers.set(interaction.user.id, results);

                    } catch (error) {
                        console.error('Error searching games:', error);
                        await interaction.editReply('Wystąpił błąd podczas wyszukiwania gier.');
                    }
                }
            },
            {
                data: new SlashCommandBuilder()
                    .setName('download')
                    .setDescription('Pobierz grę z wyników wyszukiwania')
                    .addIntegerOption(option =>
                        option.setName('number')
                            .setDescription('Numer gry z wyników wyszukiwania')
                            .setRequired(true)
                            .setMinValue(1)
                            .setMaxValue(10)
                    ),
                async execute(interaction) {
                    const index = interaction.options.getInteger('number') - 1;

                    // Get user-specific search results
                    client.lastSearchUsers = client.lastSearchUsers || new Map();
                    const userResults = client.lastSearchUsers.get(interaction.user.id);

                    if (!userResults || index < 0 || index >= userResults.length) {
                        await interaction.reply('Nieprawidłowy numer gry. Użyj komendy `/search` aby wyszukać gry.');
                        return;
                    }

                    const game = userResults[index];

                    await interaction.deferReply();
                    await interaction.editReply(`Pobieram grę: ${game.title}...`);

                    try {
                        // Check if game has links
                        if (!game.links || game.links.length === 0) {
                            await interaction.editReply(`Nie znaleziono linków do pobrania dla ${game.title}.`);
                            return;
                        }

                        // Process the links
                        await interaction.editReply(`Dodaję linki do Real-Debrid...`);

                        const processedLinks = await processGameLinks(game);

                        // Check if any links are still processing
                        const hasProcessingLinks = processedLinks.some(link => link.status === 'processing');

                        // Format and send the initial links message
                        const linksMessage = formatLinksForDiscord(processedLinks, game.title);
                        await interaction.editReply(linksMessage);

                        // If there are processing links, wait for them to complete and send updates
                        if (hasProcessingLinks) {
                            // Store the original processed links to track changes
                            const originalLinks = [...processedLinks];

                            // Wait for up to 2 minutes, checking every 10 seconds
                            const maxWaitTime = 120000; // 2 minutes
                            const checkInterval = 10000; // 10 seconds
                            const startTime = Date.now();

                            // Create a timeout to periodically check for updates
                            const checkForUpdates = async () => {
                                try {
                                    // Check if we've exceeded the maximum wait time
                                    if (Date.now() - startTime > maxWaitTime) {
                                        console.log('[Discord Bot] Timeout waiting for torrent processing');
                                        return;
                                    }

                                    // Check for processing links that need to be updated
                                    const processingLinks = processedLinks.filter(link => link.status === 'processing');

                                    if (processingLinks.length === 0) {
                                        console.log('[Discord Bot] No more processing links, stopping updates');
                                        return;
                                    }

                                    let hasUpdates = false;

                                    // Check each processing link
                                    for (const link of processingLinks) {
                                        if (link.type === 'magnet' && link.id) {
                                            try {
                                                // Get download links for the torrent
                                                const downloadLinks = await waitForTorrentLinks(link.id, 10000, 2000);

                                                if (downloadLinks && downloadLinks.length > 0) {
                                                    // Remove the processing link
                                                    const linkIndex = processedLinks.findIndex(l =>
                                                        l.type === 'magnet' && l.id === link.id && l.status === 'processing');

                                                    if (linkIndex !== -1) {
                                                        processedLinks.splice(linkIndex, 1);

                                                        // Add the download links
                                                        downloadLinks.forEach(dlLink => {
                                                            processedLinks.push({
                                                                name: dlLink.filename || 'Plik',
                                                                originalUrl: dlLink.url,
                                                                type: 'direct',
                                                                status: 'unrestricted',
                                                                downloadUrl: dlLink.url,
                                                                filename: dlLink.filename,
                                                                filesize: dlLink.filesize
                                                            });
                                                        });

                                                        hasUpdates = true;
                                                    }
                                                }
                                            } catch (error) {
                                                console.error(`[Discord Bot] Error checking torrent status: ${error.message}`);

                                                // Update the link status to indicate it's no longer processing
                                                const linkIndex = processedLinks.findIndex(l =>
                                                    l.type === 'magnet' && l.id === link.id && l.status === 'processing');

                                                if (linkIndex !== -1) {
                                                    processedLinks[linkIndex].status = 'added';
                                                    processedLinks[linkIndex].message = 'Torrent dodany do Real-Debrid. Sprawdź zakładkę "Chlewik" w aplikacji, aby pobrać pliki.';
                                                    hasUpdates = true;
                                                }
                                            }
                                        }
                                    }

                                    // If there are updates, send a new message
                                    if (hasUpdates) {
                                        const updatedLinksMessage = formatLinksForDiscord(processedLinks, game.title);
                                        await interaction.editReply(updatedLinksMessage);
                                    }

                                    // Schedule the next check if there are still processing links
                                    const stillProcessing = processedLinks.some(link => link.status === 'processing');
                                    if (stillProcessing) {
                                        setTimeout(checkForUpdates, checkInterval);
                                    } else {
                                        console.log('[Discord Bot] All torrents processed');
                                    }
                                } catch (error) {
                                    console.error('[Discord Bot] Error in update check:', error);
                                }
                            };

                            // Start checking for updates
                            setTimeout(checkForUpdates, checkInterval);
                        }

                    } catch (error) {
                        console.error('Error downloading game:', error);
                        await interaction.editReply('Wystąpił błąd podczas pobierania gry.');
                    }
                }
            },
            {
                data: new SlashCommandBuilder()
                    .setName('help')
                    .setDescription('Wyświetl pomoc dotyczącą bota'),
                async execute(interaction) {
                    const helpMessage = `
**Dostępne komendy:**
- \`/search <nazwa>\` - Wyszukaj grę
- \`/download <numer>\` - Pobierz grę o podanym numerze z ostatnich wyników wyszukiwania
- \`/help\` - Wyświetl tę pomoc

Bot automatycznie dodaje linki do Real-Debrid i zwraca linki do pobrania.

Możesz również używać komend tekstowych:
- \`!search <nazwa>\` - Wyszukaj grę
- \`!download <numer>\` - Pobierz grę o podanym numerze
- \`!help\` - Wyświetl tę pomoc
                    `;

                    await interaction.reply(helpMessage);
                }
            }
        ];

        // Register commands
        for (const command of commands) {
            client.commands.set(command.data.name, command);
        }

        // Register event handlers
        client.once(Events.ClientReady, async c => {
            console.log(`Discord bot ready! Logged in as ${c.user.tag}`);
            isInitialized = true;

            // Set bot activity
            client.user.setActivity('Szukam gier...', { type: ActivityType.Playing });

            // Register slash commands with Discord
            try {
                console.log('Started refreshing application (/) commands.');

                const commandsData = commands.map(command => command.data.toJSON());

                const rest = new REST().setToken(config.apiKey);

                // Register commands globally (for all servers and DMs)
                await rest.put(
                    Routes.applicationCommands(config.clientId),
                    { body: commandsData }
                );

                console.log('Successfully reloaded application (/) commands.');
            } catch (error) {
                console.error('Error registering slash commands:', error);
            }
        });

        // Handle error events
        client.on('error', (error) => {
            console.error('Discord client error:', error);
        });

        client.on('warn', (warning) => {
            console.warn('Discord client warning:', warning);
        });

        // Handle slash commands
        client.on(Events.InteractionCreate, async interaction => {
            if (!interaction.isCommand()) return;

            const command = client.commands.get(interaction.commandName);

            if (!command) {
                console.error(`No command matching ${interaction.commandName} was found.`);
                return;
            }

            try {
                await command.execute(interaction);
            } catch (error) {
                console.error(`Error executing command ${interaction.commandName}:`, error);

                const errorMessage = 'Wystąpił błąd podczas wykonywania komendy.';

                if (interaction.replied || interaction.deferred) {
                    await interaction.editReply({ content: errorMessage });
                } else {
                    await interaction.reply({ content: errorMessage, ephemeral: true });
                }
            }
        });

        // Handle text messages (both in channels and DMs)
        client.on(Events.MessageCreate, async message => {
            // Ignore messages from bots
            if (message.author.bot) return;

            // Check if it's a DM or from the configured channel
            const isDM = message.channel.type === ChannelType.DM;
            const isConfiguredChannel = message.channelId === config.channelId;

            // Only process messages from DMs or the configured channel
            if (!isDM && !isConfiguredChannel) return;

            console.log(`Received message from ${message.author.tag} in ${isDM ? 'DM' : 'channel'}: ${message.content}`);

            // Handle !search command
            if (message.content.startsWith('!search ')) {
                const query = message.content.slice(8).trim();

                if (query.length < 3) {
                    message.reply('Wyszukiwanie wymaga co najmniej 3 znaków.');
                    return;
                }

                console.log(`Searching for: ${query}`);
                const searchingMsg = await message.reply(`Szukam gry: ${query}...`);

                try {
                    // Search for games using our helper function
                    const results = await searchGamesForDiscord(query, 5);

                    if (!results || results.length === 0) {
                        searchingMsg.edit(`Nie znaleziono wyników dla "${query}"`);
                        return;
                    }

                    // Format results
                    let response = `Znaleziono ${results.length} wyników dla "${query}":\n\n`;

                    results.forEach((game, index) => {
                        response += `**${index + 1}. ${game.title}**\n`;
                        if (game.description) {
                            response += `${game.description.substring(0, 80)}${game.description.length > 80 ? '...' : ''}\n`;
                        }
                        response += `Źródło: ${game.source}\n\n`;
                    });

                    response += 'Użyj komendy `!download <numer>` aby pobrać grę.';

                    // Check if the message is too long for Discord
                    if (response.length > 1900) {
                        console.log(`[Discord Bot] Message too long (${response.length} chars), truncating...`);
                        response = response.substring(0, 1900) + '...';
                    }

                    searchingMsg.edit(response);

                    // Store results in memory for later use
                    client.lastSearchUsers = client.lastSearchUsers || new Map();
                    client.lastSearchUsers.set(message.author.id, results);

                } catch (error) {
                    console.error('Error searching games:', error);
                    searchingMsg.edit('Wystąpił błąd podczas wyszukiwania gier.');
                }
            }

            // Handle !download command
            if (message.content.startsWith('!download ')) {
                const indexStr = message.content.slice(10).trim();
                const index = parseInt(indexStr) - 1;

                // Get user-specific search results
                client.lastSearchUsers = client.lastSearchUsers || new Map();
                const userResults = client.lastSearchUsers.get(message.author.id);

                if (isNaN(index) || !userResults || index < 0 || index >= userResults.length) {
                    message.reply('Nieprawidłowy numer gry. Użyj komendy `!search <nazwa>` aby wyszukać gry.');
                    return;
                }

                const game = userResults[index];

                const downloadingMsg = await message.reply(`Pobieram grę: ${game.title}...`);

                try {
                    // Check if game has links
                    if (!game.links || game.links.length === 0) {
                        downloadingMsg.edit(`Nie znaleziono linków do pobrania dla ${game.title}.`);
                        return;
                    }

                    // Process the links
                    await downloadingMsg.edit(`Dodaję linki do Real-Debrid...`);

                    // Process the links using our helper function
                    const processedLinks = await processGameLinks(game);

                    // Check if any links are still processing
                    const hasProcessingLinks = processedLinks.some(link => link.status === 'processing');

                    // Format and send the initial links message
                    const linksMessage = formatLinksForDiscord(processedLinks, game.title);
                    await downloadingMsg.edit(linksMessage);

                    // If there are processing links, wait for them to complete and send updates
                    if (hasProcessingLinks) {
                        // Store the original processed links to track changes
                        const originalLinks = [...processedLinks];

                        // Wait for up to 2 minutes, checking every 10 seconds
                        const maxWaitTime = 120000; // 2 minutes
                        const checkInterval = 10000; // 10 seconds
                        const startTime = Date.now();

                        // Create a timeout to periodically check for updates
                        const checkForUpdates = async () => {
                            try {
                                // Check if we've exceeded the maximum wait time
                                if (Date.now() - startTime > maxWaitTime) {
                                    console.log('[Discord Bot] Timeout waiting for torrent processing');
                                    return;
                                }

                                // Check for processing links that need to be updated
                                const processingLinks = processedLinks.filter(link => link.status === 'processing');

                                if (processingLinks.length === 0) {
                                    console.log('[Discord Bot] No more processing links, stopping updates');
                                    return;
                                }

                                let hasUpdates = false;

                                // Check each processing link
                                for (const link of processingLinks) {
                                    if (link.type === 'magnet' && link.id) {
                                        try {
                                            // Get download links for the torrent
                                            const downloadLinks = await waitForTorrentLinks(link.id, 10000, 2000);

                                            if (downloadLinks && downloadLinks.length > 0) {
                                                // Remove the processing link
                                                const linkIndex = processedLinks.findIndex(l =>
                                                    l.type === 'magnet' && l.id === link.id && l.status === 'processing');

                                                if (linkIndex !== -1) {
                                                    processedLinks.splice(linkIndex, 1);

                                                    // Add the download links
                                                    downloadLinks.forEach(dlLink => {
                                                        processedLinks.push({
                                                            name: dlLink.filename || 'Plik',
                                                            originalUrl: dlLink.url,
                                                            type: 'direct',
                                                            status: 'unrestricted',
                                                            downloadUrl: dlLink.url,
                                                            filename: dlLink.filename,
                                                            filesize: dlLink.filesize
                                                        });
                                                    });

                                                    hasUpdates = true;
                                                }
                                            }
                                        } catch (error) {
                                            console.error(`[Discord Bot] Error checking torrent status: ${error.message}`);

                                            // Update the link status to indicate it's no longer processing
                                            const linkIndex = processedLinks.findIndex(l =>
                                                l.type === 'magnet' && l.id === link.id && l.status === 'processing');

                                            if (linkIndex !== -1) {
                                                processedLinks[linkIndex].status = 'added';
                                                processedLinks[linkIndex].message = 'Torrent dodany do Real-Debrid. Sprawdź zakładkę "Chlewik" w aplikacji, aby pobrać pliki.';
                                                hasUpdates = true;
                                            }
                                        }
                                    }
                                }

                                // If there are updates, send a new message
                                if (hasUpdates) {
                                    const updatedLinksMessage = formatLinksForDiscord(processedLinks, game.title);
                                    await downloadingMsg.edit(updatedLinksMessage);
                                }

                                // Schedule the next check if there are still processing links
                                const stillProcessing = processedLinks.some(link => link.status === 'processing');
                                if (stillProcessing) {
                                    setTimeout(checkForUpdates, checkInterval);
                                } else {
                                    console.log('[Discord Bot] All torrents processed');
                                }
                            } catch (error) {
                                console.error('[Discord Bot] Error in update check:', error);
                            }
                        };

                        // Start checking for updates
                        setTimeout(checkForUpdates, checkInterval);
                    }
                } catch (error) {
                    console.error('Error downloading game:', error);
                    downloadingMsg.edit('Wystąpił błąd podczas pobierania gry.');
                }
            }

            // Handle !help command
            if (message.content === '!help') {
                const helpMessage = `
**Dostępne komendy:**
- \`!search <nazwa>\` - Wyszukaj grę
- \`!download <numer>\` - Pobierz grę o podanym numerze z ostatnich wyników wyszukiwania
- \`!help\` - Wyświetl tę pomoc

Bot automatycznie dodaje linki do Real-Debrid i zwraca linki do pobrania.

Możesz również używać komend slash:
- \`/search\` - Wyszukaj grę
- \`/download\` - Pobierz grę z wyników wyszukiwania
- \`/help\` - Wyświetl tę pomoc
                `;

                message.reply(helpMessage);
            }
        });

        // Login to Discord
        console.log('Attempting to login to Discord...');
        console.log('Token format check:', config.apiKey ? `Token length: ${config.apiKey.length}, starts with: ${config.apiKey.substring(0, 5)}...` : 'No token provided');

        try {
            // Validate token format - less strict check
            if (!config.apiKey) {
                console.error('No Discord bot token provided.');
                throw new Error('No Discord bot token provided.');
            }

            if (config.apiKey.length < 50) {
                console.error('Discord bot token seems too short. Please check your token.');
                throw new Error('Discord bot token seems too short. Please check your token.');
            }

            // Try to login with the token
            console.log('Attempting to login with provided token...');
            await client.login(config.apiKey);
            console.log('Login successful!');

            // Store bot instance
            botInstance = client;
            isInitialized = true;

            return true;
        } catch (loginError) {
            console.error('Discord login error:', loginError);

            // Provide more helpful error messages based on common issues
            if (loginError.message.includes('invalid token')) {
                throw new Error('Invalid Discord bot token. Please check that you\'re using the correct token from the Bot section in Discord Developer Portal.');
            } else if (loginError.message.includes('disallowed intents')) {
                throw new Error('Bot is missing required intents. Please enable "Message Content Intent" in the Discord Developer Portal > Bot section.');
            } else {
                throw new Error(`Discord login failed: ${loginError.message}`);
            }
        }
    } catch (error) {
        console.error('Error initializing Discord bot:', error);
        throw error; // Re-throw to propagate to API endpoint
    }
}

// Shutdown Discord bot
export async function shutdownBot() {
    if (botInstance) {
        botInstance.destroy();
        botInstance = null;
        isInitialized = false;
        console.log('Discord bot shut down');
    }
}

// Check if bot is initialized
export function isBotInitialized() {
    try {
        // Check if bot instance exists and is logged in
        return isInitialized && botInstance && botInstance.isReady();
    } catch (error) {
        console.error('Error checking bot initialization status:', error);
        return false;
    }
}

// Restart bot (useful when configuration changes)
export async function restartBot() {
    try {
        await shutdownBot();
        return await initializeBot();
    } catch (error) {
        console.error('Error restarting Discord bot:', error);
        throw error;
    }
}

/**
 * Send a message to the configured Discord channel
 * @param {string} message Message to send
 * @returns {Promise<boolean>} Success status
 */
export async function sendDiscordMessage(message) {
    try {
        // Check if bot is initialized
        if (!isInitialized || !botInstance) {
            console.error('[Discord Bot] Cannot send message: Bot is not initialized');
            return false;
        }

        // Get the configuration
        const config = await loadConfig();

        // If no channel ID is configured, try to send to the first available channel
        if (!config.channelId) {
            console.warn('[Discord Bot] No channel ID configured, trying to find a suitable channel');

            // Try to find a suitable channel
            const channels = botInstance.channels.cache;
            const textChannel = channels.find(channel =>
                channel.type === ChannelType.GuildText &&
                channel.permissionsFor(botInstance.user).has('SEND_MESSAGES')
            );

            if (textChannel) {
                await textChannel.send(message);
                console.log('[Discord Bot] Message sent to fallback channel');
                return true;
            } else {
                console.error('[Discord Bot] No suitable channel found to send message');
                return false;
            }
        }

        // Try to get the configured channel
        try {
            const channel = await botInstance.channels.fetch(config.channelId);

            if (!channel) {
                console.error(`[Discord Bot] Channel with ID ${config.channelId} not found`);
                return false;
            }

            await channel.send(message);
            console.log('[Discord Bot] Message sent successfully');
            return true;
        } catch (channelError) {
            console.error('[Discord Bot] Error sending message to channel:', channelError);

            // If we can't send to the configured channel, try to send via DM to the owner
            try {
                const application = await botInstance.application.fetch();
                const owner = await botInstance.users.fetch(application.owner.id);

                await owner.send(`[NOTIFICATION] ${message}\n\n(This message was sent as DM because the bot couldn't send to the configured channel)`);
                console.log('[Discord Bot] Message sent to owner via DM');
                return true;
            } catch (dmError) {
                console.error('[Discord Bot] Error sending DM to owner:', dmError);
                return false;
            }
        }
    } catch (error) {
        console.error('[Discord Bot] Error sending Discord message:', error);
        return false;
    }
}
