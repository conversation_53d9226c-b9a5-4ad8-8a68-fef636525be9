/**
 * Server-side API configuration
 * Contains API keys and other configuration for external APIs
 */

// OMDB API configuration
export const OMDB_API_KEY = 'c24805a2';

// TMDB API configuration
export const TMDB_API_KEY = '085f067105ff44afd48943580aedecfd';
export const TMDB_API_READ_TOKEN = 'eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIwODVmMDY3MTA1ZmY0NGFmZDQ4OTQzNTgwYWVkZWNmZCIsIm5iZiI6MTU3MDUyMzEzNy41OTAwMDAyLCJzdWIiOiI1ZDljNDgwMTZkNjc1YTAwMjhmMmZmMzgiLCJzY29wZXMiOlsiYXBpXJlYWQiXSwidmVyc2lvbiI6MX0.7q8Dc-itQ004jajnINOAMmysAtuN1A-AzDBSg0SDHas';

// IGDB API configuration
export const IGDB_CLIENT_ID = 'hfy9ogj9xvy0mhjfshvk2w3dtfvjti';
export const IGDB_CLIENT_SECRET = 'i8e0ni67igwekuoxddx2yr00df2zk8';

// SteamGridDB API configuration
export const STEAMGRIDDB_API_KEY = '7cfae18520dd8e35a8f971f8025291d0';

// Trakt API configuration
export const TRAKT_CLIENT_ID = '01d887218070ff644527e1fb9524ce51b096fd15faef26fd6c6f60ef7dac7978';
export const TRAKT_CLIENT_SECRET = 'c2c15c65f038c0d0c1d29d7276cb0caf639fb22563cd030936fcf85fbec497e8';

// Fanart.tv API configuration
export const FANART_API_KEY = '9a1fe6e371a3be5d1877660a9b6454f1';
export const FANART_CLIENT_KEY = '5e563afee3a08cd86e6607e0448d5f8e';
