/**
 * PC game search implementation for Discord bot
 * This file contains functions for searching PC games from various sources
 * and processing the results for use with the Discord bot.
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs/promises';

const execAsync = promisify(exec);

// Sources for game data
const FITGIRL_SOURCE = "https://hydralinks.cloud/sources/fitgirl.json";
const DODI_SOURCE = "https://hydralinks.cloud/sources/dodi.json";
const GOG_SOURCE = "https://hydralinks.cloud/sources/gog.json";

// Cache for game data
let gameDataCache = {
    fitgirl: null,
    dodi: null,
    gog: null,
    timestamp: 0
};

/**
 * Search for PC games from all sources
 * @param {string} query Search query
 * @param {Object} options Search options
 * @returns {Promise<Array>} Search results
 */
export async function searchPcGames(query, options = {}) {
    try {
        console.log(`[PC Search Discord] Searching for games with query: ${query}`);
        
        // Default options
        const searchOptions = {
            maxResults: 5,
            sources: {
                fitgirl: true,
                dodi: true,
                gog: true,
                gamedrive: true
            },
            ...options
        };
        
        // Load game data if needed
        await loadGameData();
        
        // Search in all enabled sources
        const results = [];
        
        // Search in FitGirl
        if (searchOptions.sources.fitgirl && gameDataCache.fitgirl) {
            console.log('[PC Search Discord] Searching in FitGirl...');
            const fitgirlResults = searchInFitGirl(query);
            results.push(...fitgirlResults);
        }
        
        // Search in DODI
        if (searchOptions.sources.dodi && gameDataCache.dodi) {
            console.log('[PC Search Discord] Searching in DODI...');
            const dodiResults = searchInDODI(query);
            results.push(...dodiResults);
        }
        
        // Search in GOG
        if (searchOptions.sources.gog && gameDataCache.gog) {
            console.log('[PC Search Discord] Searching in GOG...');
            const gogResults = searchInGOG(query);
            results.push(...gogResults);
        }
        
        // Search in GameDrive
        if (searchOptions.sources.gamedrive) {
            console.log('[PC Search Discord] Searching in GameDrive...');
            const gameDriveResults = await searchInGameDrive(query);
            results.push(...gameDriveResults);
        }
        
        // Sort results by relevance (exact matches first, then partial matches)
        const sortedResults = sortResultsByRelevance(results, query);
        
        // Limit the number of results
        const limitedResults = sortedResults.slice(0, searchOptions.maxResults);
        
        console.log(`[PC Search Discord] Found ${results.length} total results, returning ${limitedResults.length}`);
        return limitedResults;
    } catch (error) {
        console.error('[PC Search Discord] Error searching games:', error);
        return [];
    }
}

/**
 * Load game data from sources or cache
 * @returns {Promise<void>}
 */
async function loadGameData() {
    try {
        // Check if we need to refresh the cache (older than 24 hours)
        const now = Date.now();
        const cacheAge = now - gameDataCache.timestamp;
        const cacheExpiration = 24 * 60 * 60 * 1000; // 24 hours
        
        if (cacheAge < cacheExpiration && gameDataCache.fitgirl && gameDataCache.dodi && gameDataCache.gog) {
            console.log('[PC Search Discord] Using cached game data');
            return;
        }
        
        console.log('[PC Search Discord] Loading game data from sources...');
        
        // Load data from FitGirl
        try {
            console.log('[PC Search Discord] Loading FitGirl data...');
            const fitgirlResponse = await fetch(FITGIRL_SOURCE);
            if (fitgirlResponse.ok) {
                gameDataCache.fitgirl = await fitgirlResponse.json();
                console.log(`[PC Search Discord] Loaded ${gameDataCache.fitgirl.downloads.length} FitGirl games`);
            } else {
                console.error(`[PC Search Discord] Error loading FitGirl data: ${fitgirlResponse.status}`);
            }
        } catch (error) {
            console.error('[PC Search Discord] Error loading FitGirl data:', error);
        }
        
        // Load data from DODI
        try {
            console.log('[PC Search Discord] Loading DODI data...');
            const dodiResponse = await fetch(DODI_SOURCE);
            if (dodiResponse.ok) {
                gameDataCache.dodi = await dodiResponse.json();
                console.log(`[PC Search Discord] Loaded ${gameDataCache.dodi.downloads.length} DODI games`);
            } else {
                console.error(`[PC Search Discord] Error loading DODI data: ${dodiResponse.status}`);
            }
        } catch (error) {
            console.error('[PC Search Discord] Error loading DODI data:', error);
        }
        
        // Load data from GOG
        try {
            console.log('[PC Search Discord] Loading GOG data...');
            const gogResponse = await fetch(GOG_SOURCE);
            if (gogResponse.ok) {
                gameDataCache.gog = await gogResponse.json();
                console.log(`[PC Search Discord] Loaded ${gameDataCache.gog.downloads.length} GOG games`);
            } else {
                console.error(`[PC Search Discord] Error loading GOG data: ${gogResponse.status}`);
            }
        } catch (error) {
            console.error('[PC Search Discord] Error loading GOG data:', error);
        }
        
        // Update cache timestamp
        gameDataCache.timestamp = now;
        
    } catch (error) {
        console.error('[PC Search Discord] Error loading game data:', error);
    }
}

/**
 * Search for games in FitGirl data
 * @param {string} query Search query
 * @returns {Array} Search results
 */
function searchInFitGirl(query) {
    try {
        const results = [];
        const queryLower = query.toLowerCase();
        
        if (!gameDataCache.fitgirl || !gameDataCache.fitgirl.downloads) {
            return results;
        }
        
        gameDataCache.fitgirl.downloads.forEach(download => {
            if (download.title.toLowerCase().includes(queryLower) && download.uris && download.uris.length > 0) {
                // Find the first magnet link
                const magnetLink = download.uris.find(uri => uri.startsWith('magnet:'));
                
                if (magnetLink) {
                    results.push({
                        title: cleanGameTitle(download.title),
                        description: `Repack od FitGirl, rozmiar: ${download.fileSize || 'Nieznany'}`,
                        source: 'FitGirl',
                        links: [
                            {
                                name: 'Magnet',
                                url: magnetLink
                            }
                        ],
                        fileSize: download.fileSize || 'Nieznany rozmiar',
                        uploadDate: download.uploadDate || 'Nieznana data'
                    });
                }
            }
        });
        
        return results;
    } catch (error) {
        console.error('[PC Search Discord] Error searching in FitGirl:', error);
        return [];
    }
}

/**
 * Search for games in DODI data
 * @param {string} query Search query
 * @returns {Array} Search results
 */
function searchInDODI(query) {
    try {
        const results = [];
        const queryLower = query.toLowerCase();
        
        if (!gameDataCache.dodi || !gameDataCache.dodi.downloads) {
            return results;
        }
        
        gameDataCache.dodi.downloads.forEach(download => {
            if (download.title.toLowerCase().includes(queryLower) && download.uris && download.uris.length > 0) {
                // Find the first magnet link
                const magnetLink = download.uris.find(uri => uri.startsWith('magnet:'));
                
                if (magnetLink) {
                    results.push({
                        title: cleanGameTitle(download.title),
                        description: `Repack od DODI, rozmiar: ${download.fileSize || 'Nieznany'}`,
                        source: 'DODI',
                        links: [
                            {
                                name: 'Magnet',
                                url: magnetLink
                            }
                        ],
                        fileSize: download.fileSize || 'Nieznany rozmiar',
                        uploadDate: download.uploadDate || 'Nieznana data'
                    });
                }
            }
        });
        
        return results;
    } catch (error) {
        console.error('[PC Search Discord] Error searching in DODI:', error);
        return [];
    }
}

/**
 * Search for games in GOG data
 * @param {string} query Search query
 * @returns {Array} Search results
 */
function searchInGOG(query) {
    try {
        const results = [];
        const queryLower = query.toLowerCase();
        
        if (!gameDataCache.gog || !gameDataCache.gog.downloads) {
            return results;
        }
        
        gameDataCache.gog.downloads.forEach(download => {
            if (download.title.toLowerCase().includes(queryLower) && download.uris && download.uris.length > 0) {
                // Find the first magnet link
                const magnetLink = download.uris.find(uri => uri.startsWith('magnet:'));
                
                if (magnetLink) {
                    results.push({
                        title: cleanGameTitle(download.title),
                        description: `Wersja GOG, rozmiar: ${download.fileSize || 'Nieznany'}`,
                        source: 'GOG',
                        links: [
                            {
                                name: 'Magnet',
                                url: magnetLink
                            }
                        ],
                        fileSize: download.fileSize || 'Nieznany rozmiar',
                        uploadDate: download.uploadDate || 'Nieznana data'
                    });
                }
            }
        });
        
        return results;
    } catch (error) {
        console.error('[PC Search Discord] Error searching in GOG:', error);
        return [];
    }
}

/**
 * Search for games in GameDrive
 * @param {string} query Search query
 * @returns {Promise<Array>} Search results
 */
async function searchInGameDrive(query) {
    try {
        console.log(`[PC Search Discord] Searching GameDrive for: ${query}`);
        
        // Use the gamedrive_search.py script
        const scriptPath = path.join(process.cwd(), 'gamedrive_search.py');
        
        // Execute the Python script
        const { stdout, stderr } = await execAsync(`python3 ${scriptPath} "${query}"`);
        
        if (stderr) {
            console.error('[PC Search Discord] Debug info from Python script:', stderr);
        }
        
        // Parse the results
        const results = JSON.parse(stdout);
        console.log(`[PC Search Discord] Found ${results.length} GameDrive results`);
        
        // Process the results to match our format
        return results.map(result => ({
            title: result.title,
            description: result.description || `Gra z GameDrive`,
            source: 'GameDrive',
            links: result.links || [],
            url: result.url
        }));
    } catch (error) {
        console.error('[PC Search Discord] Error searching in GameDrive:', error);
        return [];
    }
}

/**
 * Sort results by relevance
 * @param {Array} results Search results
 * @param {string} query Search query
 * @returns {Array} Sorted results
 */
function sortResultsByRelevance(results, query) {
    const queryLower = query.toLowerCase();
    
    // Define a function to calculate relevance score
    const getRelevanceScore = (result) => {
        const titleLower = result.title.toLowerCase();
        
        // Exact match gets highest score
        if (titleLower === queryLower) {
            return 100;
        }
        
        // Title starts with query gets high score
        if (titleLower.startsWith(queryLower)) {
            return 80;
        }
        
        // Title contains query as a whole word gets medium score
        if (titleLower.includes(` ${queryLower} `)) {
            return 60;
        }
        
        // Title contains query gets low score
        if (titleLower.includes(queryLower)) {
            return 40;
        }
        
        // Default score
        return 0;
    };
    
    // Sort by relevance score (descending) and then by source priority
    return [...results].sort((a, b) => {
        const scoreA = getRelevanceScore(a);
        const scoreB = getRelevanceScore(b);
        
        // If scores are different, sort by score
        if (scoreA !== scoreB) {
            return scoreB - scoreA;
        }
        
        // If scores are the same, sort by source priority
        const sourcePriority = {
            'FitGirl': 3,
            'DODI': 2,
            'GOG': 1,
            'GameDrive': 0
        };
        
        return (sourcePriority[b.source] || 0) - (sourcePriority[a.source] || 0);
    });
}

/**
 * Clean game title by removing common tags and phrases
 * @param {string} title Game title
 * @returns {string} Cleaned title
 */
function cleanGameTitle(title) {
    let cleanTitle = title;
    
    // Remove common tags and phrases
    const patterns = [
        /\bFitGirl\b/i,
        /\bDODI\b/i,
        /\bRepack\b/i,
        /\bMulti\d*\b/i,
        /\bv\d+\.\d+\b/i,
        /\bv\d+\b/i,
        /\bUpdate\b/i,
        /\bDLC\b/i,
        /\bAll\s*DLCs\b/i,
        /\bGOG\b/i,
        /\bEGS\b/i,
        /\bSteam\b/i,
        /\bEpic\b/i,
        /\bCrack\b/i,
        /\bCracked\b/i,
        /\bCODEX\b/i,
        /\bPLAZA\b/i,
        /\bELAMIGOS\b/i,
        /\bSKIDROW\b/i,
        /\bRELOADED\b/i,
        /\bCPY\b/i,
        /\bHI2U\b/i,
        /\bBALTMAN\b/i,
        /\bDARKSiDERS\b/i,
        /\bSelectivE\b/i
    ];
    
    // Apply each pattern
    patterns.forEach(pattern => {
        cleanTitle = cleanTitle.replace(pattern, '');
    });
    
    // Remove release information
    cleanTitle = cleanTitle
        .replace(/\b\d{4}\b/g, '') // Years
        .replace(/\[\d+\]/g, '') // [1], [2], etc.
        .replace(/\(\d+\)/g, '') // (1), (2), etc.
        .replace(/\b\d{1,2}\.\d{1,2}\b/g, '') // Version numbers like 1.0, 2.1
        .replace(/\b\d{1,2}\.\d{1,2}\.\d{1,2}\b/g, '') // Version numbers like 1.0.0
        .replace(/\b\d+bit\b/i, '') // 32bit, 64bit
        .replace(/\b\d+\s*GB\b/i, '') // 10GB, 20 GB
        .replace(/\b\d+\s*MB\b/i, '') // 500MB, 1000 MB
        .replace(/\bx86\b/i, '')
        .replace(/\bx64\b/i, '')
        .replace(/\bx86-64\b/i, '')
        .replace(/\bx86_64\b/i, '');
    
    // Clean up whitespace
    cleanTitle = cleanTitle
        .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
        .replace(/\s*-\s*/g, ' - ') // Normalize dashes
        .replace(/\s*\+\s*/g, ' + ') // Normalize plus signs
        .replace(/\s*:\s*/g, ': ') // Normalize colons
        .trim();
    
    return cleanTitle;
}
