/**
 * Service for communicating with Anna's Archive
 * Handles searching and retrieving book information
 */

import { load } from 'cheerio';

/**
 * Search for books on <PERSON>'s Archive
 * @param {string} query - Search query
 * @returns {Promise<Object>} - Search results or error
 */
export async function searchBooks(query) {
    try {
        console.log(`[Anna's Archive Service] Searching for books with query: ${query}`);

        // Format the search URL
        const searchUrl = `https://annas-archive.org/search?q=${encodeURIComponent(query)}`;
        console.log(`[Anna's Archive Service] Search URL: ${searchUrl}`);

        // Fetch the search results page
        const response = await fetch(searchUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (!response.ok) {
            console.error(`[Anna's Archive Service] API Error: ${response.status}`);
            return { error: `API Error: ${response.status}` };
        }

        const html = await response.text();

        // Parse the HTML using cheerio
        const $ = load(html);

        // Extract book information from search results
        const results = [];

        $('#aarecord-list .js-vim-focus').each((i, element) => {
            // Extract MD5 hash from the href attribute
            const href = $(element).attr('href');
            const md5Match = href.match(/\/md5\/([a-f0-9]{32})/);
            const md5 = md5Match ? md5Match[1] : null;

            if (!md5) return; // Skip if no MD5 found

            // Extract book information
            const title = $(element).find('h3').text().trim();
            const author = $(element).find('.max-lg\\:line-clamp-\\[2\\]').text().trim();
            const year = $(element).find('.truncate.leading-\\[1\\.2\\]').text().trim();
            const fileInfo = $(element).find('.line-clamp-\\[2\\]').text().trim();

            // Extract format from fileInfo
            const formatMatch = fileInfo.match(/\.(epub|pdf|mobi|azw3|djvu|fb2|txt)/i);
            const format = formatMatch ? formatMatch[1].toLowerCase() : 'unknown';

            // Extract language from fileInfo
            const langMatch = fileInfo.match(/([a-z]{2})\]/i);
            const language = langMatch ? langMatch[1].toLowerCase() : 'unknown';

            // Extract file size from fileInfo
            const sizeMatch = fileInfo.match(/(\d+(\.\d+)?)(MB|KB|GB)/i);
            const fileSize = sizeMatch ? sizeMatch[0] : 'unknown';

            // Extract cover image URL
            const coverImg = $(element).find('img').attr('src');

            results.push({
                md5,
                title,
                author,
                year,
                format,
                language,
                fileSize,
                coverImg,
                fileInfo
            });
        });

        console.log(`[Anna's Archive Service] Found ${results.length} results for query: ${query}`);

        return { results };
    } catch (error) {
        console.error('[Anna\'s Archive Service] Error searching books:', error);
        return { error: error.message };
    }
}

/**
 * Get download links for a book
 * @param {string} md5 - MD5 hash of the book
 * @returns {Promise<Object>} - Download links or error
 */
export async function getDownloadLinks(md5) {
    try {
        console.log(`[Anna's Archive Service] Getting download links for book with MD5: ${md5}`);

        // Format the book page URL
        const bookUrl = `https://annas-archive.org/md5/${md5}`;
        console.log(`[Anna's Archive Service] Book URL: ${bookUrl}`);

        // Fetch the book page
        const response = await fetch(bookUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (!response.ok) {
            console.error(`[Anna's Archive Service] API Error: ${response.status}`);
            return { error: `API Error: ${response.status}` };
        }

        const html = await response.text();

        // Parse the HTML using cheerio
        const $ = load(html);

        // Extract book details
        const title = $('.max-lg\\:line-clamp-\\[2\\]').first().text().trim();
        const author = $('.max-lg\\:line-clamp-\\[2\\].italic').first().text().trim();
        const coverImg = $('#list_cover_aarecord_id__md5\\:' + md5.toLowerCase()).find('img').attr('src');

        // Extract download links
        const downloadLinks = [];

        // Debug: Log the HTML structure to understand what we're working with
        console.log('[Anna\'s Archive Service] Analyzing download panel structure');

        // Check if the download panel exists
        const downloadPanel = $('#md5-panel-downloads');
        if (!downloadPanel.length) {
            console.error('[Anna\'s Archive Service] Download panel not found');
        } else {
            console.log('[Anna\'s Archive Service] Download panel found');
        }

        // Find all slow download options
        const slowDownloadOptions = [];

        // First approach: Look for all slow download links directly
        $('#md5-panel-downloads a').each((i, el) => {
            const href = $(el).attr('href');
            const text = $(el).text().trim();
            if (href && href.includes('/slow_download/')) {
                slowDownloadOptions.push({ href, text, element: $(el).parent().text().trim() });
                console.log(`[Anna\'s Archive Service] Found slow download option: ${text} - ${href}`);
            }
        });

        // Second approach: Look for slow download links in list items
        $('#md5-panel-downloads .list-disc li').each((i, el) => {
            const text = $(el).text().trim();
            const link = $(el).find('a').first().attr('href');

            if (link && link.includes('/slow_download/')) {
                // Check if this link is already in our options
                const exists = slowDownloadOptions.some(option => option.href === link);
                if (!exists) {
                    slowDownloadOptions.push({ href: link, text: $(el).find('a').first().text().trim(), element: text });
                    console.log(`[Anna\'s Archive Service] Found additional slow download option: ${text} - ${link}`);
                }
            }
        });

        console.log(`[Anna\'s Archive Service] Total slow download options found: ${slowDownloadOptions.length}`);

        // Add slow download options directly
        console.log(`[Anna\'s Archive Service] Adding slow download links directly`);

        // First, find the "no waitlist" option
        let noWaitlistOption = slowDownloadOptions.find(option =>
            option.element.includes('no waitlist') ||
            option.text.includes('Slow Partner Server #3')
        );

        // Add the no waitlist option first if found
        if (noWaitlistOption) {
            const slowDownloadUrl = `https://annas-archive.org${noWaitlistOption.href}`;
            console.log(`[Anna\'s Archive Service] Adding preferred no waitlist option: ${slowDownloadUrl}`);

            downloadLinks.push({
                url: slowDownloadUrl,
                type: 'slow',
                label: `⭐ Slow Download: ${noWaitlistOption.text} (No Waitlist)`,
                isPreferred: true,
                directUrl: false
            });
        }

        // Add all other slow download options
        for (const option of slowDownloadOptions) {
            // Skip if it's the no waitlist option we already added
            if (noWaitlistOption && option.href === noWaitlistOption.href) {
                continue;
            }

            const slowDownloadUrl = `https://annas-archive.org${option.href}`;
            console.log(`[Anna\'s Archive Service] Adding slow download option: ${slowDownloadUrl}`);

            downloadLinks.push({
                url: slowDownloadUrl,
                type: 'slow',
                label: `Slow Download: ${option.text}`,
                isPreferred: false,
                directUrl: false
            });
        }

        // Get other download options as fallbacks
        console.log('[Anna\'s Archive Service] Looking for external download options');

        // Try to find all external links in the download panel
        const externalLinks = [];

        // Approach 1: Look for the "show external downloads" button and try to extract links from the hidden section
        const showExternalButton = $('.js-show-external-button');
        if (showExternalButton.length) {
            console.log('[Anna\'s Archive Service] Found show external downloads button');

            // Look for all list items in the external section
            $('.js-show-external li').each((i, element) => {
                const linkElement = $(element).find('a').first();
                const linkText = linkElement.text().trim();
                const linkUrl = linkElement.attr('href');
                const fullText = $(element).text().trim();

                if (linkUrl) {
                    externalLinks.push({
                        linkText,
                        linkUrl,
                        fullText,
                        source: 'external-section'
                    });
                    console.log(`[Anna\'s Archive Service] Found external link in hidden section: ${linkText} - ${linkUrl}`);
                }
            });
        }

        // Approach 2: Look for all links in list items that might be external sources
        $('#md5-panel-downloads .list-disc li').each((i, element) => {
            const fullText = $(element).text().trim();
            const linkElement = $(element).find('a').first();
            const linkText = linkElement.text().trim();
            const linkUrl = linkElement.attr('href');

            // Skip if no URL or if it's a slow/fast download link
            if (!linkUrl || linkUrl.includes('/fast_download/') || linkUrl.includes('/slow_download/')) {
                return;
            }

            // Check if this is likely an external link
            const isExternal =
                linkUrl.startsWith('http') ||
                fullText.includes('Libgen') ||
                fullText.includes('Z-Library') ||
                fullText.includes('IPFS') ||
                fullText.includes('torrent');

            if (isExternal) {
                // Check if we already have this URL
                const exists = externalLinks.some(link => link.linkUrl === linkUrl);
                if (!exists) {
                    externalLinks.push({
                        linkText,
                        linkUrl,
                        fullText,
                        source: 'list-item'
                    });
                    console.log(`[Anna\'s Archive Service] Found external link in list item: ${linkText} - ${linkUrl}`);
                }
            }
        });

        // Approach 3: Look for specific external source patterns
        const externalSourcePatterns = [
            { pattern: 'libgen', label: 'Library Genesis' },
            { pattern: 'z-lib', label: 'Z-Library' },
            { pattern: 'ipfs', label: 'IPFS' },
            { pattern: 'torrent', label: 'Torrent' },
            { pattern: 'archive.org', label: 'Internet Archive' }
        ];

        $('a').each((i, el) => {
            const href = $(el).attr('href');
            const text = $(el).text().trim();

            if (!href) return;

            // Check if this URL matches any of our patterns
            for (const { pattern, label } of externalSourcePatterns) {
                if (href.includes(pattern) || text.toLowerCase().includes(pattern)) {
                    // Check if we already have this URL
                    const exists = externalLinks.some(link => link.linkUrl === href);
                    if (!exists) {
                        externalLinks.push({
                            linkText: text || label,
                            linkUrl: href,
                            fullText: text,
                            source: 'pattern-match',
                            patternMatched: pattern
                        });
                        console.log(`[Anna\'s Archive Service] Found external link by pattern match (${pattern}): ${text} - ${href}`);
                    }
                    break;
                }
            }
        });

        // Add all found external links
        for (const { linkText, linkUrl, fullText, source, patternMatched } of externalLinks) {
            // Make sure the URL is absolute
            const absoluteUrl = linkUrl.startsWith('http') ? linkUrl : `https://annas-archive.org${linkUrl.startsWith('/') ? '' : '/'}${linkUrl}`;

            // Create a descriptive label
            let label = `External: ${linkText}`;

            // Add source information if available
            if (source === 'external-section') {
                label = `External: ${linkText}`;
            } else if (patternMatched) {
                label = `External: ${patternMatched.charAt(0).toUpperCase() + patternMatched.slice(1)}`;
                if (linkText && !linkText.toLowerCase().includes(patternMatched.toLowerCase())) {
                    label += ` (${linkText})`;
                }
            }

            // Add additional context from the full text if available
            if (fullText) {
                const contextMatch = fullText.match(/\(([^)]+)\)/);
                if (contextMatch && contextMatch[1] && !label.includes(contextMatch[1])) {
                    label += ` ${contextMatch[1]}`;
                }
            }

            downloadLinks.push({
                url: absoluteUrl,
                type: 'external',
                label: label,
                source: source
            });
            console.log(`[Anna\'s Archive Service] Added external link: ${absoluteUrl}`);
        }

        // If we still have no download links, try a more aggressive approach
        if (downloadLinks.length === 0) {
            console.log('[Anna\'s Archive Service] No download links found, trying more aggressive approach');

            // Look for any link that might be a download link
            $('a').each((i, el) => {
                const href = $(el).attr('href');
                const text = $(el).text().trim();

                if (href && (
                    href.includes('libgen') ||
                    href.includes('download') ||
                    href.includes('z-lib') ||
                    href.includes('ipfs') ||
                    href.includes('torrents') ||
                    href.includes('archive.org')
                )) {
                    // Check if we already have this URL
                    const exists = downloadLinks.some(link => link.url === href);
                    if (!exists) {
                        const absoluteUrl = href.startsWith('http') ? href : `https://annas-archive.org${href.startsWith('/') ? '' : '/'}${href}`;

                        downloadLinks.push({
                            url: absoluteUrl,
                            type: 'external',
                            label: `External: ${text || 'Alternative Source'}`,
                            source: 'fallback'
                        });
                        console.log(`[Anna\'s Archive Service] Added fallback link: ${absoluteUrl}`);
                    }
                }
            });
        }

        // Add a direct link to Anna's Archive page as a last resort
        if (downloadLinks.length === 0) {
            const annasArchiveUrl = `https://annas-archive.org/md5/${md5}`;
            downloadLinks.push({
                url: annasArchiveUrl,
                type: 'external',
                label: 'View on Anna\'s Archive',
                source: 'fallback'
            });
            console.log(`[Anna\'s Archive Service] Added Anna's Archive page link: ${annasArchiveUrl}`);
        }

        return {
            title,
            author,
            coverImg,
            downloadLinks
        };
    } catch (error) {
        console.error('[Anna\'s Archive Service] Error getting download links:', error);
        return { error: error.message };
    }
}

/**
 * Helper function to convert relative URLs to absolute URLs
 * @param {string} relativeUrl - Relative URL
 * @returns {string} - Absolute URL
 */
function toAbsoluteUrl(relativeUrl) {
    if (!relativeUrl) return null;

    // If it's already an absolute URL, return it
    if (relativeUrl.startsWith('http://') || relativeUrl.startsWith('https://')) {
        return relativeUrl;
    }

    // Otherwise, prepend the base URL
    return `https://annas-archive.org${relativeUrl.startsWith('/') ? '' : '/'}${relativeUrl}`;
}
