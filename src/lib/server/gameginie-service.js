/**
 * Service for searching and fetching ROM/ISO games from gameginie.com
 */

import fetch from 'node-fetch';
import { load } from 'cheerio';
import { URL } from 'url';

// Base URL for gameginie.com
const BASE_URL = 'https://www.gameginie.com';

/**
 * Convert a relative URL to an absolute URL
 * @param {string} url - The URL to convert
 * @param {string} baseUrl - The base URL to use
 * @returns {string} - The absolute URL
 */
function toAbsoluteUrl(url, baseUrl = BASE_URL) {
    if (!url) return url;

    try {
        // Check if it's already an absolute URL
        new URL(url);
        return url;
    } catch (e) {
        // It's a relative URL, make it absolute
        return url.startsWith('/')
            ? `${baseUrl}${url}`
            : `${baseUrl}/${url}`;
    }
}

/**
 * Search for games on gameginie.com
 * @param {string} query - The search query
 * @returns {Promise<Object>} - Search results or error
 */
export async function searchGames(query) {
    try {
        console.log(`[GameGinie Service] Searching for games with query: ${query}`);

        // Format the search URL
        const searchUrl = `${BASE_URL}/?s=${encodeURIComponent(query)}`;
        console.log(`[GameGinie Service] Search URL: ${searchUrl}`);

        // Fetch the search results page
        const response = await fetch(searchUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (!response.ok) {
            console.error(`[GameGinie Service] Search request failed with status: ${response.status}`);
            return {
                error: `Search request failed with status: ${response.status}`,
                results: []
            };
        }

        const html = await response.text();

        // Parse the HTML using cheerio
        const $ = load(html);
        const results = [];

        // Find all game entries - they are in td_module_6 divs
        $('.td_module_6').each((index, element) => {
            try {
                // Extract game title from the entry-title a element
                const titleElement = $(element).find('.entry-title a');
                const title = titleElement.text().trim();

                // Skip if no title found
                if (!title) return;

                // Extract game URL from the entry-title a href
                const url = toAbsoluteUrl(titleElement.attr('href'));

                // Extract image URL from the entry-thumb img src
                let imageUrl = $(element).find('.entry-thumb').attr('src');
                if (imageUrl) {
                    imageUrl = toAbsoluteUrl(imageUrl);
                }

                // Add to results
                results.push({
                    title,
                    url,
                    imageUrl
                });
            } catch (err) {
                console.error(`[GameGinie Service] Error parsing game entry: ${err.message}`);
            }
        });

        console.log(`[GameGinie Service] Found ${results.length} results`);
        return { results };
    } catch (error) {
        console.error('[GameGinie Service] Search error:', error);
        return {
            error: `An error occurred during search: ${error.message}`,
            results: []
        };
    }
}

/**
 * Get download links from game page
 * @param {string} url - The game page URL
 * @returns {Promise<Object>} - Download links or error
 */
export async function getDownloadLinks(url) {
    try {
        console.log(`[GameGinie Service] Getting download links from: ${url}`);

        // Fetch the game page
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        });

        if (!response.ok) {
            console.error(`[GameGinie Service] Game page request failed with status: ${response.status}`);
            return {
                error: `Game page request failed with status: ${response.status}`
            };
        }

        const html = await response.text();

        // Parse the HTML using cheerio
        const $ = load(html);

        // Extract game information
        const title = $('.entry-title').first().text().trim();

        // Look for 1fichier links first
        const oneFichierLinks = [];
        $('a').each((i, el) => {
            const href = $(el).attr('href');
            if (href && href.includes('1fichier.com')) {
                // Avoid duplicates
                if (!oneFichierLinks.includes(href)) {
                    oneFichierLinks.push(href);
                }
            }
        });

        // If no 1fichier links found, look for MEGA links
        const megaLinks = [];
        if (oneFichierLinks.length === 0) {
            $('a').each((i, el) => {
                const href = $(el).attr('href');
                if (href && (href.includes('mega.nz') || href.includes('mega.co.nz'))) {
                    // Avoid duplicates
                    if (!megaLinks.includes(href)) {
                        megaLinks.push(href);
                    }
                }
            });
        }

        // If still no links found, look for any links in the entry-content that might be download links
        if (oneFichierLinks.length === 0 && megaLinks.length === 0) {
            // Look for links in paragraphs that contain text like "Download", "Link", etc.
            $('.entry-content p').each((i, el) => {
                const text = $(el).text().toLowerCase();
                if (text.includes('download') || text.includes('link') || text.includes('part') || text.includes('file')) {
                    $(el).find('a').each((j, link) => {
                        const href = $(link).attr('href');
                        if (href && !href.includes('gameginie.com') && !href.includes('javascript:') && !href.startsWith('#')) {
                            // Check if it's a hosting site
                            if (href.includes('mediafire.com') ||
                                href.includes('drive.google.com') ||
                                href.includes('zippyshare.com') ||
                                href.includes('uploadhaven.com') ||
                                href.includes('rapidgator.net')) {
                                megaLinks.push(href);
                            }
                        }
                    });
                }
            });
        }

        // Determine which links to return
        const links = oneFichierLinks.length > 0 ? oneFichierLinks : megaLinks;
        const hostingType = oneFichierLinks.length > 0 ? '1fichier' : (megaLinks.length > 0 ? 'MEGA/Other' : 'unknown');

        // Extract image URL if available
        let imageUrl = $('.td-post-featured-image img').attr('src') || $('.entry-content img').first().attr('src');
        if (imageUrl) {
            imageUrl = toAbsoluteUrl(imageUrl);
        }

        return {
            title,
            links,
            hostingType,
            imageUrl
        };
    } catch (error) {
        console.error('[GameGinie Service] Error getting download links:', error);
        return {
            error: `An error occurred while getting download links: ${error.message}`
        };
    }
}
