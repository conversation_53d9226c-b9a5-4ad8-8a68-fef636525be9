/**
 * Authentication utilities for server-side
 */

import jwt from 'jsonwebtoken';
import { AUTH_CONFIG } from '$lib/config/authConfig';

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
    '/api/auth/login',
    '/api/discord/config',
    '/api/discord/bot',
    '/api/steamgriddb',
    '/api/omdb',
    '/api/igdb',
    '/api/health',
    '/proxy',
    '/api/pc/gamedrive/search',
    '/api/video/extract', // Added video extraction endpoint
    '/api/image/proxy' // Added image proxy endpoint
];

/**
 * Verify JWT token
 * @param {string} token JWT token
 * @returns {object|null} Decoded token payload or null if invalid
 */
export function verifyToken(token) {
    try {
        return jwt.verify(token, AUTH_CONFIG.JWT_SECRET);
    } catch (error) {
        console.error('Token verification error:', error.message);
        return null;
    }
}

/**
 * Check if a route is public (doesn't require authentication)
 * @param {string} path Route path
 * @returns {boolean} True if route is public
 */
export function isPublicRoute(path) {
    return PUBLIC_ROUTES.some(route => path.startsWith(route));
}

/**
 * Extract token from request headers
 * @param {Request} request Request object
 * @returns {string|null} JWT token or null if not found
 */
export function extractToken(request) {
    const authHeader = request.headers.get('authorization');

    if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
    }

    return null;
}

/**
 * Authenticate request
 * @param {Request} request Request object
 * @returns {boolean} True if authenticated
 */
export function authenticateRequest(request) {
    const token = extractToken(request);

    if (!token) {
        return false;
    }

    const decoded = verifyToken(token);
    return !!decoded;
}
