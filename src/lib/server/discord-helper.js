/**
 * Helper functions for Discord bot integration with Prosiaczek API
 */

import { addMagnet, unrestrictLink, selectFiles, waitForTorrentLinks } from './debrid-service.js';
import { searchPcGames } from './pc-search-discord.js';

/**
 * Search for PC games using our PC search implementation
 * @param {string} query Search query
 * @param {number} maxResults Maximum number of results to return (default: 5)
 * @returns {Promise<Array>} Search results
 */
export async function searchGamesForDiscord(query, maxResults = 5) {
    try {
        console.log(`[Discord Helper] Searching for games with query: ${query}`);

        // Use our PC search implementation
        const results = await searchPcGames(query, {
            maxResults: maxResults,
            sources: {
                fitgirl: true,
                dodi: true,
                gog: true,
                gamedrive: true
            }
        });

        console.log(`[Discord Helper] Found ${results.length} results for query "${query}"`);
        return results;
    } catch (error) {
        console.error('[Discord Helper] Error searching games:', error);

        // If there's an error, return placeholder results
        console.log('[Discord Helper] Returning placeholder results');

        // Placeholder results (limited to maxResults)
        const placeholderResults = [
            {
                title: `${query} - Ultimate Edition`,
                description: 'Pełna wersja gry ze wszystkimi DLC',
                source: 'GameDrive',
                links: [
                    { name: 'Link 1', url: 'https://example.com/game1' },
                    { name: 'Link 2', url: 'https://example.com/game1-mirror' }
                ]
            },
            {
                title: `${query} - Standard Edition`,
                description: 'Podstawowa wersja gry',
                source: 'FitGirl',
                links: [
                    { name: 'Torrent', url: 'magnet:?xt=urn:btih:EXAMPLE' },
                    { name: 'Direct Download', url: 'https://example.com/game2' }
                ]
            },
            {
                title: `${query} 2 - Sequel`,
                description: 'Kontynuacja popularnej gry',
                source: 'DODI',
                links: [
                    { name: 'Torrent', url: 'magnet:?xt=urn:btih:EXAMPLE2' },
                    { name: 'Google Drive', url: 'https://drive.google.com/example' }
                ]
            }
        ].slice(0, maxResults);

        return placeholderResults;
    }
}

/**
 * Process game links and add them to Real-Debrid
 * @param {Object} game Game object with links
 * @returns {Promise<Array>} Array of processed links with download URLs
 */
export async function processGameLinks(game) {
    try {
        console.log(`[Discord Helper] Processing links for game: ${game.title}`);

        if (!game.links || game.links.length === 0) {
            console.log('[Discord Helper] No links found for game');
            return [];
        }

        const processedLinks = [];

        // First, look for torrent links specifically
        const torrentLinks = game.links.filter(link =>
            link.name.toLowerCase().includes('torrent') ||
            link.url.toLowerCase().includes('torrent') ||
            link.url.startsWith('magnet:')
        );

        console.log(`[Discord Helper] Found ${torrentLinks.length} torrent links`);

        // If we have torrent links, prioritize those
        if (torrentLinks.length > 0) {
            // Process each torrent link
            for (const link of torrentLinks) {
                try {
                    console.log(`[Discord Helper] Processing torrent link: ${link.name} - ${link.url}`);

                    // Check if it's a magnet link
                    if (link.url.startsWith('magnet:')) {
                        console.log('[Discord Helper] Processing magnet link');

                        // Add magnet to Real-Debrid
                        const magnetResult = await addMagnet(link.url);

                        // Select all files in the torrent
                        await selectFiles(magnetResult.id);

                        // Informuj użytkownika, że czekamy na przetworzenie torrentu
                        processedLinks.push({
                            name: link.name,
                            originalUrl: link.url,
                            type: 'magnet',
                            status: 'processing',
                            id: magnetResult.id,
                            message: 'Czekam na przetworzenie torrentu przez Real-Debrid...'
                        });

                        try {
                            // Czekaj na przetworzenie torrentu i pobierz linki (maksymalnie 2 minuty)
                            const downloadLinks = await waitForTorrentLinks(magnetResult.id, 120000, 5000);

                            if (downloadLinks && downloadLinks.length > 0) {
                                // Usuń poprzedni wpis o przetwarzaniu
                                processedLinks.pop();

                                // Dodaj każdy link jako osobny wpis
                                downloadLinks.forEach(link => {
                                    processedLinks.push({
                                        name: link.filename || 'Plik',
                                        originalUrl: link.url,
                                        type: 'direct',
                                        status: 'unrestricted',
                                        downloadUrl: link.url,
                                        filename: link.filename,
                                        filesize: link.filesize
                                    });
                                });
                            } else {
                                // Jeśli nie znaleziono linków, zaktualizuj status
                                processedLinks[processedLinks.length - 1].status = 'added';
                                processedLinks[processedLinks.length - 1].message = 'Torrent dodany do Real-Debrid, ale nie znaleziono linków do pobrania.';
                            }
                        } catch (waitError) {
                            console.error(`[Discord Helper] Error waiting for torrent links: ${waitError.message}`);

                            // Jeśli wystąpił błąd podczas oczekiwania, zaktualizuj status
                            processedLinks[processedLinks.length - 1].status = 'added';
                            processedLinks[processedLinks.length - 1].message = 'Torrent dodany do Real-Debrid. Sprawdź zakładkę "Chlewik" w aplikacji, aby pobrać pliki.';
                        }
                    } else if (link.url.toLowerCase().includes('.torrent')) {
                        // It's a direct torrent file link
                        console.log('[Discord Helper] Processing torrent file link');

                        // For now, just add it as a direct link
                        processedLinks.push({
                            name: link.name,
                            originalUrl: link.url,
                            type: 'torrent',
                            status: 'direct',
                            downloadUrl: link.url,
                            message: 'Pobierz plik torrent i dodaj go ręcznie do Real-Debrid'
                        });
                    } else {
                        // It might be a page with a torrent
                        console.log('[Discord Helper] Processing potential torrent page link');

                        processedLinks.push({
                            name: link.name,
                            originalUrl: link.url,
                            type: 'torrent_page',
                            status: 'direct',
                            downloadUrl: link.url,
                            message: 'Otwórz stronę, pobierz plik torrent i dodaj go ręcznie do Real-Debrid'
                        });
                    }
                } catch (error) {
                    console.error(`[Discord Helper] Error processing torrent link ${link.url}:`, error);

                    processedLinks.push({
                        name: link.name,
                        originalUrl: link.url,
                        type: 'torrent',
                        status: 'error',
                        error: error.message
                    });
                }
            }
        } else {
            // No torrent links found, try to process direct links
            console.log('[Discord Helper] No torrent links found, trying direct links');

            // For GameDrive links, we'll just provide them directly
            for (const link of game.links) {
                try {
                    console.log(`[Discord Helper] Processing direct link: ${link.name} - ${link.url}`);

                    // For GameDrive links (crypt.cybar.xyz, etc.), just provide them directly
                    if (link.url.includes('cybar.xyz') ||
                        link.url.includes('crypt.') ||
                        link.url.includes('txtlink.')) {

                        processedLinks.push({
                            name: link.name,
                            originalUrl: link.url,
                            type: 'gamedrive',
                            status: 'direct',
                            downloadUrl: link.url,
                            message: 'Link z GameDrive - otwórz w przeglądarce i pobierz plik'
                        });
                    } else {
                        // Try to unrestrict the link
                        try {
                            const unrestrictedLink = await unrestrictLink(link.url);

                            processedLinks.push({
                                name: link.name,
                                originalUrl: link.url,
                                type: 'direct',
                                status: 'unrestricted',
                                downloadUrl: unrestrictedLink.download,
                                filename: unrestrictedLink.filename
                            });
                        } catch (unrestrictError) {
                            // If unrestricting fails, just provide the direct link
                            processedLinks.push({
                                name: link.name,
                                originalUrl: link.url,
                                type: 'direct',
                                status: 'direct',
                                downloadUrl: link.url,
                                message: 'Link bezpośredni - otwórz w przeglądarce'
                            });
                        }
                    }
                } catch (error) {
                    console.error(`[Discord Helper] Error processing direct link ${link.url}:`, error);

                    processedLinks.push({
                        name: link.name,
                        originalUrl: link.url,
                        type: 'direct',
                        status: 'error',
                        error: error.message
                    });
                }
            }
        }

        console.log(`[Discord Helper] Processed ${processedLinks.length} links for game: ${game.title}`);
        return processedLinks;
    } catch (error) {
        console.error('[Discord Helper] Error processing game links:', error);
        throw error;
    }
}

/**
 * Format file size in bytes to human-readable format
 * @param {number} bytes File size in bytes
 * @returns {string} Formatted file size
 */
function formatFileSize(bytes) {
    if (!bytes || isNaN(bytes)) return 'Nieznany rozmiar';

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * Format processed links for Discord message
 * @param {Array} processedLinks Array of processed links
 * @param {string} gameTitle Game title
 * @returns {string} Formatted message for Discord
 */
export function formatLinksForDiscord(processedLinks, gameTitle) {
    try {
        if (!processedLinks || processedLinks.length === 0) {
            return `Nie znaleziono linków do pobrania dla ${gameTitle}.`;
        }

        let message = `Linki do pobrania dla ${gameTitle}:\n\n`;

        // Prioritize links by type: magnet > torrent > direct > error
        const sortedLinks = [...processedLinks].sort((a, b) => {
            const typeOrder = {
                'magnet': 0,
                'torrent': 1,
                'torrent_page': 2,
                'direct': 3,
                'gamedrive': 4,
                'unknown': 5
            };

            return (typeOrder[a.type] || 99) - (typeOrder[b.type] || 99);
        });

        // Limit the number of links to display to avoid Discord's message length limit
        const maxLinksToDisplay = 5;
        const linksToDisplay = sortedLinks.slice(0, maxLinksToDisplay);

        linksToDisplay.forEach((link, index) => {
            message += `Link ${index + 1}: `;

            if (link.status === 'unrestricted' && link.downloadUrl) {
                message += `${link.downloadUrl}\n`;
                if (link.filename) {
                    // Truncate filename if it's too long
                    const maxFilenameLength = 50;
                    const filename = link.filename.length > maxFilenameLength
                        ? link.filename.substring(0, maxFilenameLength) + '...'
                        : link.filename;
                    message += `Nazwa pliku: ${filename}\n`;
                }
                if (link.filesize) {
                    // Format filesize
                    const formattedSize = formatFileSize(link.filesize);
                    message += `Rozmiar: ${formattedSize}\n`;
                }
            } else if (link.status === 'processing' && link.id) {
                message += `Przetwarzanie... (ID: ${link.id})\n`;
                if (link.message) {
                    message += `${link.message}\n`;
                }
            } else if (link.status === 'added' && link.id) {
                message += `Dodano do Real-Debrid (ID: ${link.id})\n`;
                if (link.message) {
                    message += `${link.message}\n`;
                } else {
                    message += `Sprawdź zakładkę "Chlewik" w aplikacji, aby pobrać pliki.\n`;
                }
            } else if (link.status === 'direct' && link.downloadUrl) {
                message += `${link.downloadUrl}\n`;
                if (link.message) {
                    message += `${link.message}\n`;
                }
                if (link.type === 'gamedrive') {
                    message += `To jest link z GameDrive - otwórz w przeglądarce i pobierz plik.\n`;
                } else if (link.type === 'torrent') {
                    message += `To jest plik torrent - pobierz i dodaj do Real-Debrid ręcznie.\n`;
                } else if (link.type === 'torrent_page') {
                    message += `To jest strona z torrentem - otwórz, pobierz plik torrent i dodaj do Real-Debrid ręcznie.\n`;
                }
            } else if (link.status === 'error') {
                message += `Błąd - ${link.error || 'Nieznany błąd'}\n`;
            } else {
                message += `${link.originalUrl}\n`;
                if (link.message) {
                    message += `${link.message}\n`;
                }
            }

            message += '\n';
        });

        // Add a note if there are more links than we're displaying
        if (processedLinks.length > maxLinksToDisplay) {
            message += `...i ${processedLinks.length - maxLinksToDisplay} więcej linków. Sprawdź zakładkę "Chlewik" w aplikacji, aby zobaczyć wszystkie.\n`;
        }

        // Add a note about torrent links
        const hasProcessingLinks = processedLinks.some(link => link.status === 'processing');
        const hasTorrentLinks = processedLinks.some(link =>
            link.type === 'torrent' || link.type === 'torrent_page' || link.type === 'magnet'
        );

        if (hasProcessingLinks) {
            message += `\nUwaga: Niektóre torrenty są nadal przetwarzane. Poczekaj chwilę, a bot wyśle Ci gotowe linki do pobrania.\n`;
        } else if (hasTorrentLinks) {
            message += `\nUwaga: Linki torrent mogą wymagać ręcznego dodania do Real-Debrid.\n`;
        }

        // Check if the message is too long for Discord (2000 character limit)
        if (message.length > 1900) {
            console.log(`[Discord Helper] Message too long (${message.length} chars), truncating...`);
            message = message.substring(0, 1900) + '...';
        }

        return message;
    } catch (error) {
        console.error('[Discord Helper] Error formatting links for Discord:', error);
        return `Wystąpił błąd podczas formatowania linków: ${error.message}`;
    }
}
