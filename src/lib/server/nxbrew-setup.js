/**
 * This module handles the setup and installation of nxbrew-dl
 * It provides functions to check if nxbrew-dl is installed and install it if needed
 *
 * IMPORTANT: This module should only be imported dynamically at runtime,
 * never during build time to prevent the GUI from opening.
 */

import fs from 'fs/promises';
import path from 'path';

// Only import child_process and util in a server environment
const isServer = typeof window === 'undefined';
let execAsync;

// Dynamically initialize execAsync only in a server environment
if (isServer) {
  const { exec } = await import('child_process');
  const { promisify } = await import('util');
  execAsync = promisify(exec);
} else {
  // Mock implementation for client-side
  execAsync = async () => ({ stdout: '', stderr: 'Not available in browser' });
}

/**
 * Check if nxbrew-dl is installed
 * @returns {Promise<boolean>} True if installed, false otherwise
 */
export async function isNxbrewDlInstalled() {
    // Skip all checks during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew-dl check in non-server environment');
        return false;
    }

    try {
        // We'll use a simpler approach that doesn't try to import nxbrew-dl directly
        // This prevents the GUI from opening during checks

        // Check if the Python script exists (our custom implementation)
        try {
            const scriptPath = path.join(process.cwd(), 'nxbrew_search.py');
            await fs.access(scriptPath);
            console.log('nxbrew_search.py script found, assuming functionality is available');
            return true;
        } catch (fileError) {
            console.log('nxbrew_search.py script not found:', fileError.message);
        }

        // Only if the script doesn't exist, try a simple pip list check
        try {
            const { stdout } = await execAsync('pip list | grep nxbrew-dl');
            if (stdout && stdout.includes('nxbrew-dl')) {
                console.log('nxbrew-dl found using pip list');
                return true;
            }
        } catch (pipError) {
            console.log('pip list check failed:', pipError.message);
        }

        // If we get here, nxbrew-dl is not installed or not properly configured
        console.log('nxbrew-dl not found');
        return false;
    } catch (error) {
        console.error('Error checking if nxbrew-dl is installed:', error);
        return false;
    }
}

/**
 * Install nxbrew-dl
 * @returns {Promise<{success: boolean, message: string}>} Result of installation
 */
export async function installNxbrewDl() {
    // Skip installation during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew-dl installation in non-server environment');
        return {
            success: false,
            message: 'Cannot install nxbrew-dl in non-server environment'
        };
    }

    try {
        console.log('Installing nxbrew-dl...');

        // Try pip first
        try {
            const { stdout, stderr } = await execAsync('pip install nxbrew-dl');
            console.log('pip install output:', stdout);

            if (stderr) {
                console.log('pip install stderr:', stderr);
            }

            // Check if installation was successful
            const installed = await isNxbrewDlInstalled();
            if (installed) {
                console.log('nxbrew-dl installed successfully');
                return { success: true, message: 'nxbrew-dl installed successfully' };
            }
        } catch (pipError) {
            console.error('Error installing with pip:', pipError);
        }

        // If pip failed, try pip3
        try {
            const { stdout, stderr } = await execAsync('pip3 install nxbrew-dl');
            console.log('pip3 install output:', stdout);

            if (stderr) {
                console.log('pip3 install stderr:', stderr);
            }

            // Check if installation was successful
            const installed = await isNxbrewDlInstalled();
            if (installed) {
                console.log('nxbrew-dl installed successfully using pip3');
                return { success: true, message: 'nxbrew-dl installed successfully using pip3' };
            }
        } catch (pip3Error) {
            console.error('Error installing with pip3:', pip3Error);
        }

        // If all methods failed
        return {
            success: false,
            message: 'Failed to install nxbrew-dl using pip and pip3. Please install manually.'
        };
    } catch (error) {
        console.error('Error in installNxbrewDl function:', error);
        return {
            success: false,
            message: `Failed to install nxbrew-dl: ${error.message}`,
            debug: {
                error: error.toString(),
                stack: error.stack
            }
        };
    }
}

/**
 * Ensure nxbrew-dl is installed
 * @returns {Promise<{success: boolean, message: string}>} Result of installation check
 */
export async function ensureNxbrewDlInstalled() {
    // Skip during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew-dl installation check in non-server environment');
        return {
            success: false,
            message: 'Cannot check nxbrew-dl installation in non-server environment'
        };
    }

    const installed = await isNxbrewDlInstalled();

    if (installed) {
        return { success: true, message: 'nxbrew-dl is already installed' };
    }

    return await installNxbrewDl();
}

/**
 * Create nxbrew-dl configuration file
 * @param {Object} config Configuration object
 * @returns {Promise<{success: boolean, message: string}>} Result of configuration
 */
export async function configureNxbrewDl(config = {}) {
    // Skip during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew-dl configuration in non-server environment');
        return {
            success: false,
            message: 'Cannot configure nxbrew-dl in non-server environment'
        };
    }

    try {
        // Default configuration
        const defaultConfig = {
            download_dir: '/Users/<USER>/Downloads',
            preferred_format: 'NSP',
            download_updates: true,
            download_dlc: true,
            preferred_regions: ['USA', 'EUR'],
            preferred_languages: ['English'],
            base_url: 'https://nxbrew.net/',
            jdownloader: {
                enabled: true,
                device_name: '',
                username: '',
                password: ''
            }
        };

        // Merge with provided config
        const finalConfig = { ...defaultConfig, ...config };

        // Create config directory if it doesn't exist
        const configDir = path.join(process.cwd(), '.config', 'nxbrew-dl');
        await fs.mkdir(configDir, { recursive: true });

        // Write config file
        const configPath = path.join(configDir, 'config.json');
        await fs.writeFile(configPath, JSON.stringify(finalConfig, null, 2));

        return { success: true, message: 'nxbrew-dl configured successfully' };
    } catch (error) {
        console.error('Error configuring nxbrew-dl:', error);
        return { success: false, message: `Failed to configure nxbrew-dl: ${error.message}` };
    }
}

/**
 * Initialize nxbrew-dl (install and configure)
 * @param {Object} config Configuration object
 * @returns {Promise<{success: boolean, message: string}>} Result of initialization
 */
export async function initializeNxbrewDl(config = {}) {
    // Skip during build time or in browser environment
    if (!isServer) {
        console.log('Skipping nxbrew-dl initialization in non-server environment');
        return {
            success: false,
            message: 'Cannot initialize nxbrew-dl in non-server environment'
        };
    }

    const installResult = await ensureNxbrewDlInstalled();

    if (!installResult.success) {
        return installResult;
    }

    return await configureNxbrewDl(config);
}
