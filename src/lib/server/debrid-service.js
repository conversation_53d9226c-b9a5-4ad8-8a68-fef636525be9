/**
 * Server-side version of the debridService for use with Discord bot
 * Handles torrent management, link unrestriction, and related functionality
 */

// Import https for Node.js environments
import https from 'https';
import { URL } from 'url';
import { REAL_DEBRID_CONFIG } from '../config/apiConfig.js';

// Get API configuration from centralized config
const { API_KEY, BASE_URL: API_BASE_URL } = REAL_DEBRID_CONFIG;

// Helper function to make HTTPS requests
function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const parsedUrl = new URL(url);

        const requestOptions = {
            hostname: parsedUrl.hostname,
            path: parsedUrl.pathname + parsedUrl.search,
            method: options.method || 'GET',
            headers: options.headers || {}
        };

        const req = https.request(requestOptions, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    try {
                        const jsonData = data ? JSON.parse(data) : {};
                        resolve({
                            ok: true,
                            status: res.statusCode,
                            json: () => Promise.resolve(jsonData),
                            text: () => Promise.resolve(data)
                        });
                    } catch (error) {
                        resolve({
                            ok: true,
                            status: res.statusCode,
                            json: () => Promise.reject(new Error('Invalid JSON')),
                            text: () => Promise.resolve(data)
                        });
                    }
                } else {
                    resolve({
                        ok: false,
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        text: () => Promise.resolve(data)
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        if (options.body) {
            req.write(options.body);
        }

        req.end();
    });
}

// Helper function for API requests with error handling
async function fetchWithErrorHandling(url, options = {}) {
    try {
        console.log(`[Debrid Service] Making request to: ${url}`);
        const response = await makeRequest(url, options);

        if (!response.ok) {
            let errorMessage = `HTTP error ${response.status}`;
            try {
                const errorData = await response.text();
                errorMessage += `: ${errorData}`;
            } catch (e) {
                // Ignore error parsing error
            }
            throw new Error(errorMessage);
        }

        return response;
    } catch (error) {
        console.error(`[Debrid Service] API request failed: ${url}`, error);
        throw error;
    }
}

/**
 * Add a magnet link to Real-Debrid
 * @param {string} magnetUrl Magnet URL to add
 * @returns {Promise<Object>} Response with torrent ID
 */
export async function addMagnet(magnetUrl) {
    try {
        console.log(`[Debrid Service] Adding magnet: ${magnetUrl.substring(0, 50)}...`);

        const body = `magnet=${encodeURIComponent(magnetUrl)}`;
        const response = await makeRequest(`${API_BASE_URL}/torrents/addMagnet`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(body)
            },
            body: body
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("[Debrid Service] API Error:", errorText);
            throw new Error(`Error: ${response.status}`);
        }

        const result = await response.json();
        console.log(`[Debrid Service] Magnet added successfully, ID: ${result.id}`);
        return result;
    } catch (error) {
        console.error('[Debrid Service] Error adding magnet link:', error);
        throw error;
    }
}

/**
 * Select all files in a torrent
 * @param {string} torrentId Torrent ID
 * @returns {Promise<void>}
 */
export async function selectFiles(torrentId) {
    try {
        console.log(`[Debrid Service] Selecting files for torrent: ${torrentId}`);

        // Add delay to ensure torrent is properly registered
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Get torrent info
        const infoResponse = await makeRequest(`${API_BASE_URL}/torrents/info/${torrentId}`, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`
            }
        });

        if (!infoResponse.ok) {
            throw new Error(`Error: ${infoResponse.status}`);
        }

        const infoData = await infoResponse.json();
        console.log(`[Debrid Service] Torrent info received: ${infoData.filename}`);

        // If no files found, retry after delay
        if (!infoData.files || infoData.files.length === 0) {
            console.log("[Debrid Service] No files found in torrent, waiting and retrying...");
            await new Promise(resolve => setTimeout(resolve, 2000));
            return selectFiles(torrentId);
        }

        // Select all files
        const body = 'files=all';
        const selectResponse = await makeRequest(`${API_BASE_URL}/torrents/selectFiles/${torrentId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(body)
            },
            body: body
        });

        if (!selectResponse.ok) {
            const errorText = await selectResponse.text();
            console.error("[Debrid Service] File selection failed:", errorText);
            throw new Error(`Error: ${selectResponse.status}`);
        }

        console.log(`[Debrid Service] Files selected successfully for torrent: ${torrentId}`);
        return;
    } catch (error) {
        console.error('[Debrid Service] Error selecting files:', error);
        throw error;
    }
}

/**
 * Unrestrict a link (get direct download URL)
 * @param {string} link Link to unrestrict
 * @returns {Promise<Object>} Unrestricted link data
 */
export async function unrestrictLink(link) {
    try {
        console.log(`[Debrid Service] Unrestricting link: ${link}`);

        const body = `link=${encodeURIComponent(link)}`;
        const response = await makeRequest(`${API_BASE_URL}/unrestrict/link`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/x-www-form-urlencoded',
                'Content-Length': Buffer.byteLength(body)
            },
            body: body
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("[Debrid Service] API Error:", errorText);
            throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        console.log(`[Debrid Service] Link unrestricted successfully: ${data.filename}`);

        return data;
    } catch (error) {
        console.error('[Debrid Service] Error unrestricting link:', error);
        throw error;
    }
}

/**
 * Extract filename from URL
 * @param {string} url URL to extract filename from
 * @returns {string|null} Extracted filename or null
 */
export function extractFilename(url) {
    try {
        const parsedUrl = new URL(url);
        const pathname = parsedUrl.pathname;
        const segments = pathname.split('/');
        const lastSegment = segments[segments.length - 1];

        // If last segment is empty or doesn't contain a dot, return null
        if (!lastSegment || !lastSegment.includes('.')) {
            return null;
        }

        return decodeURIComponent(lastSegment);
    } catch (e) {
        return null;
    }
}

/**
 * Get torrent info from Real-Debrid
 * @param {string} torrentId Torrent ID
 * @returns {Promise<Object>} Torrent info
 */
export async function getTorrentInfo(torrentId) {
    try {
        console.log(`[Debrid Service] Getting torrent info for: ${torrentId}`);

        const response = await makeRequest(`${API_BASE_URL}/torrents/info/${torrentId}`, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("[Debrid Service] API Error:", errorText);
            throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        console.log(`[Debrid Service] Torrent info received: ${data.filename}, status: ${data.status}`);

        return data;
    } catch (error) {
        console.error('[Debrid Service] Error getting torrent info:', error);
        throw error;
    }
}

/**
 * Wait for torrent to be ready and get download links
 * @param {string} torrentId Torrent ID
 * @param {number} maxWaitTime Maximum wait time in milliseconds (default: 60000 - 1 minute)
 * @param {number} checkInterval Check interval in milliseconds (default: 5000 - 5 seconds)
 * @returns {Promise<Array>} Array of download links
 */
export async function waitForTorrentLinks(torrentId, maxWaitTime = 60000, checkInterval = 5000) {
    try {
        console.log(`[Debrid Service] Waiting for torrent ${torrentId} to be ready...`);

        const startTime = Date.now();
        let downloadLinks = [];

        // Loop until we get links or timeout
        while (Date.now() - startTime < maxWaitTime) {
            // Get torrent info
            const torrentInfo = await getTorrentInfo(torrentId);

            // Check if torrent is ready
            if (torrentInfo.status === 'downloaded') {
                console.log(`[Debrid Service] Torrent ${torrentId} is ready!`);

                // Get download links
                if (torrentInfo.links && torrentInfo.links.length > 0) {
                    console.log(`[Debrid Service] Found ${torrentInfo.links.length} links for torrent ${torrentId}`);

                    // Process each link
                    const processedLinks = [];
                    for (const link of torrentInfo.links) {
                        try {
                            // Unrestrict the link
                            const unrestrictedLink = await unrestrictLink(link);

                            processedLinks.push({
                                url: unrestrictedLink.download,
                                filename: unrestrictedLink.filename,
                                filesize: unrestrictedLink.filesize,
                                host: unrestrictedLink.host
                            });
                        } catch (error) {
                            console.error(`[Debrid Service] Error unrestricting link ${link}:`, error);
                        }
                    }

                    return processedLinks;
                } else {
                    console.log(`[Debrid Service] No links found for torrent ${torrentId}`);
                    return [];
                }
            } else if (torrentInfo.status === 'error' || torrentInfo.status === 'magnet_error') {
                console.error(`[Debrid Service] Torrent ${torrentId} has error status: ${torrentInfo.status}`);
                throw new Error(`Torrent error: ${torrentInfo.status}`);
            } else {
                // Torrent is still processing, wait and check again
                console.log(`[Debrid Service] Torrent ${torrentId} status: ${torrentInfo.status}, progress: ${torrentInfo.progress || 0}%, waiting...`);
                await new Promise(resolve => setTimeout(resolve, checkInterval));
            }
        }

        // If we get here, we timed out
        console.log(`[Debrid Service] Timeout waiting for torrent ${torrentId}`);
        throw new Error('Timeout waiting for torrent');
    } catch (error) {
        console.error('[Debrid Service] Error waiting for torrent links:', error);
        throw error;
    }
}
