/**
 * English translations
 */

export default {
    // Common
    'app.name': '<PERSON>siaczek <PERSON>bieracze<PERSON>',
    'app.description': 'Download manager for torrents and direct links',
    
    // Navigation
    'nav.home': 'Home',
    'nav.torrents': 'Torrents',
    'nav.links': 'Links',
    'nav.pc': 'PC Games',
    'nav.switch': 'Switch Games',
    'nav.settings': 'Settings',
    
    // Actions
    'action.search': 'Search',
    'action.download': 'Download',
    'action.add': 'Add',
    'action.cancel': 'Cancel',
    'action.close': 'Close',
    'action.save': 'Save',
    'action.delete': 'Delete',
    'action.edit': 'Edit',
    'action.copy': 'Copy',
    'action.refresh': 'Refresh',
    'action.retry': 'Retry',
    'action.showMore': 'Show more',
    'action.loadMore': 'Load more',
    'action.visitWebsite': 'Visit website',
    'action.addTorrent': 'Add torrent',
    
    // PC Games Search
    'pc.title': 'Search PC Games',
    'pc.description': 'Search and download PC games from FitGirl, DODI, GameDrive or GOG',
    'pc.searchPlaceholder': 'Enter game title (min. 3 characters)...',
    'pc.noResults': 'No results found for "{query}"',
    'pc.minChars': 'Enter at least 3 characters to start searching',
    'pc.startTyping': 'Enter game title to start searching',
    'pc.loading': 'Loading data... {progress}%',
    'pc.searching': 'Searching...',
    'pc.error': 'An error occurred while searching',
    'pc.tryAgain': 'Try again',
    'pc.sources': 'Sources',
    'pc.size': 'Size',
    'pc.date': 'Date',
    'pc.results': 'Found {count} results',
    
    // Switch Games
    'switch.title': 'Search Switch Games',
    'switch.description': 'Search and download Nintendo Switch games',
    'switch.searchPlaceholder': 'Enter game title...',
    'switch.noResults': 'No Switch games found for "{query}"',
    'switch.loading': 'Loading Switch games...',
    
    // Torrents
    'torrents.title': 'Torrents',
    'torrents.add': 'Add Torrent',
    'torrents.addMagnet': 'Add Magnet Link',
    'torrents.addFile': 'Add Torrent File',
    'torrents.status': 'Status',
    'torrents.progress': 'Progress',
    'torrents.size': 'Size',
    'torrents.name': 'Name',
    'torrents.added': 'Added',
    'torrents.completed': 'Completed',
    'torrents.downloadSpeed': 'Download Speed',
    'torrents.uploadSpeed': 'Upload Speed',
    'torrents.eta': 'ETA',
    'torrents.peers': 'Peers',
    'torrents.seeds': 'Seeds',
    'torrents.ratio': 'Ratio',
    'torrents.category': 'Category',
    'torrents.actions': 'Actions',
    'torrents.noTorrents': 'No torrents found',
    'torrents.dropzone': 'Drop torrent files here or click to select',
    
    // Links
    'links.title': 'Direct Links',
    'links.add': 'Add Links',
    'links.unrestrict': 'Unrestrict Links',
    'links.paste': 'Paste links here (one per line)',
    'links.noLinks': 'No links found',
    
    // Discord Bot
    'discord.title': 'Discord Bot Settings',
    'discord.apiKey': 'Bot Token',
    'discord.clientId': 'Client ID',
    'discord.clientSecret': 'Client Secret',
    'discord.channelId': 'Channel ID',
    'discord.enabled': 'Enable Discord Bot',
    'discord.autoAddToDebrid': 'Automatically add to Real-Debrid',
    'discord.returnLinks': 'Return download links on Discord',
    'discord.status': 'Bot Status',
    'discord.active': 'Bot Active',
    'discord.inactive': 'Bot Inactive',
    'discord.start': 'Start Bot',
    'discord.stop': 'Stop Bot',
    'discord.refresh': 'Refresh Status',
    'discord.saving': 'Saving...',
    
    // Settings
    'settings.title': 'Settings',
    'settings.theme': 'Theme',
    'settings.language': 'Language',
    'settings.clearCache': 'Clear Cache',
    'settings.resetSettings': 'Reset Settings',
    'settings.about': 'About',
    'settings.version': 'Version',
    
    // Errors
    'error.generic': 'An error occurred',
    'error.loading': 'Error loading data',
    'error.search': 'Error searching',
    'error.download': 'Error downloading',
    'error.api': 'API Error',
    'error.network': 'Network Error',
    'error.unauthorized': 'Unauthorized',
    'error.notFound': 'Not Found',
    'error.serverError': 'Server Error',
    
    // Success
    'success.added': 'Added successfully',
    'success.saved': 'Saved successfully',
    'success.deleted': 'Deleted successfully',
    'success.downloaded': 'Downloaded successfully',
    
    // Misc
    'misc.loading': 'Loading...',
    'misc.processing': 'Processing...',
    'misc.waitingForSelection': 'Waiting for file selection',
    'misc.queued': 'Queued',
    'misc.downloading': 'Downloading',
    'misc.downloaded': 'Downloaded',
    'misc.error': 'Error',
    'misc.dead': 'Dead',
    'misc.magnetError': 'Magnet Error',
    'misc.ready': 'Ready',
    'misc.unknown': 'Unknown',
    'misc.noCover': 'No Cover',
    'misc.noDescription': 'No description available'
};
