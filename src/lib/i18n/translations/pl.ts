/**
 * Polish translations
 */

export default {
    // Common
    'app.name': 'Prosiaczek Pobieraczek',
    'app.description': 'Menedżer pobierania torrentów i bezpośrednich linków',
    
    // Navigation
    'nav.home': 'Strona główna',
    'nav.torrents': 'Torrenty',
    'nav.links': 'Linki',
    'nav.pc': 'Gry PC',
    'nav.switch': 'Gry Switch',
    'nav.settings': 'Ustawienia',
    
    // Actions
    'action.search': 'Szukaj',
    'action.download': 'Pobierz',
    'action.add': 'Dodaj',
    'action.cancel': 'Anuluj',
    'action.close': 'Zamknij',
    'action.save': 'Zapisz',
    'action.delete': 'Usuń',
    'action.edit': 'Edytuj',
    'action.copy': 'Kopiuj',
    'action.refresh': 'Odśwież',
    'action.retry': 'Spróbuj ponownie',
    'action.showMore': '<PERSON><PERSON><PERSON> więcej',
    'action.loadMore': '<PERSON>aładuj więcej',
    'action.visitWebsite': 'Odwied<PERSON> stronę',
    'action.addTorrent': 'Dodaj torrent',
    
    // PC Games Search
    'pc.title': 'Wyszukiwarka gier PC',
    'pc.description': 'Wyszukuj i pobieraj gry PC z FitGirl, DODI, GameDrive lub GOG',
    'pc.searchPlaceholder': 'Wpisz tytuł gry (min. 3 znaki)...',
    'pc.noResults': 'Nie znaleziono wyników dla "{query}"',
    'pc.minChars': 'Wpisz co najmniej 3 znaki, aby rozpocząć wyszukiwanie',
    'pc.startTyping': 'Wpisz tytuł gry, aby rozpocząć wyszukiwanie',
    'pc.loading': 'Ładowanie danych... {progress}%',
    'pc.searching': 'Wyszukiwanie...',
    'pc.error': 'Wystąpił błąd podczas wyszukiwania',
    'pc.tryAgain': 'Spróbuj ponownie',
    'pc.sources': 'Źródła',
    'pc.size': 'Rozmiar',
    'pc.date': 'Data',
    'pc.results': 'Znaleziono {count} wyników',
    
    // Switch Games
    'switch.title': 'Wyszukiwarka gier Switch',
    'switch.description': 'Wyszukuj i pobieraj gry na Nintendo Switch',
    'switch.searchPlaceholder': 'Wpisz tytuł gry...',
    'switch.noResults': 'Nie znaleziono gier Switch dla "{query}"',
    'switch.loading': 'Ładowanie gier Switch...',
    
    // Torrents
    'torrents.title': 'Torrenty',
    'torrents.add': 'Dodaj Torrent',
    'torrents.addMagnet': 'Dodaj link Magnet',
    'torrents.addFile': 'Dodaj plik Torrent',
    'torrents.status': 'Status',
    'torrents.progress': 'Postęp',
    'torrents.size': 'Rozmiar',
    'torrents.name': 'Nazwa',
    'torrents.added': 'Dodano',
    'torrents.completed': 'Ukończono',
    'torrents.downloadSpeed': 'Prędkość pobierania',
    'torrents.uploadSpeed': 'Prędkość wysyłania',
    'torrents.eta': 'Pozostały czas',
    'torrents.peers': 'Peery',
    'torrents.seeds': 'Seedy',
    'torrents.ratio': 'Ratio',
    'torrents.category': 'Kategoria',
    'torrents.actions': 'Akcje',
    'torrents.noTorrents': 'Nie znaleziono torrentów',
    'torrents.dropzone': 'Upuść pliki torrent tutaj lub kliknij, aby wybrać',
    
    // Links
    'links.title': 'Bezpośrednie linki',
    'links.add': 'Dodaj linki',
    'links.unrestrict': 'Odblokuj linki',
    'links.paste': 'Wklej linki tutaj (jeden na linię)',
    'links.noLinks': 'Nie znaleziono linków',
    
    // Discord Bot
    'discord.title': 'Ustawienia bota Discord',
    'discord.apiKey': 'Token bota',
    'discord.clientId': 'ID klienta',
    'discord.clientSecret': 'Sekret klienta',
    'discord.channelId': 'ID kanału',
    'discord.enabled': 'Włącz bota Discord',
    'discord.autoAddToDebrid': 'Automatycznie dodawaj do Real-Debrid',
    'discord.returnLinks': 'Zwracaj linki do pobrania na Discord',
    'discord.status': 'Status bota',
    'discord.active': 'Bot aktywny',
    'discord.inactive': 'Bot nieaktywny',
    'discord.start': 'Uruchom bota',
    'discord.stop': 'Zatrzymaj bota',
    'discord.refresh': 'Odśwież status',
    'discord.saving': 'Zapisywanie...',
    
    // Settings
    'settings.title': 'Ustawienia',
    'settings.theme': 'Motyw',
    'settings.language': 'Język',
    'settings.clearCache': 'Wyczyść pamięć podręczną',
    'settings.resetSettings': 'Resetuj ustawienia',
    'settings.about': 'O aplikacji',
    'settings.version': 'Wersja',
    
    // Errors
    'error.generic': 'Wystąpił błąd',
    'error.loading': 'Błąd ładowania danych',
    'error.search': 'Błąd wyszukiwania',
    'error.download': 'Błąd pobierania',
    'error.api': 'Błąd API',
    'error.network': 'Błąd sieci',
    'error.unauthorized': 'Brak autoryzacji',
    'error.notFound': 'Nie znaleziono',
    'error.serverError': 'Błąd serwera',
    
    // Success
    'success.added': 'Dodano pomyślnie',
    'success.saved': 'Zapisano pomyślnie',
    'success.deleted': 'Usunięto pomyślnie',
    'success.downloaded': 'Pobrano pomyślnie',
    
    // Misc
    'misc.loading': 'Ładowanie...',
    'misc.processing': 'Przetwarzanie...',
    'misc.waitingForSelection': 'Oczekiwanie na wybór plików',
    'misc.queued': 'W kolejce',
    'misc.downloading': 'Pobieranie',
    'misc.downloaded': 'Gotowe do pobrania',
    'misc.error': 'Błąd',
    'misc.dead': 'Nieaktywny',
    'misc.magnetError': 'Błąd magnet',
    'misc.ready': 'Gotowy',
    'misc.unknown': 'Nieznany',
    'misc.noCover': 'Brak okładki',
    'misc.noDescription': 'Brak dostępnego opisu'
};
