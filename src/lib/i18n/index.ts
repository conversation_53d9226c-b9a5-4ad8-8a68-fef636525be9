/**
 * Internationalization (i18n) support
 */

import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';

// Available locales
export const locales = ['en', 'pl'] as const;
export type Locale = typeof locales[number];

// Translation dictionaries
import en from './translations/en';
import pl from './translations/pl';

// Translation dictionaries by locale
const dictionaries: Record<Locale, Record<string, string>> = {
    en,
    pl
};

// Detect browser locale
function detectLocale(): Locale {
    if (!browser) return 'en';
    
    // Check localStorage first
    const savedLocale = localStorage.getItem('locale');
    if (savedLocale && locales.includes(savedLocale as Locale)) {
        return savedLocale as Locale;
    }
    
    // Check browser language
    const browserLocale = navigator.language.split('-')[0];
    if (locales.includes(browserLocale as Locale)) {
        return browserLocale as Locale;
    }
    
    // Default to English
    return 'en';
}

// Create locale store
export const locale = writable<Locale>(detectLocale());

// Save locale to localStorage when it changes
if (browser) {
    locale.subscribe(value => {
        localStorage.setItem('locale', value);
    });
}

// Create translation function
export const t = derived(locale, $locale => {
    const dictionary = dictionaries[$locale];
    
    return (key: string, params: Record<string, string | number> = {}): string => {
        let text = dictionary[key] || key;
        
        // Replace parameters
        Object.entries(params).forEach(([param, value]) => {
            text = text.replace(`{${param}}`, String(value));
        });
        
        return text;
    };
});

/**
 * Format date according to locale
 * @param date Date to format
 * @param options Date formatting options
 * @returns Formatted date string
 */
export function formatDate(
    date: Date | string | number,
    options: Intl.DateTimeFormatOptions = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
    }
): string {
    const dateObj = date instanceof Date ? date : new Date(date);
    
    return new Intl.DateTimeFormat(browser ? navigator.language : 'en', options).format(dateObj);
}

/**
 * Format number according to locale
 * @param number Number to format
 * @param options Number formatting options
 * @returns Formatted number string
 */
export function formatNumber(
    number: number,
    options: Intl.NumberFormatOptions = {}
): string {
    return new Intl.NumberFormat(browser ? navigator.language : 'en', options).format(number);
}

/**
 * Format file size
 * @param bytes Size in bytes
 * @param decimals Number of decimal places
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}
