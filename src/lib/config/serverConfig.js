/**
 * Server configuration
 * Contains server settings, proxy configuration, and other server-related values
 */

// Import environment utilities
import { getEnv, getNumEnv } from '$lib/utils/envUtils';

// Server port (from environment variable or default)
export const SERVER_PORT = getNumEnv('SERVER_PORT', 8009);

// Real-Debrid proxy configuration
export const REAL_DEBRID_PROXY_CONFIG = {
    target: 'https://api.real-debrid.com',
    changeOrigin: true,
    pathRewrite: {
        '^/proxy': '/rest/1.0'
    },
    onProxyReq: (proxyReq) => {
        proxyReq.setHeader('Host', 'api.real-debrid.com');
    },
    onProxyRes: (proxyRes) => {
        // Add CORS headers
        proxyRes.headers['Access-Control-Allow-Origin'] = '*';
        proxyRes.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
        proxyRes.headers['Access-Control-Allow-Headers'] = 'Authorization, Content-Type';
        proxyRes.headers['Access-Control-Allow-Credentials'] = 'true';
    }
};

// CORS headers for preflight requests
export const CORS_HEADERS = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Authorization, Content-Type',
    'Access-Control-Allow-Credentials': 'true'
};

// Configuration paths
export const CONFIG_PATHS = {
    NXBREW: '.config/nxbrew-dl/config.json',
    DISCORD: '.config/discord/config.json'
};

// Default download directory
export const DEFAULT_DOWNLOAD_DIR = getEnv('DEFAULT_DOWNLOAD_DIR', '/Users/<USER>/Downloads');

// Default nxbrew configuration
export const DEFAULT_NXBREW_CONFIG = {
    download_dir: getEnv('NXBREW_DOWNLOAD_DIR', DEFAULT_DOWNLOAD_DIR),
    preferred_format: getEnv('NXBREW_PREFERRED_FORMAT', 'NSP'),
    download_updates: true,
    download_dlc: true,
    preferred_regions: ['USA', 'EUR'],
    preferred_languages: ['English'],
    jdownloader: {
        enabled: true,
        device_name: getEnv('JDOWNLOADER_DEVICE_NAME', ''),
        username: getEnv('JDOWNLOADER_USERNAME', ''),
        password: getEnv('JDOWNLOADER_PASSWORD', '')
    }
};

// Default Discord bot configuration
export const DEFAULT_DISCORD_CONFIG = {
    apiKey: getEnv('DISCORD_BOT_TOKEN', ''),
    clientId: getEnv('DISCORD_CLIENT_ID', ''),
    clientSecret: getEnv('DISCORD_CLIENT_SECRET', ''),
    enabled: getEnv('DISCORD_BOT_ENABLED', 'false').toLowerCase() === 'true',
    autoAddToDebrid: true,
    returnLinks: true,
    channelId: getEnv('DISCORD_CHANNEL_ID', '')
};
