/**
 * Authentication configuration
 * Contains authentication settings and credentials
 */

import { getEnv } from '$lib/utils/envUtils';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Authentication configuration
export const AUTH_CONFIG = {
    // Username and password from environment variables
    USERNAME: process.env.AUTH_USERNAME || getEnv('AUTH_USERNAME', ''),
    PASSWORD: process.env.AUTH_PASSWORD || getEnv('AUTH_PASSWORD', ''),
    JWT_SECRET: process.env.JWT_SECRET || getEnv('JWT_SECRET', 'prosiaczek-secret-key'),
    // Token expiration time in seconds (24 hours)
    TOKEN_EXPIRATION: 86400
};

// Log configuration (without exposing sensitive data)
console.log('Auth configuration loaded:', {
    usernameSet: !!AUTH_CONFIG.USERNAME,
    passwordSet: !!AUTH_CONFIG.PASSWORD,
    jwtSecretSet: !!AUTH_CONFIG.JWT_SECRET
});
