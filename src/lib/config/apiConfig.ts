/**
 * Centralized API configuration
 * Contains API keys, base URLs, and other configuration values
 */

import { getEnv } from '$lib/utils/envUtils';

// Real-Debrid API configuration
export const REAL_DEBRID_CONFIG = {
    // Use environment variable with fallback for API key
    API_KEY: getEnv('REAL_DEBRID_API_KEY', 'SCCGSM6NYFDLCQIIAQVK6TW2FOWCEJBNIC2XMCRAC37BR5K3JOPA'),
    // Use proxy for API to bypass CORS restrictions in browser
    // Use direct URL in Node.js environment
    BASE_URL: typeof window !== 'undefined' ? '/proxy' : 'https://api.real-debrid.com/rest/1.0/'
};

// SteamGridDB API configuration
export const STEAMGRIDDB_CONFIG = {
    // Use environment variable with fallback for API key
    API_KEY: getEnv('STEAMGRIDDB_API_KEY', '7cfae18520dd8e35a8f971f8025291d0'),
    BASE_URL: 'https://www.steamgriddb.com/api/v2',
    PROXY_URL: '/api/steamgriddb'
};

// OMDB API configuration
export const OMDB_CONFIG = {
    // Use environment variable with fallback for API key
    API_KEY: getEnv('OMDB_API_KEY', 'c24805a2'),
    BASE_URL: 'http://www.omdbapi.com/',
    PROXY_URL: '/api/omdb'
};

// GiantBomb API configuration
export const GIANTBOMB_CONFIG = {
    // Use environment variable with fallback for API key
    API_KEY: getEnv('GIANTBOMB_API_KEY', '710628dd1c4b13e3ec770cbfe5b26f3a96071349'),
    BASE_URL: 'https://www.giantbomb.com/api',
    PROXY_URL: '/api/giantbomb'
};

// IGDB API configuration
export const IGDB_CONFIG = {
    // Use environment variables with fallbacks
    CLIENT_ID: getEnv('IGDB_CLIENT_ID', 'hfy9ogj9xvy0mhjfshvk2w3dtfvjti'),
    CLIENT_SECRET: getEnv('IGDB_CLIENT_SECRET', 'i8e0ni67igwekuoxddx2yr00df2zk8'),
    BASE_URL: 'https://api.igdb.com/v4',
    AUTH_URL: 'https://id.twitch.tv/oauth2/token',
    PROXY_URL: '/api/igdb'
};

// Trakt.tv API configuration
export const TRAKT_CONFIG = {
    // Use environment variables with fallbacks
    CLIENT_ID: getEnv('TRAKT_CLIENT_ID', '01d887218070ff644527e1fb9524ce51b096fd15faef26fd6c6f60ef7dac7978'),
    CLIENT_SECRET: getEnv('TRAKT_CLIENT_SECRET', 'c2c15c65f038c0d0c1d29d7276cb0caf639fb22563cd030936fcf85fbec497e8'),
    BASE_URL: 'https://api.trakt.tv',
    PROXY_URL: '/api/trakt'
};

// TMDB API configuration
export const TMDB_CONFIG = {
    // Use environment variable with fallback for API key
    API_KEY: getEnv('TMDB_API_KEY', '085f067105ff44afd48943580aedecfd'),
    READ_ACCESS_TOKEN: getEnv('TMDB_READ_ACCESS_TOKEN', 'eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiIwODVmMDY3MTA1ZmY0NGFmZDQ4OTQzNTgwYWVkZWNmZCIsIm5iZiI6MTU3MDUyMzEzNy41OTAwMDAyLCJzdWIiOiI1ZDljNDgwMTZkNjc1YTAwMjhmMmZmMzgiLCJzY29wZXMiOlsiYXBpXJlYWQiXSwidmVyc2lvbiI6MX0.7q8Dc-itQ004jajnINOAMmysAtuN1A-AzDBSg0SDHas'),
    BASE_URL: 'https://api.themoviedb.org/3',
    IMAGE_BASE_URL: 'https://image.tmdb.org/t/p/w500'
};

// Fanart.tv API configuration
export const FANART_CONFIG = {
    // Use environment variable with fallback for API key
    API_KEY: getEnv('FANART_API_KEY', '9a1fe6e371a3be5d1877660a9b6454f1'),
    PROJECT_KEY: getEnv('FANART_PROJECT_KEY', '5e563afee3a08cd86e6607e0448d5f8e'),
    BASE_URL: 'https://webservice.fanart.tv/v3'
};

// Discord Bot configuration
export const DISCORD_CONFIG = {
    // Default configuration, will be overridden by user settings
    DEFAULT_CONFIG: {
        enabled: false,
        autoAddToDebrid: true,
        returnLinks: true
    }
};

// nxbrew configuration
export const NXBREW_CONFIG = {
    DEFAULT_CONFIG: {
        preferred_format: 'NSP',
        download_updates: true,
        download_dlc: true,
        preferred_regions: ['USA', 'EUR'],
        preferred_languages: ['English'],
        base_url: 'https://nxbrew.net/'
    }
};

// PC Game Search configuration
export const PC_SEARCH_CONFIG = {
    SOURCES: {
        FITGIRL: 'https://hydralinks.cloud/sources/fitgirl.json',
        DODI: 'https://hydralinks.cloud/sources/dodi.json',
        GOG: 'https://hydralinks.cloud/sources/gog.json'
    },
    // Default cache expiration time (24 hours)
    CACHE_EXPIRATION: 24 * 60 * 60 * 1000
};

// Local storage keys
export const STORAGE_KEYS = {
    GAME_SEARCH_DATA: 'gameSearchData',
    DISCORD_CONFIG: 'discordConfig',
    NXBREW_CONFIG: 'nxbrewConfig'
};

// Pagination configuration
export const PAGINATION_CONFIG = {
    ITEMS_PER_PAGE: 10
};
