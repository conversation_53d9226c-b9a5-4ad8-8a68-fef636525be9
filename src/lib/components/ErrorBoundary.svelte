<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { toastStore } from '$lib/stores/toastStore';
    import { t } from '$lib/i18n';
    
    /**
     * Component name for error reporting
     */
    export let componentName: string = 'Unknown';
    
    /**
     * Whether to show the error in the UI
     */
    export let showError: boolean = true;
    
    /**
     * Whether to show a toast notification
     */
    export let showToast: boolean = true;
    
    /**
     * Whether to log the error to console
     */
    export let logError: boolean = true;
    
    /**
     * Whether to allow retrying
     */
    export let allowRetry: boolean = true;
    
    /**
     * Custom error message
     */
    export let customMessage: string = '';
    
    /**
     * Custom retry message
     */
    export let retryMessage: string = $t('action.retry');
    
    /**
     * Custom error handler
     */
    export let onError: ((error: Error) => void) | null = null;
    
    // Internal state
    let error: Error | null = null;
    let hasError = false;
    let errorInfo: string = '';
    let originalConsoleError: typeof console.error;
    
    // Handle errors in child components
    function handleError(event: ErrorEvent) {
        if (event.error) {
            captureError(event.error);
        }
    }
    
    // Capture and process error
    function captureError(err: Error) {
        error = err;
        hasError = true;
        errorInfo = `${err.name}: ${err.message}\n${err.stack || ''}`;
        
        if (logError) {
            console.error(`Error in ${componentName}:`, err);
        }
        
        if (showToast) {
            toastStore.error(
                customMessage || `Error in ${componentName}: ${err.message}`,
                'Error'
            );
        }
        
        if (onError) {
            onError(err);
        }
    }
    
    // Reset error state
    function retry() {
        error = null;
        hasError = false;
        errorInfo = '';
    }
    
    // Set up error handling
    onMount(() => {
        // Save original console.error
        originalConsoleError = console.error;
        
        // Override console.error to catch rendering errors
        console.error = (...args: any[]) => {
            originalConsoleError(...args);
            
            // Check if this is a Svelte rendering error
            const errorString = args.join(' ');
            if (
                errorString.includes('Svelte') ||
                errorString.includes('rendering') ||
                errorString.includes('component')
            ) {
                const errorObject = args.find(arg => arg instanceof Error);
                if (errorObject && !hasError) {
                    captureError(errorObject);
                }
            }
        };
        
        // Listen for error events
        window.addEventListener('error', handleError);
    });
    
    // Clean up
    onDestroy(() => {
        // Restore original console.error
        if (originalConsoleError) {
            console.error = originalConsoleError;
        }
        
        // Remove event listener
        window.removeEventListener('error', handleError);
    });
</script>

{#if hasError && showError}
    <div class="error-boundary">
        <div class="error-content">
            <h3>Something went wrong</h3>
            
            <p class="error-message">
                {customMessage || `An error occurred in ${componentName}`}
            </p>
            
            {#if allowRetry}
                <button class="retry-button" on:click={retry}>
                    {retryMessage}
                </button>
            {/if}
            
            <details class="error-details">
                <summary>Technical Details</summary>
                <pre>{errorInfo}</pre>
            </details>
        </div>
    </div>
{:else}
    <slot />
{/if}

<style>
    .error-boundary {
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0.375rem;
        background-color: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .error-content {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .error-message {
        color: var(--text-primary);
        margin: 0;
    }
    
    .retry-button {
        align-self: flex-start;
        padding: 0.5rem 1rem;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 0.25rem;
        cursor: pointer;
        font-size: 0.875rem;
        transition: background-color 0.2s;
    }
    
    .retry-button:hover {
        background-color: var(--primary-color-dark);
    }
    
    .error-details {
        margin-top: 0.5rem;
        font-size: 0.875rem;
    }
    
    .error-details summary {
        cursor: pointer;
        color: var(--text-secondary);
    }
    
    .error-details pre {
        margin-top: 0.5rem;
        padding: 0.75rem;
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
        overflow-x: auto;
        font-size: 0.75rem;
        color: var(--text-secondary);
    }
</style>
