<script lang="ts">
    import { ChevronLeft, ChevronRight } from 'lucide-svelte';

    /**
     * Current page number (1-based)
     */
    export let currentPage: number;
    
    /**
     * Total number of pages
     */
    export let totalPages: number;
    
    /**
     * Function to call when page changes
     */
    export let onPageChange: (page: number) => void;
    
    /**
     * Maximum number of page buttons to show
     */
    export let maxPageButtons: number = 5;
    
    /**
     * Go to previous page if possible
     */
    function goToPrevPage() {
        if (currentPage > 1) {
            onPageChange(currentPage - 1);
        }
    }
    
    /**
     * Go to next page if possible
     */
    function goToNextPage() {
        if (currentPage < totalPages) {
            onPageChange(currentPage + 1);
        }
    }
    
    /**
     * Go to specific page
     */
    function goToPage(page: number) {
        if (page >= 1 && page <= totalPages) {
            onPageChange(page);
        }
    }
    
    /**
     * Calculate which page buttons to show
     */
    $: pageButtons = calculatePageButtons(currentPage, totalPages, maxPageButtons);
    
    /**
     * Calculate which page buttons to show based on current page and total pages
     */
    function calculatePageButtons(current: number, total: number, max: number): number[] {
        if (total <= max) {
            // If total pages is less than max buttons, show all pages
            return Array.from({ length: total }, (_, i) => i + 1);
        }
        
        // Calculate start and end page
        let start = Math.max(current - Math.floor(max / 2), 1);
        let end = start + max - 1;
        
        // Adjust if end is beyond total pages
        if (end > total) {
            end = total;
            start = Math.max(end - max + 1, 1);
        }
        
        return Array.from({ length: end - start + 1 }, (_, i) => start + i);
    }
</script>

{#if totalPages > 1}
    <div class="pagination">
        <button 
            class="pagination-button" 
            on:click={goToPrevPage} 
            disabled={currentPage === 1}
            aria-label="Previous page"
        >
            <ChevronLeft size={18} />
        </button>
        
        {#if pageButtons[0] > 1}
            <button 
                class="pagination-button" 
                on:click={() => goToPage(1)}
                aria-label="Go to first page"
            >
                1
            </button>
            
            {#if pageButtons[0] > 2}
                <span class="pagination-ellipsis">...</span>
            {/if}
        {/if}
        
        {#each pageButtons as page}
            <button 
                class="pagination-button {page === currentPage ? 'active' : ''}" 
                on:click={() => goToPage(page)}
                aria-label="Go to page {page}"
                aria-current={page === currentPage ? 'page' : undefined}
            >
                {page}
            </button>
        {/each}
        
        {#if pageButtons[pageButtons.length - 1] < totalPages}
            {#if pageButtons[pageButtons.length - 1] < totalPages - 1}
                <span class="pagination-ellipsis">...</span>
            {/if}
            
            <button 
                class="pagination-button" 
                on:click={() => goToPage(totalPages)}
                aria-label="Go to last page"
            >
                {totalPages}
            </button>
        {/if}
        
        <button 
            class="pagination-button" 
            on:click={goToNextPage} 
            disabled={currentPage === totalPages}
            aria-label="Next page"
        >
            <ChevronRight size={18} />
        </button>
    </div>
{/if}

<style>
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 1.5rem 0;
        gap: 0.25rem;
    }
    
    .pagination-button {
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 2.5rem;
        height: 2.5rem;
        padding: 0 0.5rem;
        border-radius: 0.375rem;
        background-color: var(--card-bg);
        color: var(--text);
        border: 1px solid rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .pagination-button:hover:not(:disabled) {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .pagination-button.active {
        background-color: var(--primary);
        color: #000;
        font-weight: 600;
    }
    
    .pagination-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .pagination-ellipsis {
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 2.5rem;
        height: 2.5rem;
        color: var(--text-secondary);
    }
</style>
