<script lang="ts">
    import { onMount } from 'svelte';
    import { Save, Download, Loader2, Settings, AlertTriangle, ChevronDown, ChevronUp } from 'lucide-svelte';
    import { getWithAuth, fetchWithAuth } from '$lib/utils/apiUtils';

    // State variables
    let isoRomSearchQuery = '';
    let searchResults: any[] = [];
    let isSearching = false;
    let isDownloading = false;
    let selectedGame: any = null;
    let errorMessage = '';
    let showDebugInfo = false;
    let debugInfo: any = null;

    // Debounce variables
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 1500; // 1.5 seconds debounce time

    // Function to handle search input changes with debounce
    function handleSearchInput() {
        // Clear any existing timer
        clearTimeout(typingTimer);

        // Reset results if search query is cleared
        if (isoRomSearchQuery.trim().length === 0) {
            searchResults = [];
            return;
        }

        // Start a new timer for search
        if (isoRomSearchQuery.trim().length >= 3) {
            // Set a timer to execute search after the debounce interval
            typingTimer = setTimeout(() => {
                searchGames();
            }, doneTypingInterval);
        }
    }

    // Function to handle key press in search input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && isoRomSearchQuery.trim().length >= 3) {
            // Clear any existing timer to prevent duplicate searches
            clearTimeout(typingTimer);
            searchGames();
        }
    }

    // Function to search for games
    async function searchGames() {
        if (isoRomSearchQuery.trim().length < 3) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wpisz co najmniej 3 znaki', 'error');
            }
            return;
        }

        isSearching = true;
        searchResults = [];
        errorMessage = '';
        debugInfo = null;
        showDebugInfo = false;

        try {
            const response = await getWithAuth(`/api/isorom/search?query=${encodeURIComponent(isoRomSearchQuery)}`);

            const data = await response.json();

            if (!response.ok) {
                errorMessage = data.error || `Błąd wyszukiwania: ${response.status}`;
                debugInfo = data.debug || null;
                console.error('Błąd wyszukiwania:', errorMessage, debugInfo);

                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(errorMessage, 'error');
                }
                return;
            }

            searchResults = data.results || [];

            if (searchResults.length === 0) {
                errorMessage = 'Nie znaleziono gier';
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(errorMessage, 'error');
                }
            }
        } catch (error) {
            console.error('Błąd wyszukiwania:', error);
            errorMessage = 'Błąd wyszukiwania: ' + (error instanceof Error ? error.message : 'Nieznany błąd');

            if (error instanceof Error) {
                debugInfo = {
                    error: error.toString(),
                    stack: error.stack,
                    message: error.message
                };
            } else {
                debugInfo = {
                    error: String(error),
                    message: 'Nieznany błąd'
                };
            }

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(errorMessage, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Function to download a game
    async function downloadGame(game: any) {
        console.log('Download game:', game);

        if (!game.url) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Brak URL gry', 'error');
            }
            return;
        }

        // Set the selected game and downloading state
        selectedGame = game;
        isDownloading = true;

        try {
            // First, we need to get the download page URL from the game page
            const response = await fetchWithAuth(`/api/isorom/download`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ downloadUrl: game.url })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `Błąd pobierania: ${response.status}`);
            }

            if (!data.success || !data.magnetLink) {
                throw new Error('Nie znaleziono magnet linku');
            }

            // We have the magnet link, now redirect to the "Add Torrent" tab with the magnet link
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification('Znaleziono magnet link!', 'success');
            }

            // Use the setMagnetAndSwitchTab function to switch to the "Add Torrent" tab
            setMagnetAndSwitchTab(data.magnetLink);

        } catch (error) {
            console.error('Błąd pobierania:', error);

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd pobierania: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        } finally {
            isDownloading = false;
        }
    }

    // Function to switch to the "Add Torrent" tab with a magnet link
    export let setMagnetAndSwitchTab: (magnetLink: string) => void = () => {};

    // Toggle debug info display
    function toggleDebugInfo() {
        showDebugInfo = !showDebugInfo;
    }

    onMount(() => {
        // Focus on search input when component mounts
        const searchInput = document.getElementById('isoRomSearchQuery');
        if (searchInput) {
            searchInput.focus();
        }
    });
</script>

<div class="card">
    <div class="flex items-center justify-between mb-4">
        <div>
            <h2>Szukaj gry (ISO/ROM)</h2>
            <p class="text-[var(--text-secondary)]">Wyszukaj i pobierz obrazy ISO i pliki ROM gier na konsole</p>
        </div>
        <div class="flex gap-2">
            <button class="btn btn-icon" title="Konfiguracja">
                <Settings size={32} color="white" />
            </button>
        </div>
    </div>

    <div style="display: flex; gap: 10px; margin-top: 20px; position: relative;">
        <input type="text" id="isoRomSearchQuery" class="magnet-input"
               style="flex: 1; padding-right: 40px;"
               placeholder="Wpisz tytuł gry (min. 3 znaki)..."
               bind:value={isoRomSearchQuery}
               on:input={handleSearchInput}
               on:keypress={handleKeyPress}
               disabled={isSearching}>

        {#if isSearching}
            <div class="search-indicator">
                <div class="loading"></div>
            </div>
        {/if}
    </div>

    {#if errorMessage}
        <div class="mt-6 error-container">
            <button
                class="w-full text-left error-header"
                on:click={toggleDebugInfo}
                on:keydown={(e) => e.key === 'Enter' && toggleDebugInfo()}
                type="button"
                aria-expanded={showDebugInfo}
                aria-controls="debug-info-panel">
                <AlertTriangle size={20} class="text-[var(--error)]" />
                <span>{errorMessage}</span>
                {#if debugInfo}
                    <div class="ml-auto btn-icon-small">
                        {#if showDebugInfo}
                            <ChevronUp size={16} />
                        {:else}
                            <ChevronDown size={16} />
                        {/if}
                    </div>
                {/if}
            </button>

            {#if showDebugInfo && debugInfo}
                <div id="debug-info-panel" class="debug-info">
                    <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
                </div>
            {/if}
        </div>
    {/if}

    {#if searchResults.length > 0}
        <div class="search-results">
            <h3 class="mt-6 mb-3">Wyniki wyszukiwania</h3>
            <div class="results-list">
                {#each searchResults as game}
                    <div class="game-item">
                        <div class="game-info">
                            {#if game.imageUrl}
                                <div class="game-cover">
                                    <img src={game.imageUrl} alt={game.title} loading="lazy">
                                </div>
                            {:else}
                                <div class="game-icon">
                                    <Save size={24} />
                                </div>
                            {/if}
                            <div class="game-details">
                                <h4 class="game-title">{game.title}</h4>
                                <div class="game-meta">
                                    {#if game.platform}
                                        <span class="game-platform">Platform: {game.platform}</span>
                                    {/if}
                                    {#if game.size}
                                        <span class="game-size">Size: {game.size}</span>
                                    {/if}
                                    {#if game.releaseDate}
                                        <span class="game-date">Release Date: {game.releaseDate}</span>
                                    {/if}
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-secondary download-btn"
                                on:click={() => downloadGame(game)}
                                disabled={isDownloading && selectedGame?.url === game.url}>
                            {#if isDownloading && selectedGame?.url === game.url}
                                <div class="flex items-center gap-2">
                                    <Loader2 size={18} class="animate-spin" />
                                    <span>Szukanie...</span>
                                </div>
                            {:else}
                                <Download size={18} />
                            {/if}
                        </button>
                    </div>
                {/each}
            </div>
        </div>
    {:else if isSearching}
        <div class="searching-indicator">
            <div class="loading-large"></div>
            <p>Wyszukiwanie gier...</p>
        </div>
    {/if}
</div>

<style>
    .search-indicator {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    .loading {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top: 2px solid var(--primary);
        animation: spin 1s linear infinite;
    }

    .loading-large {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top: 4px solid var(--primary);
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .search-results {
        margin-top: 20px;
    }

    .results-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: 10px;
    }

    .game-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background-color: var(--item-bg);
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .game-item:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .game-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .game-icon {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
    }

    .game-cover {
        width: 60px;
        height: 60px;
        border-radius: 6px;
        overflow: hidden;
        background-color: rgba(0, 0, 0, 0.2);
    }

    .game-cover img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .game-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .game-title {
        font-weight: 600;
        margin: 0;
        font-size: 1rem;
    }

    .game-meta {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .game-platform, .game-size, .game-date {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }

    .download-btn {
        min-width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
    }

    .download-btn:disabled {
        opacity: 0.8;
        min-width: 130px;
    }

    .searching-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        gap: 16px;
    }

    .error-container {
        background-color: rgba(var(--error-rgb, 248, 113, 113), 0.1);
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 20px;
    }

    .error-header {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 16px;
        cursor: pointer;
        font-weight: 500;
    }

    .btn-icon-small {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .btn-icon-small:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text);
    }

    .debug-info {
        padding: 12px 16px;
        background-color: rgba(0, 0, 0, 0.2);
        border-top: 1px solid rgba(var(--error-rgb, 248, 113, 113), 0.2);
        overflow-x: auto;
    }

    .debug-info pre {
        font-family: monospace;
        font-size: 0.85rem;
        white-space: pre-wrap;
        color: var(--text-secondary);
    }
</style>
