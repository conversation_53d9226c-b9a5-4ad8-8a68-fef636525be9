<script lang="ts">
    import { onMount } from 'svelte';
    import {
        getTorrents,
        getStatusClass,
        formatStatus,
        deleteTorrent,
        getTorrentInfo,
        unrestrictLink,
        type Torrent as BaseTorrent,
        type TorrentCategory
    } from '$lib/services/debridService';

    // Rozszerzamy typ Torrent o pole coverUrl
    interface Torrent extends BaseTorrent {
        coverUrl?: string;
    }
    import { Layers, Film, Video, Gamepad, Download, Link, Trash2 } from 'lucide-svelte';
    import { getGameCoverByName } from '$lib/services/steamGridDbService';
    import { getMoviePosterByTitle } from '$lib/services/omdbService';
    import DownloadSuccessModal from './DownloadSuccessModal.svelte';

    // Zmienne dla paginacji
    const itemsPerPage = 10; // Liczba torrentów na stronę
    let currentPage = 1;
    let totalPages = 1;
    let allTorrents: Torrent[] = [];
    let filteredTorrents: Torrent[] = [];
    let currentPageTorrents: Torrent[] = [];
    let isLoading = false;
    let error: string | null = null;

    // Zbiór indeksów torrentów, dla których załadowano już okładki
    let loadedCoverIndices = new Set<number>();

    // Zmienna dla aktywnej kategorii
    let activeCategory: TorrentCategory = 'all';

    // Funkcja do ładowania listy torrentów
    async function loadTorrents() {
        isLoading = true;
        error = null;

        try {
            // Pobierz listę torrentów
            const newTorrents = await getTorrents();

            // Sprawdź, czy lista torrentów się zmieniła
            const torrentsChanged = checkTorrentsChanged(allTorrents, newTorrents);

            // Aktualizuj listę torrentów
            allTorrents = newTorrents;

            // Jeśli lista torrentów się zmieniła, resetuj zbiór załadowanych okładek
            if (torrentsChanged) {
                console.log('Lista torrentów się zmieniła, resetuję zbiór załadowanych okładek');
                loadedCoverIndices.clear();
            } else {
                console.log('Lista torrentów nie zmieniła się, zachowuję zbiór załadowanych okładek');
            }

            // Filtruj torrenty według aktywnej kategorii
            filterTorrentsByCategory();
        } catch (err) {
            console.error('Błąd wczytywania pobrań:', err);
            error = err instanceof Error ? err.message : 'Nieznany błąd';
        } finally {
            isLoading = false;
        }
    }

    // Funkcja do sprawdzania, czy lista torrentów się zmieniła
    function checkTorrentsChanged(oldTorrents: Torrent[], newTorrents: Torrent[]): boolean {
        // Jeśli liczba torrentów jest różna, lista się zmieniła
        if (oldTorrents.length !== newTorrents.length) {
            return true;
        }

        // Sprawdź, czy wszystkie torrenty są takie same
        const oldIds = new Set(oldTorrents.map(t => t.id));
        const newIds = new Set(newTorrents.map(t => t.id));

        // Sprawdź, czy wszystkie stare ID są w nowych torrentach
        for (const id of oldIds) {
            if (!newIds.has(id)) {
                return true;
            }
        }

        // Sprawdź, czy wszystkie nowe ID są w starych torrentach
        for (const id of newIds) {
            if (!oldIds.has(id)) {
                return true;
            }
        }

        // Sprawdź, czy status torrentów się zmienił
        for (let i = 0; i < oldTorrents.length; i++) {
            const oldTorrent = oldTorrents[i];
            const newTorrent = newTorrents.find(t => t.id === oldTorrent.id);

            if (!newTorrent || oldTorrent.status !== newTorrent.status) {
                return true;
            }
        }

        // Jeśli doszliśmy tutaj, lista torrentów nie zmieniła się
        return false;
    }

    // Funkcja do filtrowania torrentów według kategorii
    function filterTorrentsByCategory() {
        // Zapisz poprzednią listę przefiltrowanych torrentów
        const previousFilteredTorrents = [...filteredTorrents];

        if (activeCategory === 'all') {
            filteredTorrents = [...allTorrents];
        } else {
            filteredTorrents = allTorrents.filter(torrent => torrent.category === activeCategory);
        }

        // Sprawdź, czy lista przefiltrowanych torrentów się zmieniła
        const filtersChanged = checkTorrentsChanged(previousFilteredTorrents, filteredTorrents);

        // Resetuj do pierwszej strony przy zmianie filtra
        currentPage = 1;

        // Resetuj zbiór załadowanych okładek tylko jeśli filtry się zmieniły
        if (filtersChanged) {
            console.log('Filtry się zmieniły, resetuję zbiór załadowanych okładek');
            loadedCoverIndices.clear();
        } else {
            console.log('Filtry się nie zmieniły, zachowuję zbiór załadowanych okładek');
        }

        // Oblicz całkowitą liczbę stron dla przefiltrowanych torrentów
        totalPages = Math.ceil(filteredTorrents.length / itemsPerPage);

        // Renderuj torrenty
        renderTorrents();
    }

    // Funkcja do zmiany aktywnej kategorii
    function setActiveCategory(category: TorrentCategory) {
        activeCategory = category;
        filterTorrentsByCategory();
    }

    // Funkcja do renderowania torrentów dla bieżącej strony
    function renderTorrents() {
        if (filteredTorrents.length === 0) {
            currentPageTorrents = [];
            return;
        }

        // Oblicz indeksy początkowe i końcowe dla bieżącej strony
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, filteredTorrents.length);

        // Pobierz torrenty dla bieżącej strony
        currentPageTorrents = filteredTorrents.slice(startIndex, endIndex);
    }

    // Funkcja do przejścia do określonej strony
    function goToPage(pageNumber: number) {
        currentPage = pageNumber;
        renderTorrents();
        // Pobierz okładki dla nowej strony
        fetchCovers();
    }

    // Funkcja do usuwania torrenta
    async function handleDeleteTorrent(torrentId: string, torrentName: string) {
        // Użyj nowego modalu zamiast standardowego confirm
        if (typeof window !== 'undefined' && (window as any).showConfirm) {
            (window as any).showConfirm(
                `Czy na pewno chcesz usunąć torrent "${torrentName}"?`,
                async () => {
                    try {
                        await deleteTorrent(torrentId);

                        if (typeof window !== 'undefined' && (window as any).showNotification) {
                            (window as any).showNotification('Torrent usunięty pomyślnie', 'success');
                        }

                        // Odśwież listę
                        loadTorrents();
                    } catch (err) {
                        console.error('Błąd usuwania torrenta:', err);

                        if (typeof window !== 'undefined' && (window as any).showNotification) {
                            (window as any).showNotification(
                                'Nie udało się usunąć torrenta: ' + (err instanceof Error ? err.message : 'Nieznany błąd'),
                                'error'
                            );
                        }
                    }
                }
            );
        }
    }

    // Funkcja do kopiowania odblokowanego linku
    async function copyUnrestrictedLink(torrentId: string) {
        try {
            const torrentInfo = await getTorrentInfo(torrentId);

            if (torrentInfo.links.length === 0) {
                if (typeof window !== 'undefined' && (window as any).showNotification) {
                    (window as any).showNotification('Brak dostępnych linków do skopiowania', 'error');
                }
                return;
            }

            // Jeśli jest tylko jeden link, kopiujemy go bezpośrednio
            if (torrentInfo.links.length === 1) {
                const unrestricted = await unrestrictLink(torrentInfo.links[0]);
                await navigator.clipboard.writeText(unrestricted.download);

                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert('Skopiowano do schowka', 'success');
                }
                return;
            }

            // Jeśli jest więcej linków, pokazujemy modal z wyborem
            showLinkSelectionModal(torrentInfo, 'copy');
        } catch (err) {
            console.error('Błąd kopiowania linku:', err);

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Nie udało się skopiować linku: ' + (err instanceof Error ? err.message : 'Nieznany błąd'),
                    'error'
                );
            }
        }
    }

    // Funkcja do pobierania odblokowanego linku
    async function downloadUnrestrictedLink(torrentId: string) {
        try {
            const torrentInfo = await getTorrentInfo(torrentId);

            if (torrentInfo.links.length === 0) {
                if (typeof window !== 'undefined' && (window as any).showNotification) {
                    (window as any).showNotification('Brak dostępnych linków do pobrania', 'error');
                }
                return;
            }

            // Jeśli jest tylko jeden link, pobieramy go bezpośrednio
            if (torrentInfo.links.length === 1) {
                const unrestricted = await unrestrictLink(torrentInfo.links[0]);

                // Pokaż modal sukcesu z okładką i tytułem
                const torrent = filteredTorrents.find(t => t.id === torrentId);
                if (torrent) {
                    successModalData = {
                        isOpen: true,
                        coverUrl: torrent.coverUrl || '',
                        title: torrent.filename
                    };
                }

                // Utwórz ukryty link i kliknij go zamiast otwierać nowe okno
                setTimeout(() => {
                    const downloadLink = document.createElement('a');
                    downloadLink.href = unrestricted.download;
                    downloadLink.download = torrent?.filename || 'download';
                    downloadLink.style.display = 'none';
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                }, 500); // Małe opóźnienie, aby animacja była widoczna przed rozpoczęciem pobierania

                return;
            }

            // Jeśli jest więcej linków, pokazujemy modal z wyborem
            showLinkSelectionModal(torrentInfo, 'download');
        } catch (err) {
            console.error('Błąd pobierania linku:', err);

            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(
                    'Nie udało się pobrać linku: ' + (err instanceof Error ? err.message : 'Nieznany błąd'),
                    'error'
                );
            }
        }
    }

    // Zmienna do przechowywania danych modalu
    let modalData: {
        isOpen: boolean;
        torrent: Torrent | null;
        mode: 'copy' | 'download';
        isProcessing: boolean;
        processingIndex: number | null;
    } = {
        isOpen: false,
        torrent: null,
        mode: 'copy',
        isProcessing: false,
        processingIndex: null
    };

    // Zmienna do przechowywania danych modalu sukcesu pobierania
    let successModalData: {
        isOpen: boolean;
        coverUrl: string;
        title: string;
    } = {
        isOpen: false,
        coverUrl: '',
        title: ''
    };

    // Funkcja do pokazywania modalu wyboru linku
    function showLinkSelectionModal(torrent: Torrent, mode: 'copy' | 'download') {
        modalData = {
            isOpen: true,
            torrent,
            mode,
            isProcessing: false,
            processingIndex: null
        };
    }

    // Funkcja do zamykania modalu
    function closeModal() {
        modalData.isOpen = false;
    }

    // Funkcja do kopiowania linku z modalu
    async function handleCopyLink(link: string, index: number) {
        modalData.isProcessing = true;
        modalData.processingIndex = index;

        try {
            const unrestricted = await unrestrictLink(link);
            await navigator.clipboard.writeText(unrestricted.download);

            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification('Skopiowano do schowka', 'success');
            }

            closeModal();
        } catch (err) {
            console.error('Błąd kopiowania linku:', err);

            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(
                    'Nie udało się skopiować linku: ' + (err instanceof Error ? err.message : 'Nieznany błąd'),
                    'error'
                );
            }
        } finally {
            modalData.isProcessing = false;
            modalData.processingIndex = null;
        }
    }

    // Funkcja do pobierania linku z modalu
    async function handleDownloadLink(link: string, index: number) {
        modalData.isProcessing = true;
        modalData.processingIndex = index;

        try {
            const unrestricted = await unrestrictLink(link);

            // Zamknij modal wyboru linku
            closeModal();

            // Pokaż modal sukcesu z okładką i tytułem
            if (modalData.torrent) {
                successModalData = {
                    isOpen: true,
                    coverUrl: modalData.torrent.coverUrl || '',
                    title: modalData.torrent.filename
                };
            }

            // Utwórz ukryty link i kliknij go zamiast otwierać nowe okno
            setTimeout(() => {
                const downloadLink = document.createElement('a');
                downloadLink.href = unrestricted.download;
                downloadLink.download = modalData.torrent?.filename || 'download';
                downloadLink.style.display = 'none';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
            }, 500); // Małe opóźnienie, aby animacja była widoczna przed rozpoczęciem pobierania

        } catch (err) {
            console.error('Błąd pobierania linku:', err);

            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(
                    'Nie udało się pobrać linku: ' + (err instanceof Error ? err.message : 'Nieznany błąd'),
                    'error'
                );
            }
        } finally {
            modalData.isProcessing = false;
            modalData.processingIndex = null;
        }
    }

    // Funkcja do pobierania okładek dla torrentów
    async function fetchCovers() {
        console.log('Rozpoczynam pobieranie okładek dla torrentów');

        // Oblicz zakres indeksów dla bieżącej strony
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = Math.min(startIndex + itemsPerPage, filteredTorrents.length);

        console.log(`Pobieranie okładek dla torrentów ${startIndex}-${endIndex}`);

        // Sprawdź czy localStorage jest dostępny
        const hasLocalStorage = typeof window !== 'undefined' && window.localStorage;

        // Pobierz cache okładek z localStorage
        let coverCache: Record<string, string> = {};
        if (hasLocalStorage) {
            try {
                const cachedData = localStorage.getItem('torrentCovers');
                if (cachedData) {
                    coverCache = JSON.parse(cachedData);
                    console.log('Wczytano cache okładek z localStorage');
                }
            } catch (error) {
                console.error('Błąd wczytywania cache okładek:', error);
            }
        }

        for (let i = startIndex; i < endIndex; i++) {
            // Pomijamy jeśli już załadowano okładkę dla tego torrenta
            if (loadedCoverIndices.has(i)) {
                console.log(`Pomijam indeks ${i} - okładka już załadowana`);
                continue;
            }

            const torrent = filteredTorrents[i];

            // Jeśli torrent już ma okładkę, pomijamy
            if (torrent.coverUrl) {
                loadedCoverIndices.add(i);
                continue;
            }

            try {
                let coverUrl = null;

                // Sprawdź czy okładka jest w cache
                if (hasLocalStorage && coverCache[torrent.filename]) {
                    coverUrl = coverCache[torrent.filename];
                    console.log(`Znaleziono okładkę w cache dla: ${torrent.filename}`);
                } else {
                    // Pobierz okładkę w zależności od kategorii
                    if (torrent.category === 'movies' || torrent.category === 'tvshows') {
                        // Dla filmów i seriali używamy OMDb
                        coverUrl = await getMoviePosterByTitle(torrent.filename);
                    } else if (torrent.category === 'other') {
                        // Dla gier używamy SteamGridDB
                        coverUrl = await getGameCoverByName(torrent.filename);
                    }

                    // Zapisz okładkę w cache
                    if (coverUrl && hasLocalStorage) {
                        coverCache[torrent.filename] = coverUrl;
                        try {
                            localStorage.setItem('torrentCovers', JSON.stringify(coverCache));
                            console.log(`Zapisano okładkę w cache dla: ${torrent.filename}`);
                        } catch (error) {
                            console.error('Błąd zapisywania cache okładek:', error);
                        }
                    }
                }

                if (coverUrl) {
                    // Aktualizuj torrent z URL okładki
                    filteredTorrents[i] = { ...torrent, coverUrl };
                    console.log(`Pobrano okładkę dla: ${torrent.filename}, URL: ${coverUrl}`);
                } else {
                    console.log(`Nie znaleziono okładki dla: ${torrent.filename}`);
                }

                // Dodaj indeks do zbioru załadowanych okładek
                loadedCoverIndices.add(i);
            } catch (error) {
                console.error(`Błąd pobierania okładki dla ${torrent.filename}:`, error);
            }
        }

        // Aktualizuj listę torrentów na bieżącej stronie
        renderTorrents();
    }



    // Załaduj torrenty przy montowaniu komponentu
    onMount(() => {
        loadTorrents();
    });

    // Efekt do pobierania okładek po załadowaniu torrentów
    $: if (currentPageTorrents.length > 0 && !isLoading) {
        fetchCovers();
    }
</script>

<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2>Chlewikowa pobieralnia</h2>
        <button class="btn btn-outline" onclick={loadTorrents} disabled={isLoading}>
            {#if isLoading}
                Odświeżanie...<div class="loading"></div>
            {:else}
                Odśwież listę
            {/if}
        </button>
    </div>

    <!-- Kategorie -->
    <div class="category-tabs">
        <button class="category-tab {activeCategory === 'all' ? 'active' : ''}"
                onclick={() => setActiveCategory('all')}>
            <Layers size={18} strokeWidth={2} />
            <span>Wszystkie</span>
        </button>
        <button class="category-tab {activeCategory === 'movies' ? 'active' : ''}"
                onclick={() => setActiveCategory('movies')}>
            <Film size={18} strokeWidth={2} />
            <span>Filmy</span>
        </button>
        <button class="category-tab {activeCategory === 'tvshows' ? 'active' : ''}"
                onclick={() => setActiveCategory('tvshows')}>
            <Video size={18} strokeWidth={2} />
            <span>Seriale</span>
        </button>
        <button class="category-tab {activeCategory === 'other' ? 'active' : ''}"
                onclick={() => setActiveCategory('other')}>
            <Gamepad size={18} strokeWidth={2} />
            <span>Gry / inne</span>
        </button>
    </div>

    {#if error}
        <div class="empty-state">
            <p>Błąd wczytywania pobrań.<br />
            {error}</p>
        </div>
    {:else if allTorrents.length === 0}
        <div class="empty-state">
            <p>Brak dostępnych pobrań.<br />
            Dodaj torrent z zakładki "Dodaj Torrent"</p>
        </div>
    {:else if filteredTorrents.length === 0}
        <div class="empty-state">
            <p>Brak pobrań w kategorii {activeCategory === 'movies' ? 'Filmy' :
                                      activeCategory === 'tvshows' ? 'Seriale' :
                                      activeCategory === 'other' ? 'Inne' : ''}.<br />
            Wybierz inną kategorię lub dodaj nowe torrenty.</p>
        </div>
    {:else}
        <div class="resource-list">
            {#each currentPageTorrents as torrent}
                <div class="resource-item">
                    {#if torrent.coverUrl}
                        <div class="resource-cover">
                            <img src={torrent.coverUrl} alt="Okładka" />
                        </div>
                    {:else}
                        <div class="resource-cover placeholder">
                            {#if torrent.category === 'movies'}
                                <Film size={40} />
                            {:else if torrent.category === 'tvshows'}
                                <Video size={40} />
                            {:else}
                                <Gamepad size={40} />
                            {/if}
                        </div>
                    {/if}
                    <div class="resource-item-info">
                        <h3 title={torrent.filename}>{torrent.filename}</h3>
                        <div class="resource-status {getStatusClass(torrent.status)}">
                            {formatStatus(torrent.status, torrent.status === 'downloading' ? torrent.progress : null)}
                        </div>
                        {#if torrent.status !== 'downloaded' && torrent.status !== 'ready'}
                            <div class="resource-progress">
                                <div class="progress-bar" style="width: {Math.floor(torrent.progress)}%"></div>
                            </div>
                        {/if}
                    </div>
                    <div class="action-btns">
                        <button class="btn-icon btn-primary"
                                disabled={torrent.links.length === 0}
                                onclick={() => downloadUnrestrictedLink(torrent.id)}
                                title="Pobierz">
                            {#if torrent.links.length === 0}
                                <div class="loading-small"></div>
                            {:else}
                                <Download size={20} />
                            {/if}
                        </button>
                        <button class="btn-icon btn-outline"
                                disabled={torrent.links.length === 0}
                                onclick={() => copyUnrestrictedLink(torrent.id)}
                                title="Kopiuj link">
                            <Link size={20} />
                        </button>
                        <button class="btn-icon btn-error"
                                onclick={() => handleDeleteTorrent(torrent.id, torrent.filename)}
                                title="Usuń">
                            <Trash2 size={20} />
                        </button>
                    </div>
                </div>
            {/each}
        </div>

        <!-- Paginacja -->
        {#if totalPages > 1}
            <div class="pagination">
                <button class="pagination-btn"
                        disabled={currentPage === 1}
                        onclick={() => goToPage(currentPage - 1)}>
                    &laquo; Wstecz
                </button>

                {#each Array(totalPages) as _, i}
                    <button class="pagination-btn {currentPage === i + 1 ? 'active' : ''}"
                            onclick={() => goToPage(i + 1)}>
                        {i + 1}
                    </button>
                {/each}

                <button class="pagination-btn"
                        disabled={currentPage === totalPages}
                        onclick={() => goToPage(currentPage + 1)}>
                    Dalej &raquo;
                </button>

                <span class="pagination-info">
                    {(currentPage - 1) * itemsPerPage + 1}-{Math.min(currentPage * itemsPerPage, filteredTorrents.length)} z {filteredTorrents.length}
                </span>
            </div>
        {/if}
    {/if}
</div>

<!-- Modal wyboru linku -->
{#if modalData.isOpen && modalData.torrent}
    <div class="modal-overlay" tabindex="-1" onclick={closeModal} onkeydown={(e) => e.key === 'Escape' && closeModal()} role="dialog" aria-modal="true" aria-labelledby="modal-title">
        <div class="modal-content" onclick={(e) => e.stopPropagation()} role="none">
            <h2 id="modal-title">Dostępne pliki</h2>
            <div class="modal-body">
                {#each modalData.torrent.files as file, i}
                    {#if i < modalData.torrent.links.length}
                        <div class="modal-item">
                            <span class="modal-item-name" title={file.path.split('/').pop()}>
                                {file.path.split('/').pop()}
                            </span>
                            <div class="modal-item-actions">
                                {#if modalData.mode === 'copy'}
                                    <button class="btn btn-outline"
                                            disabled={modalData.isProcessing && modalData.processingIndex === i}
                                            onclick={() => handleCopyLink(modalData.torrent!.links[i], i)}>
                                        {#if modalData.isProcessing && modalData.processingIndex === i}
                                            Kopiowanie...<div class="loading"></div>
                                        {:else}
                                            Kopiuj
                                        {/if}
                                    </button>
                                {:else}
                                    <button class="btn btn-primary"
                                            disabled={modalData.isProcessing && modalData.processingIndex === i}
                                            onclick={() => handleDownloadLink(modalData.torrent!.links[i], i)}>
                                        {#if modalData.isProcessing && modalData.processingIndex === i}
                                            Dodawanie...<div class="loading"></div>
                                        {:else}
                                            Pobierz
                                        {/if}
                                    </button>
                                {/if}
                            </div>
                        </div>
                    {/if}
                {/each}
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick={closeModal}>Zamknij</button>
            </div>
        </div>
    </div>
{/if}

<!-- Modal sukcesu pobierania -->
<DownloadSuccessModal
    bind:open={successModalData.isOpen}
    coverUrl={successModalData.coverUrl}
    title={successModalData.title}
    autoCloseTimeout={3000}
/>

<style>
    .category-tabs {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .category-tab {
        padding: 8px 16px;
        border-radius: 20px;
        background-color: #2d2d2d;
        border: none;
        color: #ccc;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .category-tab.active {
        background-color: #4a4a4a;
        color: white;
        font-weight: 500;
    }

    .category-tab:hover {
        background-color: #3d3d3d;
    }

    .resource-item {
        display: flex;
        gap: 15px;
    }

    .action-btns {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-left: auto;
    }

    .resource-cover {
        width: 80px;
        height: 120px;
        border-radius: 6px;
        overflow: hidden;
        flex-shrink: 0;
        background-color: #2d2d2d;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .resource-cover img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .resource-cover.placeholder {
        color: #666;
    }

    .btn-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        color: white;
    }

    .btn-icon.btn-primary {
        background-color: var(--primary-color, #4a6cf7);
    }

    .btn-icon.btn-outline {
        background-color: transparent;
        border: 1px solid #4a4a4a;
        color: #ccc;
    }

    .btn-icon.btn-error {
        background-color: var(--error-color, #e53935);
    }

    .btn-icon:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .btn-icon:active {
        transform: translateY(0);
    }

    .btn-icon:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .loading-small {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .modal-content {
        background-color: var(--card-bg);
        border-radius: 8px;
        padding: 20px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow: auto;
    }

    .modal-body {
        margin: 20px 0;
    }

    .modal-item {
        background-color: #2d2d2d;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-item-name {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: wrap;
        margin-right: 10px;
    }

    .modal-item-actions {
        display: flex;
        gap: 8px;
    }

    .modal-footer {
        text-align: right;
    }
</style>
