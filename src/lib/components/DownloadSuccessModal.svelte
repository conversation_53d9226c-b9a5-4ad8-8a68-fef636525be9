<script lang="ts">
    import { onDestroy } from 'svelte';
    import { fade, scale } from 'svelte/transition';

    let {
        open = $bindable(false),
        coverUrl = '',
        title = '',
        autoCloseTimeout = 3000
    } = $props();

    // Internal state
    let closeTimeout = $state<number | null>(null);

    // Handle modal open/close
    $effect(() => {
        if (open) {
            // Auto close after timeout
            closeTimeout = window.setTimeout(() => {
                open = false;
            }, autoCloseTimeout);
        } else {
            // Clear timeouts
            if (closeTimeout) {
                clearTimeout(closeTimeout);
                closeTimeout = null;
            }
        }
    });

    // Clean up on component destroy
    onDestroy(() => {
        if (closeTimeout) clearTimeout(closeTimeout);
    });
</script>

{#if open}
    <!-- Backdrop with blur effect -->
    <div
        class="success-modal-backdrop"
        transition:fade={{ duration: 200 }}
        role="presentation"
    >
        <!-- Modal content -->
        <div
            class="success-modal-container"
            transition:scale={{ duration: 200, start: 0.95 }}
            role="dialog"
            aria-modal="true"
        >
            <!-- Cover image -->
            <div class="cover-container">
                {#if coverUrl}
                    <img
                        src={coverUrl}
                        alt={title}
                        class="cover-image"
                    />
                {:else}
                    <div class="cover-placeholder cover-image">
                        <span>🎮</span>
                    </div>
                {/if}
            </div>

            <!-- Title (optional) -->
            {#if title}
                <div class="success-title">
                    {title}
                </div>
            {/if}
        </div>
    </div>
{/if}

<style>
    .success-modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        overflow: hidden;
    }

    .success-modal-container {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        border-radius: 1rem;
        background-color: rgba(34, 34, 34, 0.8);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        overflow: hidden;
        width: 90%;
        max-width: 450px;
        aspect-ratio: 1;
    }

    .cover-container {
        width: 90%;
        height: 90%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .cover-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 10px;
        box-shadow:
            0 10px 25px -5px rgba(0, 0, 0, 0.5),
            0 0 0 1px rgba(255, 255, 255, 0.1);
        position: relative;
        transform-style: preserve-3d;
        transform: perspective(1000px);
    }

    .cover-image::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.2) 0%,
            rgba(255, 255, 255, 0) 50%,
            rgba(0, 0, 0, 0.2) 100%);
        border-radius: 10px;
        pointer-events: none;
    }

    .cover-placeholder {
        background-color: #333;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 4rem;
        position: relative;
    }

    .cover-placeholder::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0) 50%,
            rgba(0, 0, 0, 0.2) 100%);
        border-radius: 10px;
    }

    .success-title {
        color: white;
        font-size: 1.25rem;
        font-weight: bold;
        text-align: center;
        margin-top: 1.5rem;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    /* Usunięto animację gentle-spin */
</style>
