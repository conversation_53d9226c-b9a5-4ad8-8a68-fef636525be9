<script lang="ts">
    import { onMount } from 'svelte';
    import { getRecentGames, getUpcomingGames, searchGames, type GameSearchResult } from '$lib/services/giantBombService';

    // State variables
    let isLoading = true;
    let searchQuery = '';
    let recentGames: GameSearchResult[] = [];
    let upcomingGames: GameSearchResult[] = [];
    let searchResults: GameSearchResult[] = [];
    let activeTab: 'recent' | 'upcoming' | 'search' = 'recent';
    let errorMessage = '';
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 500; // ms

    // Load data on mount
    onMount(async () => {
        await loadGames();

        // Focus na pole wyszukiwania
        setTimeout(() => {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                (searchInput as HTMLInputElement).focus();
            }
        }, 100);
    });

    // Function to load games
    async function loadGames() {
        isLoading = true;
        errorMessage = '';

        try {
            // Load recent games
            recentGames = await getRecentGames(12);

            // Load upcoming games
            upcomingGames = await getUpcomingGames(12);
        } catch (error) {
            console.error('Error loading games:', error);
            errorMessage = error instanceof Error ? error.message : 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Error loading games: ' + errorMessage,
                    'error'
                );
            }
        } finally {
            isLoading = false;
        }
    }

    // Function to handle search input changes with debounce
    function handleSearchInput() {
        // Clear any existing timer
        clearTimeout(typingTimer);

        // Reset results if search query is cleared
        if (searchQuery.trim().length === 0) {
            searchResults = [];
            return;
        }

        // Start a new timer for search
        if (searchQuery.trim().length >= 3) {
            // Set a timer to execute search after the debounce interval
            typingTimer = setTimeout(() => {
                performSearch();
            }, doneTypingInterval);
        }
    }

    // Function to handle key press in search input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && searchQuery.trim().length >= 3) {
            // Clear any existing timer to prevent duplicate searches
            clearTimeout(typingTimer);
            performSearch();
        }
    }

    // Function to perform search
    async function performSearch() {
        if (searchQuery.trim().length < 3) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Please enter at least 3 characters', 'error');
            }
            return;
        }

        isLoading = true;
        errorMessage = '';
        searchResults = [];

        try {
            // Search for games
            searchResults = await searchGames(searchQuery);

            // Switch to search tab
            activeTab = 'search';

            if (searchResults.length === 0) {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert('No games found', 'info');
                }
            }
        } catch (error) {
            console.error('Error searching games:', error);
            errorMessage = error instanceof Error ? error.message : 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Error searching games: ' + errorMessage,
                    'error'
                );
            }
        } finally {
            isLoading = false;
        }
    }

    // Function to format date
    function formatDate(dateString: string): string {
        if (!dateString) return 'Unknown';

        const date = new Date(dateString);
        return date.toLocaleDateString('pl-PL', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    // Function to get platform names
    function getPlatformNames(platforms: Array<{ id: number; name: string }>): string {
        if (!platforms || platforms.length === 0) return 'Unknown';

        return platforms.map(p => p.name).join(', ');
    }
</script>

<div class="card">
    <div class="header">
        <div>
            <h2>Odkrywaj gry PC</h2>
            <p class="description">Przeglądaj najnowsze i nadchodzące gry</p>
        </div>
    </div>

    <!-- Search input -->
    <div class="search-container">
        <input
            type="text"
            class="search-input"
            placeholder="Szukaj gier..."
            bind:value={searchQuery}
            on:input={handleSearchInput}
            on:keypress={handleKeyPress}
            disabled={isLoading}
        />
        <div class="search-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
        </div>
    </div>

    <!-- Tabs -->
    <div class="tabs-container">
        <button
            class="tab-button {activeTab === 'recent' ? 'active' : ''}"
            on:click={() => activeTab = 'recent'}
        >
            Najnowsze
        </button>
        <button
            class="tab-button {activeTab === 'upcoming' ? 'active' : ''}"
            on:click={() => activeTab = 'upcoming'}
        >
            Nadchodzące
        </button>
        {#if searchResults.length > 0}
            <button
                class="tab-button {activeTab === 'search' ? 'active' : ''}"
                on:click={() => activeTab = 'search'}
            >
                Wyniki wyszukiwania
            </button>
        {/if}
    </div>

    <!-- Loading indicator -->
    {#if isLoading}
        <div class="loading-container">
            <div class="loading"></div>
        </div>
    {:else if errorMessage}
        <div class="error-message">
            {errorMessage}
        </div>
    {:else}
        <!-- Recent games tab -->
        {#if activeTab === 'recent'}
            {#if recentGames.length === 0}
                <div class="empty-message">
                    Nie znaleziono najnowszych gier.
                </div>
            {:else}
                <div class="game-grid">
                    {#each recentGames as game}
                        <div class="game-card">
                            <div class="game-image-container">
                                {#if game.image && game.image.medium_url}
                                    <img src={game.image.medium_url} alt={game.name} class="game-image" />
                                {:else}
                                    <div class="no-image">
                                        <span>No image</span>
                                    </div>
                                {/if}
                            </div>
                            <h3 class="game-title">{game.name}</h3>
                            <p class="game-info">
                                Data wydania: {formatDate(game.original_release_date)}
                            </p>
                            <p class="game-info">
                                Platformy: {getPlatformNames(game.platforms)}
                            </p>
                            {#if game.deck}
                                <p class="game-description">{game.deck}</p>
                            {/if}
                            <a href={game.site_detail_url} target="_blank" rel="noopener noreferrer" class="more-info-link">
                                Więcej informacji
                            </a>
                        </div>
                    {/each}
                </div>
            {/if}
        {/if}

        <!-- Upcoming games tab -->
        {#if activeTab === 'upcoming'}
            {#if upcomingGames.length === 0}
                <div class="empty-message">
                    Nie znaleziono nadchodzących gier.
                </div>
            {:else}
                <div class="game-grid">
                    {#each upcomingGames as game}
                        <div class="game-card">
                            <div class="game-image-container">
                                {#if game.image && game.image.medium_url}
                                    <img src={game.image.medium_url} alt={game.name} class="game-image" />
                                {:else}
                                    <div class="no-image">
                                        <span>No image</span>
                                    </div>
                                {/if}
                            </div>
                            <h3 class="game-title">{game.name}</h3>
                            <p class="game-info">
                                Data wydania: {formatDate(game.original_release_date)}
                            </p>
                            <p class="game-info">
                                Platformy: {getPlatformNames(game.platforms)}
                            </p>
                            {#if game.deck}
                                <p class="game-description">{game.deck}</p>
                            {/if}
                            <a href={game.site_detail_url} target="_blank" rel="noopener noreferrer" class="more-info-link">
                                Więcej informacji
                            </a>
                        </div>
                    {/each}
                </div>
            {/if}
        {/if}

        <!-- Search results tab -->
        {#if activeTab === 'search'}
            {#if searchResults.length === 0}
                <div class="empty-message">
                    Nie znaleziono gier pasujących do zapytania.
                </div>
            {:else}
                <div class="game-grid">
                    {#each searchResults as game}
                        <div class="game-card">
                            <div class="game-image-container">
                                {#if game.image && game.image.medium_url}
                                    <img src={game.image.medium_url} alt={game.name} class="game-image" />
                                {:else}
                                    <div class="no-image">
                                        <span>No image</span>
                                    </div>
                                {/if}
                            </div>
                            <h3 class="game-title">{game.name}</h3>
                            <p class="game-info">
                                Data wydania: {formatDate(game.original_release_date)}
                            </p>
                            <p class="game-info">
                                Platformy: {getPlatformNames(game.platforms)}
                            </p>
                            {#if game.deck}
                                <p class="game-description">{game.deck}</p>
                            {/if}
                            <a href={game.site_detail_url} target="_blank" rel="noopener noreferrer" class="more-info-link">
                                Więcej informacji
                            </a>
                        </div>
                    {/each}
                </div>
            {/if}
        {/if}
    {/if}
</div>

<style>
    .game-card {
        background-color: var(--card-bg);
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid var(--border-color);
        transition: all 0.2s;
    }

    .game-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-0.25rem);
    }

    .loading {
        width: 2rem;
        height: 2rem;
        border: 4px solid var(--border-color);
        border-top-color: #ec4899;
        border-radius: 9999px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    /* Line clamp utility */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Game image container */
    .game-image-container {
        position: relative;
        overflow: hidden;
        border-radius: 0.5rem;
        margin-bottom: 0.5rem;
        aspect-ratio: 3/4;
    }

    /* Game grid */
    .game-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    @media (min-width: 768px) {
        .game-grid {
            grid-template-columns: 1fr 1fr;
        }
    }

    @media (min-width: 1024px) {
        .game-grid {
            grid-template-columns: 1fr 1fr 1fr;
        }
    }

    /* Header */
    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .description {
        color: var(--text-secondary);
    }

    /* Search */
    .search-container {
        position: relative;
        margin-bottom: 1rem;
    }

    .search-input {
        width: 100%;
        padding: 0.5rem;
        padding-left: 2.5rem;
        border-radius: 0.375rem;
        background-color: var(--input-bg);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
    }

    .search-icon {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        padding-left: 0.75rem;
        pointer-events: none;
    }

    .search-icon svg {
        color: var(--text-secondary);
    }

    /* Tabs */
    .tabs-container {
        display: flex;
        border-bottom: 1px solid var(--border-color);
        margin-bottom: 1rem;
    }

    .tab-button {
        padding: 0.5rem 1rem;
        font-weight: 500;
        color: var(--text-secondary);
    }

    .tab-button.active {
        color: #ec4899;
        border-bottom: 2px solid #ec4899;
    }

    /* Loading and error states */
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 2rem 0;
    }

    .error-message {
        color: #ef4444;
        padding: 1rem 0;
        text-align: center;
    }

    .empty-message {
        text-align: center;
        padding: 1rem 0;
        color: var(--text-secondary);
    }
</style>
