<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { Film, Tv, Search, Loader2, Subtitles } from 'lucide-svelte';
    import { getTrendingMovies, enrichWithTmdbData } from '$lib/services/traktService';
    import { getMovieLogo, getMovieBackground } from '$lib/services/fanartService';
    import { getWithAuth } from '$lib/utils/apiUtils';
    import { TMDB_CONFIG } from '$lib/config/apiConfig';
    import { addMagnet, getTorrentInfo, selectFiles, unrestrictLink } from '$lib/services/debridService';

    // Add default export
    export {};

    // Simple state without reactivity
    let activeSection = 'movies'; // 'movies', 'shows', 'search', 'searchMovies', 'searchTVShows'
    let trendingMovies: any[] = [];
    let isLoading = true;
    let error: string | null = null;
    let focusedItemIndex = 0;
    let selectedMovie: any = null;
    let movieDetails: any = null;
    let movieSources: any[] = [];
    let isLoadingDetails = false;
    let isLoadingSources = false;
    let detailsError: string | null = null;
    let sourcesError: string | null = null;
    let movieLogo: string | null = null;
    let movieBackground: string | null = null;
    let gridColumns = calculateGridColumns();

    // TV show navigation
    let selectedTVShow: any = null;
    let selectedEpisode: any = null;
    let episodeReleases: any[] = [];
    let viewMode: 'search' | 'episodes' | 'releases' = 'search';

    // Season filtering
    let availableSeasons: number[] = [];
    let selectedSeason: number | null = null;

    // Search functionality
    let searchQuery = '';
    let searchResults: any[] = [];
    let isSearching = false;
    let searchError: string | null = null;
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 1000; // 1 second debounce time

    // Video player state
    let showVideoPlayer = false;
    let videoUrl = '';
    let isLoadingVideo = false;
    let subtitlesUrl = '';

    // Flags to prevent infinite loops
    let isProcessingMovieDetails = false;
    let isLoadingTrendingMovies = false;

    // Calculate grid columns based on window width
    function calculateGridColumns() {
        if (typeof window === 'undefined') return 4; // Default for SSR

        const width = window.innerWidth;
        if (width >= 1280) return 6;
        if (width >= 768) return 4;
        return 2;
    }

    // Function to format quality for display
    function getFormattedQuality(quality: string) {
        if (!quality) return { text: 'Unknown', class: 'quality-unknown' };

        const lowerQuality = quality.toLowerCase();

        // 4K / UHD
        if (lowerQuality.includes('2160p') || lowerQuality.includes('4k') || lowerQuality.includes('uhd')) {
            return { text: '4K', class: 'quality-4k' };
        }

        // HDR
        if (lowerQuality.includes('hdr')) {
            if (lowerQuality.includes('1080p')) {
                return { text: 'HDR 1080p', class: 'quality-hdr' };
            }
            return { text: 'HDR', class: 'quality-hdr' };
        }

        // Dolby Vision
        if (lowerQuality.includes('dv') || lowerQuality.includes('dolby vision')) {
            return { text: 'Dolby Vision', class: 'quality-dolby-vision' };
        }

        // 1080p
        if (lowerQuality.includes('1080p') || lowerQuality.includes('fhd')) {
            return { text: '1080p', class: 'quality-1080p' };
        }

        // 720p
        if (lowerQuality.includes('720p') || lowerQuality.includes('hd')) {
            return { text: '720p', class: 'quality-720p' };
        }

        // SD
        if (lowerQuality.includes('480p') || lowerQuality.includes('sd')) {
            return { text: 'SD', class: 'quality-sd' };
        }

        // Default - just return the original quality
        return { text: quality, class: 'quality-unknown' };
    }

    // Update grid columns on window resize
    function updateGridColumns() {
        gridColumns = calculateGridColumns();
    }

    // Switch between sections
    function setActiveSection(section: string) {
        console.log(`Switching to section: ${section}`);

        // Reset state when switching tabs
        if (selectedMovie) {
            // Close movie details
            closeMovieDetails();
        }

        // Close video player if open
        if (showVideoPlayer) {
            closeVideoPlayer();
        }

        // Reset TV show navigation if switching away from shows section
        if (activeSection === 'shows' && section !== 'shows') {
            if (selectedTVShow || selectedEpisode) {
                resetTVShowNavigation();
            }
        }

        // Reset search state if switching away from search sections
        if ((activeSection === 'searchMovies' || activeSection === 'searchTVShows') &&
            (section !== 'searchMovies' && section !== 'searchTVShows')) {
            // Clear search results and query
            searchQuery = '';
            searchResults = [];
            searchError = null;
            isSearching = false;
            clearTimeout(typingTimer);
        }

        // Set the active section
        activeSection = section;

        // If switching to search sections, focus on search input after a short delay
        if (section === 'searchMovies' || section === 'searchTVShows') {
            setTimeout(() => {
                const searchInput = document.getElementById('tvSearchQuery');
                if (searchInput) {
                    searchInput.focus();
                }
            }, 100);
        } else if (section === 'shows') {
            // If switching to shows section, focus on search input after a short delay
            setTimeout(() => {
                const searchInput = document.getElementById('tvShowsSearchQuery');
                if (searchInput) {
                    searchInput.focus();
                }
            }, 100);
        }
    }

    // Function to handle search input changes with debounce
    function handleSearchInput() {
        clearTimeout(typingTimer);
        if (searchQuery.trim().length >= 3) {
            typingTimer = setTimeout(searchMedia, doneTypingInterval);
        } else {
            searchResults = [];
        }
    }

    // Function to handle key press in search input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && searchQuery.trim().length >= 3) {
            clearTimeout(typingTimer);
            searchMedia();
        }
    }

    // Function to search for movies and TV shows
    async function searchMedia() {
        if (searchQuery.trim().length < 3) {
            searchResults = [];

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wpisz co najmniej 3 znaki', 'error');
            }
            return;
        }

        isSearching = true;
        searchResults = [];
        searchError = null;

        // Reset TV show navigation when performing a new search in the shows section
        if (activeSection === 'shows' && (selectedTVShow || selectedEpisode)) {
            resetTVShowNavigation();
        }

        try {
            // Add content type to the search URL based on active section
            let url = `/api/cinema/search?query=${encodeURIComponent(searchQuery)}`;

            if (activeSection === 'searchMovies') {
                url += '&type=movie';
            } else if (activeSection === 'searchTVShows' || activeSection === 'shows') {
                url += '&type=series';
            }

            console.log(`Searching with URL: ${url}`);

            // Use getWithAuth instead of fetch to include the JWT token
            const response = await getWithAuth(url);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Error ${response.status}`);
            }

            const data = await response.json();
            let results = data.results || [];

            // Filter results based on active section if needed
            if (activeSection === 'searchMovies') {
                results = results.filter((result: any) => result.type === 'movie');
            } else if (activeSection === 'searchTVShows' || activeSection === 'shows') {
                results = results.filter((result: any) => result.type === 'tvshow');
            }

            // If we're in the TV shows section, we need to handle episodes differently
            if (activeSection === 'shows') {
                // Keep all results for TV shows section (both shows and episodes)
                searchResults = results;

                // Log the breakdown of TV shows vs episodes
                const showsCount = results.filter((r: any) => r.type === 'tvshow' && !r.episodeInfo).length;
                const episodesCount = results.filter((r: any) => r.type === 'tvshow' && r.episodeInfo).length;
                console.log(`Found ${showsCount} TV shows and ${episodesCount} episodes`);

                // If we don't have any TV shows but have episodes, we need to create show entries
                if (showsCount === 0 && episodesCount > 0) {
                    console.log('No TV shows found, but episodes exist. Creating show entries from episodes...');

                    // Group episodes by show (using imdbID)
                    const showsMap = new Map();

                    results.forEach((result: any) => {
                        if (result.type === 'tvshow' && result.episodeInfo && result.imdbID) {
                            if (!showsMap.has(result.imdbID)) {
                                // Create a show entry from the episode
                                const showEntry = { ...result };
                                delete showEntry.episodeInfo;
                                showsMap.set(result.imdbID, showEntry);
                            }
                        }
                    });

                    // Add the created show entries to the results
                    const showEntries = Array.from(showsMap.values());
                    console.log(`Created ${showEntries.length} show entries from episodes`);

                    // Add the show entries to the beginning of the results
                    searchResults = [...showEntries, ...results];
                }
            } else {
                // For other sections, remove duplicates by creating a Map with imdbID as key
                const uniqueResults = new Map();
                results.forEach((result: any) => {
                    if (result.imdbID && !uniqueResults.has(result.imdbID)) {
                        uniqueResults.set(result.imdbID, result);
                    }
                });

                searchResults = Array.from(uniqueResults.values());
            }

            // No need to show a notification for no results - we'll display it in the UI
        } catch (error) {
            console.error('Error searching media:', error);
            searchError = error instanceof Error ? error.message : 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Błąd wyszukiwania: ${searchError}`, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Load trending movies
    function loadTrendingMovies() {
        // Prevent infinite loops by checking if we're already loading trending movies
        if (isLoadingTrendingMovies) {
            console.log('Already loading trending movies, skipping duplicate call');
            return;
        }

        console.log('Starting to load trending movies...');
        isLoadingTrendingMovies = true;
        isLoading = true;
        error = null;

        // Use a non-reactive approach with callbacks to avoid infinite loops
        const loadMoviesAsync = async () => {
            try {
                console.log('Loading trending movies from Trakt.tv...');

                // Get trending movies from Trakt.tv (limited to 24)
                const movies = await getTrendingMovies(24);
                console.log(`Loaded ${movies.length} trending movies from Trakt.tv`);

                // Enrich with poster images from TMDB
                const enrichedMovies = await enrichWithTmdbData(movies);
                console.log('Enriched movies with posters:', enrichedMovies.filter(item => item.poster).length);

                // Update state
                trendingMovies = enrichedMovies;
                isLoading = false;
                error = null;
            } catch (err: any) {
                console.error('Error loading trending movies:', err);
                error = err.message || 'Failed to load trending movies';
                isLoading = false;
            } finally {
                isLoadingTrendingMovies = false;
            }
        };

        // Start loading
        loadMoviesAsync();
    }

    // Open movie details
    function openMovieDetails(movie: any) {
        console.log('Opening movie details for:', movie.title);

        // Switch to movies section to show details
        activeSection = 'movies';

        selectedMovie = movie;
        movieDetails = null;
        movieSources = [];
        detailsError = null;
        sourcesError = null;
        movieLogo = null;
        movieBackground = null;

        // Load movie details
        loadMovieDetails(movie);
        // Load movie artwork
        loadMovieArtwork(movie);
        // Load movie sources
        loadMovieSources(movie);
    }

    // Close movie details
    function closeMovieDetails() {
        console.log('Closing movie details');
        selectedMovie = null;
        movieDetails = null;
        movieSources = [];
        detailsError = null;
        sourcesError = null;
        movieLogo = null;
        movieBackground = null;

        // No need to change activeSection - we'll keep the user in the same section they were in
    }

    // Function to select a TV show
    function selectTVShow(tvShow: any) {
        console.log(`Selecting TV show: ${tvShow.title}`);
        selectedTVShow = tvShow;
        selectedEpisode = null;
        selectedSeason = null; // Reset selected season
        viewMode = 'episodes';

        // Extract all episodes for this TV show
        const episodes = searchResults.filter(result =>
            result.type === 'tvshow' &&
            result.imdbID === tvShow.imdbID &&
            result.episodeInfo !== null
        );

        console.log(`Found ${episodes.length} episodes for ${tvShow.title}`);

        // If no episodes found, we might need to search for them
        if (episodes.length === 0) {
            console.log(`No episodes found for ${tvShow.title}, searching for episodes...`);
            // We'll keep the user in the episodes view and show a loading state
            // The actual search will happen in the UI
            return;
        }

        // Extract available seasons
        availableSeasons = [];
        episodes.forEach(episode => {
            if (episode.episodeInfo && !availableSeasons.includes(episode.episodeInfo.season)) {
                availableSeasons.push(episode.episodeInfo.season);
            }
        });

        // Sort seasons in ascending order
        availableSeasons.sort((a, b) => a - b);

        console.log(`Found ${availableSeasons.length} seasons for ${tvShow.title}`);

        // Log the episodes per season for debugging
        availableSeasons.forEach(season => {
            const seasonEpisodes = episodes.filter(ep => ep.episodeInfo.season === season);
            console.log(`Season ${season}: ${seasonEpisodes.length} episodes`);
        });
    }

    // Function to select an episode
    function selectEpisode(episode: any) {
        selectedEpisode = episode;
        viewMode = 'releases';

        // Get all releases for this episode
        const imdbId = selectedTVShow.imdbID;
        const season = episode.episodeInfo.season;
        const episodeNum = episode.episodeInfo.episode;

        console.log(`Selected episode: ${selectedTVShow.title} S${season}E${episodeNum}`);

        // Fetch episode releases from the API
        fetchEpisodeReleases(imdbId, season, episodeNum);
    }

    // Function to fetch episode releases
    async function fetchEpisodeReleases(imdbId: string, season: number, episode: number) {
        try {
            isSearching = true;
            episodeReleases = []; // Clear previous releases

            console.log(`Fetching releases for ${imdbId} S${season}E${episode}`);

            // Call the API to get all releases for this episode
            // Use getWithAuth instead of fetch to include the JWT token
            const response = await getWithAuth(`/api/cinema/episode?imdbId=${imdbId}&season=${season}&episode=${episode}`);

            if (!response.ok) {
                const errorData = await response.json();

                // Check if there's an alternative IMDB ID suggestion
                if (errorData && errorData.alternativeImdbId) {
                    console.log(`API suggests alternative IMDB ID: ${errorData.alternativeImdbId}`);

                    // Show notification to the user
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert(`Próbuję użyć alternatywnego ID: ${errorData.alternativeImdbId}`, 'info');
                    }

                    // Update the selected TV show with the new IMDB ID
                    selectedTVShow = {
                        ...selectedTVShow,
                        imdbID: errorData.alternativeImdbId,
                        _originalImdbID: selectedTVShow.imdbID // Keep the original for reference
                    };

                    // Try fetching episodes again with the new IMDB ID
                    await fetchEpisodeReleases(errorData.alternativeImdbId, season, episode);
                    return;
                }

                throw new Error(errorData.error || `Error ${response.status}`);
            }

            const data = await response.json();

            if (Array.isArray(data) && data.length > 0) {
                console.log(`Found ${data.length} releases for S${season}E${episode}`);

                // Log the first few releases for debugging
                console.log('First 3 releases:', data.slice(0, 3));

                episodeReleases = data;

                // Sort releases by quality and seeders
                episodeReleases.sort((a, b) => {
                    // First sort by quality rank (highest first)
                    const qualityRankA = getQualityRank(a.quality);
                    const qualityRankB = getQualityRank(b.quality);

                    if (qualityRankB !== qualityRankA) {
                        return qualityRankB - qualityRankA;
                    }

                    // If quality ranks are the same, sort by seeders (highest first)
                    return b.seeders - a.seeders;
                });

                // Log the sorted releases for debugging
                console.log('Sorted releases by quality and seeders');
            } else {
                console.error(`No releases found for S${season}E${episode}`);

                // Check if data contains an error message
                if (data && data.error) {
                    console.error(`API error: ${data.error}`);

                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert(`Błąd: ${data.error}`, 'error');
                    }
                } else {
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert(`Nie znaleziono wydań dla S${season}E${episode}`, 'error');
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching episode releases:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd podczas pobierania wydań: ${error instanceof Error ? error.message : 'Nieznany błąd'}`, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Function to go back to episodes view
    function backToEpisodes() {
        selectedEpisode = null;
        episodeReleases = [];
        viewMode = 'episodes';
    }

    // Function to filter episodes by season
    function filterBySeason(season: number | null) {
        console.log(`Filtering by season: ${season === null ? 'All seasons' : season}`);
        selectedSeason = season;

        // Count episodes for the selected season
        const filteredEpisodes = searchResults.filter(result =>
            result.type === 'tvshow' &&
            result.imdbID === selectedTVShow.imdbID &&
            result.episodeInfo !== null &&
            (selectedSeason === null || result.episodeInfo.season === selectedSeason)
        );

        console.log(`Found ${filteredEpisodes.length} episodes for ${selectedSeason === null ? 'all seasons' : 'season ' + selectedSeason}`);
    }

    // Function to reset TV show navigation
    function resetTVShowNavigation() {
        console.log('Resetting TV show navigation');
        selectedTVShow = null;
        selectedEpisode = null;
        episodeReleases = [];
        viewMode = 'search';
        availableSeasons = [];
        selectedSeason = null;

        // If we're in the shows section, make sure we're showing the search view
        if (activeSection === 'shows') {
            // No need to change activeSection, just reset the view mode
            console.log('Returning to TV shows search view');
        }
    }

    // Function to handle playing a video in the browser
    async function handlePlayInBrowser(source: any) {
        try {
            // Show notification about starting the process
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie odtwarzacza...`, 'info');
            }

            // Get the magnet link
            const magnetLink = source.magnetLink || source.magnet;

            if (!magnetLink) {
                throw new Error('Brak linku magnet');
            }

            // Set loading state
            isLoadingVideo = true;

            // Use addMagnet function from debridService
            console.log(`Adding magnet with instant availability check`);
            const data = await addMagnet(magnetLink);
            let torrentId = data.id;

            if (!torrentId) {
                throw new Error('Nie udało się dodać torrenta');
            }

            console.log(`Torrent added with ID: ${torrentId}`);

            // Wait for torrent to be ready
            let torrentInfo;
            let maxAttempts = 10;
            let attempts = 0;
            let isReady = false;

            while (!isReady && attempts < maxAttempts) {
                attempts++;
                console.log(`Checking torrent status (attempt ${attempts}/${maxAttempts})...`);

                // Get torrent info using debridService
                torrentInfo = await getTorrentInfo(torrentId);
                console.log(`Torrent status:`, torrentInfo.status);

                if (torrentInfo.status === 'downloaded' || torrentInfo.status === 'magnet_conversion') {
                    isReady = true;
                } else if (torrentInfo.status === 'waiting_files_selection') {
                    console.log('Selecting all files automatically...');
                    // Select all files using debridService
                    try {
                        await selectFiles(torrentId);
                        console.log('Successfully selected all files');
                    } catch (error) {
                        console.error('Error selecting files:', error);
                        // Continue anyway, don't throw
                    }

                    // Wait a bit after selecting files
                    await new Promise(resolve => setTimeout(resolve, 2000));
                } else {
                    // Wait before next attempt
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            if (!isReady) {
                throw new Error('Torrent nie jest gotowy po maksymalnej liczbie prób');
            }

            // Get the list of files using debridService
            torrentInfo = await getTorrentInfo(torrentId);

            if (!torrentInfo.files || torrentInfo.files.length === 0) {
                throw new Error('Brak plików w torrencie');
            }

            console.log(`Torrent has ${torrentInfo.files.length} files`);

            // Find the largest video file
            const videoExtensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm'];
            let videoFile = null;
            let maxSize = 0;

            for (const file of torrentInfo.files) {
                const fileExt = file.path.substring(file.path.lastIndexOf('.')).toLowerCase();
                if (videoExtensions.includes(fileExt) && file.bytes > maxSize) {
                    videoFile = file;
                    maxSize = file.bytes;
                }
            }

            if (!videoFile) {
                throw new Error('Nie znaleziono pliku wideo w torrencie');
            }

            console.log(`Selected video file: ${videoFile.path} (${videoFile.bytes} bytes)`);

            // Get the download link from the videoFile
            let downloadLink = '';

            if ((videoFile as any).download) {
                // Standard structure - link is in the file
                downloadLink = (videoFile as any).download;
                console.log(`Found download link in videoFile: ${downloadLink}`);
            } else if (torrentInfo.links && torrentInfo.links.length > 0) {
                // Alternative structure - link is in the links array at the main level
                downloadLink = torrentInfo.links[0];
                console.log(`Found download link in torrentInfo.links: ${downloadLink}`);
            } else {
                console.error('No download link in file or torrent:', videoFile, torrentInfo);
                throw new Error('Brak linku do pobrania dla wybranego pliku');
            }

            // Get the unrestricted link
            console.log(`Sending request to unrestrict link: ${downloadLink}`);

            let streamUrl = '';

            try {
                // Use unrestrictLink function from debridService
                const unrestrictData = await unrestrictLink(downloadLink);
                console.log(`Unrestrict data:`, unrestrictData);

                if (!unrestrictData.download) {
                    console.error(`Missing download property in unrestrict response:`, unrestrictData);
                    throw new Error('Nie udało się pobrać linku do odtwarzania');
                }

                streamUrl = unrestrictData.download;
                console.log(`Successfully got unrestricted link: ${streamUrl.substring(0, 50)}...`);
            } catch (error) {
                console.error(`Error during unrestrict API call:`, error);
                throw error;
            }

            // Set the video URL
            videoUrl = streamUrl;

            // Show the player
            showVideoPlayer = true;

            // Show success notification
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Odtwarzanie rozpoczęte!`, 'success');
            }

        } catch (error) {
            console.error('Error handling play in browser:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd: ${error instanceof Error ? error.message : 'Nieznany błąd'}`, 'error');
            }
            isLoadingVideo = false;
        }
    }

    // Function to close the video player
    function closeVideoPlayer() {
        showVideoPlayer = false;
        videoUrl = '';
        subtitlesUrl = '';
        isLoadingVideo = false;
    }

    // Function to toggle subtitles
    function toggleSubtitles(enable: boolean) {
        if (typeof window !== 'undefined') {
            const video = document.getElementById('videoPlayer') as HTMLVideoElement;
            if (video && video.textTracks && video.textTracks.length > 0) {
                for (let i = 0; i < video.textTracks.length; i++) {
                    video.textTracks[i].mode = enable ? 'showing' : 'hidden';
                }

                // Show notification
                if (typeof window !== 'undefined' && (window as any).showNotification) {
                    (window as any).showNotification(
                        enable ? 'Napisy włączone' : 'Napisy wyłączone',
                        enable ? 'success' : 'info'
                    );
                }
            }
        }
    }

    // Load movie details
    async function loadMovieDetails(movie: any) {
        // Prevent infinite loops
        if (isProcessingMovieDetails) {
            console.log('Already processing movie details, skipping duplicate call');
            return;
        }

        console.log('Loading movie details for:', movie.title);
        isProcessingMovieDetails = true;
        isLoadingDetails = true;
        detailsError = null;

        try {
            // Check if movie has ids and tmdb id
            if (!movie.ids || !movie.ids.tmdb) {
                console.log('Movie missing TMDB ID, using basic details');
                movieDetails = {
                    title: movie.title,
                    release_date: movie.year ? `${movie.year}-01-01` : '',
                    overview: movie.overview || 'No description available',
                    genres: []
                };
                return;
            }

            // Get movie details directly from TMDB API using the configuration
            const { API_KEY } = TMDB_CONFIG;
            const tmdbUrl = `${TMDB_CONFIG.BASE_URL}/movie/${movie.ids.tmdb}?api_key=${API_KEY}&language=pl-PL`;

            console.log(`Fetching TMDB data for ${movie.title} (ID: ${movie.ids.tmdb})`);
            const response = await fetch(tmdbUrl);

            if (!response.ok) {
                throw new Error(`TMDB API error: ${response.status}`);
            }

            const data = await response.json();
            console.log('Movie details loaded:', data.title);

            // Add poster path with proxy
            if (data.poster_path) {
                const originalPosterUrl = `${TMDB_CONFIG.IMAGE_BASE_URL}/w500${data.poster_path}`;
                data.fullPosterPath = `/api/image/proxy?url=${encodeURIComponent(originalPosterUrl)}`;
            }

            // Add backdrop path with proxy
            if (data.backdrop_path) {
                const originalBackdropUrl = `${TMDB_CONFIG.IMAGE_BASE_URL}/original${data.backdrop_path}`;
                data.fullBackdropPath = `/api/image/proxy?url=${encodeURIComponent(originalBackdropUrl)}`;
            }

            movieDetails = data;
        } catch (err: any) {
            console.error('Error loading movie details:', err);
            detailsError = err.message || 'Failed to load movie details';

            // Try to get basic details from what we already have
            movieDetails = {
                title: movie.title,
                release_date: movie.year ? `${movie.year}-01-01` : '',
                overview: movie.overview || 'No description available',
                genres: []
            };
        } finally {
            isLoadingDetails = false;
            isProcessingMovieDetails = false;
        }
    }

    // Load movie artwork
    async function loadMovieArtwork(movie: any) {
        console.log('Loading movie artwork for:', movie.title);

        try {
            // Check if movie has ids and tmdb id
            if (!movie.ids || !movie.ids.tmdb) {
                console.log('Movie missing TMDB ID, skipping artwork loading');
                return;
            }

            // Get movie logo from Fanart.tv
            const logoPromise = getMovieLogo(movie.ids.tmdb);
            const backgroundPromise = getMovieBackground(movie.ids.tmdb);

            // Wait for all promises to resolve
            const [logo, background] = await Promise.all([
                logoPromise,
                backgroundPromise
            ]);

            console.log('Movie artwork loaded:', {
                logo: logo ? 'Yes' : 'No',
                background: background ? 'Yes' : 'No'
            });

            // Use proxy for images to avoid CORS issues
            if (logo) {
                movieLogo = `/api/image/proxy?url=${encodeURIComponent(logo)}`;
            }

            if (background) {
                movieBackground = `/api/image/proxy?url=${encodeURIComponent(background)}`;
            }
        } catch (err) {
            console.error('Error loading movie artwork:', err);
            // We don't set an error state for artwork, just log it
        }
    }

    // Helper function to determine quality rank for sorting
    function getQualityRank(quality: string | null) {
        if (!quality) return 0;

        const qualityLower = quality.toLowerCase();

        // Check for the highest priority: 4K HDR DV
        if ((qualityLower.includes('4k') || qualityLower.includes('2160p') || qualityLower.includes('uhd')) &&
            qualityLower.includes('hdr') &&
            (qualityLower.includes('dv') || qualityLower.includes('dolby vision'))) {
            return 60; // 4K HDR DV
        }
        // Check for 4K HDR
        else if ((qualityLower.includes('4k') || qualityLower.includes('2160p') || qualityLower.includes('uhd')) &&
                qualityLower.includes('hdr')) {
            return 50; // 4K HDR
        }
        // Check for 4K DV (without explicit HDR mention)
        else if ((qualityLower.includes('4k') || qualityLower.includes('2160p') || qualityLower.includes('uhd')) &&
                (qualityLower.includes('dv') || qualityLower.includes('dolby vision'))) {
            return 45; // 4K DV
        }
        // Check for 4K
        else if (qualityLower.includes('4k') || qualityLower.includes('2160p') || qualityLower.includes('uhd')) {
            return 40; // 4K
        }
        // Check for 1080P HDR DV
        else if ((qualityLower.includes('1080p') || qualityLower.includes('fhd')) &&
                qualityLower.includes('hdr') &&
                (qualityLower.includes('dv') || qualityLower.includes('dolby vision'))) {
            return 30; // 1080P HDR DV
        }
        // Check for 1080P HDR
        else if ((qualityLower.includes('1080p') || qualityLower.includes('fhd')) &&
                qualityLower.includes('hdr')) {
            return 25; // 1080P HDR
        }
        // Check for 1080P DV (without explicit HDR mention)
        else if ((qualityLower.includes('1080p') || qualityLower.includes('fhd')) &&
                (qualityLower.includes('dv') || qualityLower.includes('dolby vision'))) {
            return 20; // 1080P DV
        }
        // Check for 1080P
        else if (qualityLower.includes('1080p') || qualityLower.includes('fhd')) {
            return 15; // 1080P
        }
        // Check for 720P
        else if (qualityLower.includes('720p') || qualityLower.includes('hd')) {
            return 10; // 720P
        }
        // Check for 480P
        else if (qualityLower.includes('480p') || qualityLower.includes('sd')) {
            return 5; // 480P
        }
        // Check for DV only (no resolution specified)
        else if (qualityLower.includes('dv') || qualityLower.includes('dolby vision')) {
            return 3; // DV only
        }
        // Check for HDR only (no resolution specified)
        else if (qualityLower.includes('hdr')) {
            return 2; // HDR only
        }

        return 1; // Unknown quality
    }

    // We're using the getFormattedQuality function defined earlier


    // Load movie sources
    async function loadMovieSources(movie: any) {
        console.log('Loading movie sources for:', movie.title);
        isLoadingSources = true;
        sourcesError = null;

        try {
            // Get movie sources from Torrentio via the cinema API
            const response = await getWithAuth(`/api/cinema/search?query=${encodeURIComponent(movie.title)}&type=movie`);

            if (!response.ok) {
                throw new Error(`API error: ${response.status}`);
            }

            const data = await response.json();
            let sourceData = [];

            // Check if data is an array or has results property
            if (Array.isArray(data)) {
                sourceData = data;
            } else if (data.results && Array.isArray(data.results)) {
                console.log('Using data.results instead');
                sourceData = data.results;
            } else {
                throw new Error('Invalid response format from API');
            }

            // Filter results to match the current movie's IMDB ID if possible
            let filteredSources = [];
            if (movie.ids && movie.ids.imdb) {
                filteredSources = sourceData.filter((item: any) =>
                    item.imdbID === movie.ids.imdb && item.type === 'movie'
                );

                console.log(`Movie sources loaded: ${filteredSources.length} sources`);

                if (filteredSources.length === 0) {
                    console.log('No exact matches found, using all movie results');
                    // If no IMDB match found, use all movie results
                    filteredSources = sourceData.filter((item: any) => item.type === 'movie');
                }
            } else {
                // If no IMDB ID available, use all movie results
                filteredSources = sourceData.filter((item: any) => item.type === 'movie');
            }

            console.log(`Using ${filteredSources.length} movie results`);

            // Log sources before sorting
            console.log('Sources before sorting:', filteredSources.map((s: any) => ({
                title: s.title,
                quality: s.quality,
                rank: getQualityRank(s.quality),
                seeders: s.seeders
            })));

            // Sort sources by quality according to the specified hierarchy
            filteredSources.sort((a: any, b: any) => {
                // First sort by quality rank (highest first)
                const qualityRankA = getQualityRank(a.quality);
                const qualityRankB = getQualityRank(b.quality);

                if (qualityRankB !== qualityRankA) {
                    return qualityRankB - qualityRankA;
                }

                // If quality ranks are the same, sort by seeders (highest first)
                return b.seeders - a.seeders;
            });

            // Log sources after sorting
            console.log('Sources after sorting:', filteredSources.map((s: any) => ({
                title: s.title,
                quality: s.quality,
                rank: getQualityRank(s.quality),
                seeders: s.seeders
            })));

            movieSources = filteredSources;
            console.log('Sources sorted by quality hierarchy');

        } catch (err: any) {
            console.error('Error loading movie sources:', err);
            sourcesError = err.message || 'Failed to load movie sources';
        } finally {
            isLoadingSources = false;
        }
    }

    // Handle keyboard navigation
    function handleKeyDown(event: KeyboardEvent) {
        if (!selectedMovie) {
            // Grid navigation
            switch (event.key) {
                case 'ArrowUp':
                    event.preventDefault();
                    focusedItemIndex = Math.max(0, focusedItemIndex - gridColumns);
                    applyFocusEffects();
                    break;
                case 'ArrowDown':
                    event.preventDefault();
                    focusedItemIndex = Math.min(trendingMovies.length - 1, focusedItemIndex + gridColumns);
                    applyFocusEffects();
                    break;
                case 'ArrowLeft':
                    event.preventDefault();
                    focusedItemIndex = Math.max(0, focusedItemIndex - 1);
                    applyFocusEffects();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    focusedItemIndex = Math.min(trendingMovies.length - 1, focusedItemIndex + 1);
                    applyFocusEffects();
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (trendingMovies[focusedItemIndex]) {
                        openMovieDetails(trendingMovies[focusedItemIndex]);
                    }
                    break;
                case 'Escape':
                    event.preventDefault();
                    // If in a section, go back to main menu
                    break;
            }
        } else {
            // Detail view navigation
            switch (event.key) {
                case 'Escape':
                case 'Backspace':
                    event.preventDefault();
                    closeMovieDetails();
                    break;
            }
        }
    }

    // Apply focus effects to the currently focused item
    function applyFocusEffects() {
        if (typeof document !== 'undefined') {
            // First, remove focus class from all cards
            document.querySelectorAll('.movie-card').forEach(card => {
                card.classList.remove('focused');
            });

            // Add focus class to the currently focused card
            const focusedElement = document.querySelector(`.movie-card[data-index="${focusedItemIndex}"]`);
            if (focusedElement) {
                focusedElement.classList.add('focused');

                // Scroll the focused item into view with a smooth animation
                focusedElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            }
        }
    }

    // Initialize component
    onMount(() => {
        console.log('TV component mounted');

        // Add event listeners
        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('resize', updateGridColumns);

        // Load trending movies
        setTimeout(() => {
            if (!isLoadingTrendingMovies && trendingMovies.length === 0) {
                loadTrendingMovies();
            }

            // Apply focus effects to the first item after a short delay
            setTimeout(() => {
                if (trendingMovies.length > 0) {
                    focusedItemIndex = 0;
                    applyFocusEffects();
                }
            }, 1000);
        }, 100);
    });

    // Cleanup component
    onDestroy(() => {
        console.log('TV component destroyed');
        window.removeEventListener('keydown', handleKeyDown);
        window.removeEventListener('resize', updateGridColumns);
    });
</script>

<div class="tv-container">
    <!-- Navigation menu -->
    <nav class="tv-nav">
        <button
            class="tv-nav-button {activeSection === 'movies' ? 'active' : ''}"
            on:click={() => setActiveSection('movies')}
            aria-label="Movies"
        >
            <Film size={32} />
        </button>
        <button
            class="tv-nav-button {activeSection === 'shows' ? 'active' : ''}"
            on:click={() => setActiveSection('shows')}
            aria-label="TV Shows"
        >
            <Tv size={32} />
        </button>
        <button
            class="tv-nav-button {activeSection === 'searchMovies' ? 'active' : ''}"
            on:click={() => setActiveSection('searchMovies')}
            aria-label="Search Movies"
        >
            <div class="search-icon-container">
                <Search size={32} />
                <Film size={16} class="search-sub-icon" />
            </div>
        </button>
        <button
            class="tv-nav-button {activeSection === 'searchTVShows' ? 'active' : ''}"
            on:click={() => setActiveSection('searchTVShows')}
            aria-label="Search TV Shows"
        >
            <div class="search-icon-container">
                <Search size={32} />
                <Tv size={16} class="search-sub-icon" />
            </div>
        </button>
    </nav>

    <!-- Content area -->
    <main class="tv-content">
        <div class="tv-content-inner"> <!-- Added wrapper -->
            {#if selectedMovie}
            <!-- Movie details view -->
            <div class="movie-details-container" style={movieBackground ? `background-image: url(${movieBackground})` : ''}>
                <div class="movie-details-overlay"></div>

                <div class="movie-details-header">
                    <button
                        class="back-button"
                        on:click={() => closeMovieDetails()}
                        on:keydown={(e) => e.key === 'Enter' && closeMovieDetails()}
                        tabindex="0"
                        aria-label="Back to movies"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                        <span>Wróć</span>
                    </button>
                </div>

                <div class="movie-details-content">
                    <div class="movie-details-poster">
                        {#if selectedMovie.poster}
                            <img src={selectedMovie.poster} alt="{selectedMovie.title} poster" class="movie-details-poster-img" />
                        {:else}
                            <div class="movie-poster-placeholder">
                                <Film size={64} />
                            </div>
                        {/if}
                    </div>

                    <div class="movie-details-info">
                        {#if movieLogo}
                            <img src={movieLogo} alt="{selectedMovie.title} logo" class="movie-logo" fetchpriority="high" />
                        {:else}
                            <h2 class="movie-title">{selectedMovie.title}</h2>
                        {/if}

                        {#if isLoadingDetails}
                            <div class="loading-container">
                                <div class="loading"></div>
                                <p>Loading details...</p>
                            </div>
                        {:else if detailsError}
                            <div class="error-container">
                                <p>{detailsError}</p>
                            </div>
                        {:else if movieDetails}
                            <div class="movie-meta">
                                <div class="movie-year">{selectedMovie.year}</div>
                                {#if movieDetails.runtime}
                                    <div class="movie-runtime">{movieDetails.runtime} min</div>
                                {/if}
                                {#if movieDetails.vote_average}
                                    <div class="movie-rating">{movieDetails.vote_average.toFixed(1)}/10</div>
                                {/if}
                            </div>

                            {#if movieDetails.overview}
                                <div class="movie-overview">
                                    <p>{movieDetails.overview}</p>
                                </div>
                            {/if}

                            {#if movieDetails.genres && movieDetails.genres.length > 0}
                                <div class="movie-genres">
                                    {#each movieDetails.genres as genre}
                                        <span class="genre-tag">{genre.name}</span>
                                    {/each}
                                </div>
                            {/if}
                        {/if}

                        <div class="movie-sources">
                            <h3 class="sources-title">Źródła</h3>

                            {#if isLoadingSources}
                                <div class="loading-container">
                                    <div class="loading"></div>
                                    <p>Loading sources...</p>
                                </div>
                            {:else if sourcesError}
                                <div class="error-container">
                                    <p>{sourcesError}</p>
                                </div>
                            {:else if movieSources.length === 0}
                                <div class="empty-container">
                                    <p>No sources found</p>
                                </div>
                            {:else}
                                <div class="sources-list">
                                    {#each movieSources as source}
                                        <div
                                            class="source-item"
                                            tabindex="0"
                                            role="button"
                                            aria-label="Play {source.title}"
                                            on:keydown={(e) => e.key === 'Enter' && handlePlayInBrowser(source)}
                                            on:click={() => handlePlayInBrowser(source)}
                                        >
                                            <div class="source-info">
                                                <div class="source-title">{source.title}</div>
                                                <div class="source-meta">
                                                    {#if source.quality}
                                                        {@const formattedQuality = getFormattedQuality(source.quality)}
                                                        <span class="source-quality {formattedQuality.class}">{formattedQuality.text}</span>
                                                    {/if}
                                                    {#if source.size}
                                                        <span class="source-size">{source.size}</span>
                                                    {/if}
                                                </div>
                                                {#if source.filename}
                                                    <div class="source-filename" title={source.filename}>
                                                        {source.filename}
                                                    </div>
                                                {:else if source.originalTitle}
                                                    <div class="source-filename" title={source.originalTitle}>
                                                        {source.originalTitle}
                                                    </div>
                                                {/if}
                                            </div>
                                            <div class="source-action">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                                </svg>
                                            </div>
                                        </div>
                                    {/each}
                                </div>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        {/if}
        {#if !selectedMovie}
            {#if activeSection === 'movies'}
                <div class="section-title">
                    <h1>Filmy</h1>
                </div>

                {#if isLoading}
                    <div class="loading-container">
                        <div class="loading"></div>
                        <p>Loading trending movies...</p>
                    </div>
                {:else if error}
                    <div class="error-container">
                        <p>{error}</p>
                        <button class="btn" on:click={loadTrendingMovies}>Try Again</button>
                    </div>
                {:else if trendingMovies.length === 0}
                    <div class="empty-container">
                        <p>No movies found</p>
                    </div>
                {:else}
                    <div class="movie-grid">
                        {#each trendingMovies as movie, index}
                            <div
                                class="movie-card {focusedItemIndex === index ? 'focused' : ''}"
                                data-index={index}
                                tabindex="0"
                                role="button"
                                aria-label={movie.title}
                                on:focus={() => {
                                    focusedItemIndex = index;
                                    applyFocusEffects();
                                }}
                                on:mouseenter={() => {
                                    focusedItemIndex = index;
                                    applyFocusEffects();
                                }}
                                on:click={() => openMovieDetails(movie)}
                                on:keydown={(e) => e.key === 'Enter' && openMovieDetails(movie)}
                            >
                                <div class="movie-poster">
                                    {#if movie.poster}
                                        <img src={movie.poster} alt="{movie.title} poster" class="movie-poster-img" />
                                    {:else}
                                        <div class="movie-poster-placeholder">
                                            <Film size={32} />
                                        </div>
                                    {/if}
                                </div>
                                <div class="movie-info">
                                    <div class="movie-title">{movie.title}</div>
                                    <div class="movie-year">{movie.year}</div>
                                </div>
                            </div>
                        {/each}
                    </div>
                {/if}
            {:else if activeSection === 'shows'}
                {#if selectedTVShow && viewMode === 'episodes'}
                    <!-- TV Show Episodes View -->
                    <div class="navigation-header">
                        <button class="back-button" on:click={resetTVShowNavigation}>
                            <span>← Powrót do wyszukiwania</span>
                        </button>
                        <h3 class="tv-show-title">{selectedTVShow.title} {selectedTVShow.year ? `(${selectedTVShow.year})` : ''}</h3>
                    </div>

                    <!-- Season filter -->
                    {#if availableSeasons.length > 1}
                        <div class="season-filter">
                            <button
                                class="season-btn {selectedSeason === null ? 'active' : ''}"
                                on:click={() => filterBySeason(null)}
                            >
                                Wszystkie sezony
                            </button>

                            {#each availableSeasons as season}
                                <button
                                    class="season-btn {selectedSeason === season ? 'active' : ''}"
                                    on:click={() => filterBySeason(season)}
                                >
                                    Sezon {season}
                                </button>
                            {/each}
                        </div>
                    {/if}

                    <!-- Episodes list -->
                    {@const filteredEpisodes = searchResults.filter(result =>
                        result.type === 'tvshow' &&
                        result.imdbID === selectedTVShow.imdbID &&
                        result.episodeInfo !== null &&
                        (selectedSeason === null || result.episodeInfo.season === selectedSeason)
                    )}

                    {#if filteredEpisodes.length === 0}
                        <div class="empty-state">
                            <p>Nie znaleziono odcinków dla tego serialu{selectedSeason !== null ? ` (Sezon ${selectedSeason})` : ''}</p>
                            <button class="btn btn-outline" style="margin-top: 10px;" on:click={() => searchMedia()}>
                                Spróbuj wyszukać ponownie
                            </button>
                        </div>
                    {:else}
                        <div class="episodes-list">
                            {#each filteredEpisodes as episode}
                                <div
                                    class="episode-item"
                                    on:click={() => selectEpisode(episode)}
                                    on:keydown={(e) => e.key === 'Enter' && selectEpisode(episode)}
                                    tabindex="0"
                                    role="button"
                                >
                                    <div class="episode-info">
                                        <div class="episode-title">
                                            S{episode.episodeInfo.season}E{episode.episodeInfo.episode} - {episode.title}
                                        </div>
                                        {#if episode.year}
                                            <div class="episode-year">{episode.year}</div>
                                        {/if}
                                    </div>
                                    <div class="episode-action">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </div>
                            {/each}
                        </div>
                    {/if}
                {:else if selectedTVShow && selectedEpisode && viewMode === 'releases'}
                    <!-- TV Show Releases View -->
                    <div class="navigation-header">
                        <button class="back-button" on:click={backToEpisodes}>
                            <span>← Powrót do odcinków</span>
                        </button>
                        <h3 class="tv-show-title">
                            {selectedTVShow.title} - S{selectedEpisode.episodeInfo.season}E{selectedEpisode.episodeInfo.episode}
                        </h3>
                    </div>

                    {#if isSearching}
                        <div class="loading-container">
                            <div class="loading"></div>
                            <p>Pobieranie wydań...</p>
                        </div>
                    {:else if episodeReleases.length === 0}
                        <div class="empty-state">
                            <p>Nie znaleziono wydań dla tego odcinka</p>
                        </div>
                    {:else}
                        <div class="releases-count">
                            <p>Znaleziono {episodeReleases.length} wydań</p>
                        </div>
                        <div class="sources-list">
                            {#each episodeReleases as source}
                                <div
                                    class="source-item"
                                    tabindex="0"
                                    role="button"
                                    aria-label="Play {source.title}"
                                    on:keydown={(e) => e.key === 'Enter' && handlePlayInBrowser(source)}
                                    on:click={() => handlePlayInBrowser(source)}
                                >
                                    <div class="source-info">
                                        <div class="source-title">{source.title}</div>
                                        <div class="source-meta">
                                            {#if source.quality}
                                                {@const formattedQuality = getFormattedQuality(source.quality)}
                                                <span class="source-quality {formattedQuality.class}">{formattedQuality.text}</span>
                                            {/if}
                                            {#if source.size}
                                                <span class="source-size">{source.size}</span>
                                            {/if}
                                        </div>
                                        {#if source.filename}
                                            <div class="source-filename" title={source.filename}>
                                                {source.filename}
                                            </div>
                                        {:else if source.originalTitle}
                                            <div class="source-filename" title={source.originalTitle}>
                                                {source.originalTitle}
                                            </div>
                                        {/if}
                                    </div>
                                    <div class="source-action">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                        </svg>
                                    </div>
                                </div>
                            {/each}
                        </div>
                    {/if}
                {:else}
                    <!-- TV Shows Main View -->
                    <div class="section-title">
                        <h1>Seriale</h1>
                    </div>
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <input
                                type="text"
                                id="tvShowsSearchQuery"
                                bind:value={searchQuery}
                                on:input={handleSearchInput}
                                on:keydown={handleKeyPress}
                                placeholder="Wpisz tytuł serialu..."
                                class="search-input"
                            />

                            {#if isSearching}
                                <div class="search-loading-indicator">
                                    <Loader2 size={20} class="animate-spin" />
                                </div>
                            {:else if searchQuery.trim().length > 0}
                                <button class="search-clear-button" on:click={() => { searchQuery = ''; searchResults = []; }}>
                                    &times;
                                </button>
                            {/if}
                        </div>
                    </div>

                    <!-- Search results -->
                    {#if isSearching}
                        <div class="empty-state">
                            <div class="loading" style="width: 24px; height: 24px; margin: 0 auto;"></div>
                            <p style="margin-top: 10px;">Wyszukiwanie...</p>
                        </div>
                    {:else if searchError}
                        <div class="empty-state">
                            <p>Wystąpił błąd podczas wyszukiwania.<br/>{searchError}</p>
                            <button class="btn btn-outline" style="margin-top: 10px;" on:click={searchMedia}>Spróbuj ponownie</button>
                        </div>
                    {:else if searchResults.length > 0}
                        <div class="tv-shows-grid">
                            {#each searchResults.filter(result => result.type === 'tvshow' && !result.episodeInfo) as tvShow}
                                <div
                                    class="tv-show-card"
                                    tabindex="0"
                                    role="button"
                                    aria-label={tvShow.title}
                                    on:click={() => selectTVShow(tvShow)}
                                    on:keydown={(e) => e.key === 'Enter' && selectTVShow(tvShow)}
                                >
                                    <div class="tv-show-poster">
                                        {#if tvShow.poster}
                                            <img src={tvShow.poster} alt="{tvShow.title} poster" class="tv-show-poster-img" />
                                        {:else}
                                            <div class="tv-show-poster-placeholder">
                                                <Tv size={32} />
                                            </div>
                                        {/if}
                                    </div>
                                    <div class="tv-show-info">
                                        <div class="tv-show-title">{tvShow.title}</div>
                                        {#if tvShow.year}
                                            <div class="tv-show-year">{tvShow.year}</div>
                                        {/if}
                                    </div>
                                </div>
                            {/each}
                        </div>
                    {:else if searchQuery.trim().length >= 3}
                        <div class="empty-state">
                            <p>Nie znaleziono wyników dla "{searchQuery}"</p>
                        </div>
                    {:else}
                        <div class="empty-state">
                            <p>Wpisz co najmniej 3 znaki, aby rozpocząć wyszukiwanie seriali</p>
                        </div>
                    {/if}
                {/if}
            {:else if activeSection === 'searchMovies' || activeSection === 'searchTVShows'}
                <div class="section-title">
                    <h1>{activeSection === 'searchMovies' ? 'Szukaj filmów' : 'Szukaj seriali'}</h1>
                </div>

                <!-- Search input -->
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <input
                            type="text"
                            id="tvSearchQuery"
                            bind:value={searchQuery}
                            on:input={handleSearchInput}
                            on:keydown={handleKeyPress}
                            placeholder={activeSection === 'searchMovies' ? 'Wpisz tytuł filmu...' : 'Wpisz tytuł serialu...'}
                            class="search-input"
                        />

                        {#if isSearching}
                            <div class="search-loading-indicator">
                                <Loader2 size={20} class="animate-spin" />
                            </div>
                        {:else if searchQuery.trim().length > 0}
                            <button class="search-clear-button" on:click={() => { searchQuery = ''; searchResults = []; }}>
                                &times;
                            </button>
                        {/if}
                    </div>
                </div>

                <!-- Search results -->
                {#if isSearching}
                    <div class="empty-state">
                        <div class="loading" style="width: 24px; height: 24px; margin: 0 auto;"></div>
                        <p style="margin-top: 10px;">Wyszukiwanie...</p>
                    </div>
                {:else if searchError}
                    <div class="empty-state">
                        <p>Wystąpił błąd podczas wyszukiwania.<br/>{searchError}</p>
                        <button class="btn btn-outline" style="margin-top: 10px;" on:click={searchMedia}>Spróbuj ponownie</button>
                    </div>
                {:else if searchResults.length > 0}
                    <div class="movie-grid search-results-grid">
                        {#each searchResults as result}
                            <div
                                class="movie-card"
                                tabindex="0"
                                role="button"
                                aria-label={result.title}
                                on:click={() => openMovieDetails(result)}
                                on:keydown={(e) => e.key === 'Enter' && openMovieDetails(result)}
                            >
                                <div class="movie-poster">
                                    {#if result.poster}
                                        <img src={result.poster} alt="{result.title} poster" class="movie-poster-img" />
                                    {:else}
                                        <div class="movie-poster-placeholder">
                                            {#if result.type === 'movie'}
                                                <Film size={32} />
                                            {:else}
                                                <Tv size={32} />
                                            {/if}
                                        </div>
                                    {/if}
                                </div>
                                <div class="movie-info">
                                    <div class="movie-title">{result.title}</div>
                                    <div class="movie-year">{result.year}</div>
                                    {#if result.type === 'tvshow' && result.episodeInfo}
                                        <div class="episode-info">S{result.episodeInfo.season}E{result.episodeInfo.episode}</div>
                                    {/if}
                                </div>
                            </div>
                        {/each}
                    </div>
                {:else if searchQuery.trim().length >= 3}
                    <div class="empty-state">
                        <p>Nie znaleziono wyników dla "{searchQuery}"</p>
                    </div>
                {:else}
                    <div class="empty-state">
                        <p>Wpisz co najmniej 3 znaki, aby rozpocząć wyszukiwanie</p>
                    </div>
                {/if}
            {/if} <!-- This closes the inner #if for search results -->
        {:else} <!-- This is the outer else for selectedMovie -->
            <!-- Grid/Search Views -->
            {#if activeSection === 'movies'}
                <div class="section-title">
                    <h1>Filmy</h1>
                </div>

                {#if isLoading}
                    <div class="loading-container">
                        <div class="loading"></div>
                        <p>Loading trending movies...</p>
                    </div>
                {:else if error}
                    <div class="error-container">
                        <p>{error}</p>
                        <button class="btn" on:click={loadTrendingMovies}>Try Again</button>
                    </div>
                {:else if trendingMovies.length === 0}
                    <div class="empty-container">
                        <p>No movies found</p>
                    </div>
                {:else}
                    <div class="movie-grid">
                        {#each trendingMovies as movie, index}
                            <div
                                class="movie-card {focusedItemIndex === index ? 'focused' : ''}"
                                data-index={index}
                                tabindex="0"
                                role="button"
                                aria-label={movie.title}
                                on:focus={() => {
                                    focusedItemIndex = index;
                                    applyFocusEffects();
                                }}
                                on:mouseenter={() => {
                                    focusedItemIndex = index;
                                    applyFocusEffects();
                                }}
                                on:click={() => openMovieDetails(movie)}
                                on:keydown={(e) => e.key === 'Enter' && openMovieDetails(movie)}
                            >
                                <div class="movie-poster">
                                    {#if movie.poster}
                                        <img src={movie.poster} alt="{movie.title} poster" class="movie-poster-img" />
                                    {:else}
                                        <div class="movie-poster-placeholder">
                                            <Film size={32} />
                                        </div>
                                    {/if}
                                </div>
                                <div class="movie-info">
                                    <div class="movie-title">{movie.title}</div>
                                    <div class="movie-year">{movie.year}</div>
                                </div>
                            </div>
                        {/each}
                    </div>
                {/if}
            {:else if activeSection === 'shows'}
                {#if selectedTVShow && viewMode === 'episodes'}
                    <!-- TV Show Episodes View -->
                    <div class="navigation-header">
                        <button class="back-button" on:click={resetTVShowNavigation}>
                            <span>← Powrót do wyszukiwania</span>
                        </button>
                        <h3 class="tv-show-title">{selectedTVShow.title} {selectedTVShow.year ? `(${selectedTVShow.year})` : ''}</h3>
                    </div>

                    <!-- Season filter -->
                    {#if availableSeasons.length > 1}
                        <div class="season-filter">
                            <button
                                class="season-btn {selectedSeason === null ? 'active' : ''}"
                                on:click={() => filterBySeason(null)}
                            >
                                Wszystkie sezony
                            </button>

                            {#each availableSeasons as season}
                                <button
                                    class="season-btn {selectedSeason === season ? 'active' : ''}"
                                    on:click={() => filterBySeason(season)}
                                >
                                    Sezon {season}
                                </button>
                            {/each}
                        </div>
                    {/if}

                    <!-- Episodes list -->
                    {@const filteredEpisodes = searchResults.filter(result =>
                        result.type === 'tvshow' &&
                        result.imdbID === selectedTVShow.imdbID &&
                        result.episodeInfo !== null &&
                        (selectedSeason === null || result.episodeInfo.season === selectedSeason)
                    )}

                    {#if filteredEpisodes.length === 0}
                        <div class="empty-state">
                            <p>Nie znaleziono odcinków dla tego serialu{selectedSeason !== null ? ` (Sezon ${selectedSeason})` : ''}</p>
                            <button class="btn btn-outline" style="margin-top: 10px;" on:click={() => searchMedia()}>
                                Spróbuj wyszukać ponownie
                            </button>
                        </div>
                    {:else}
                        <div class="episodes-list">
                            {#each filteredEpisodes as episode}
                                <div
                                    class="episode-item"
                                    on:click={() => selectEpisode(episode)}
                                    on:keydown={(e) => e.key === 'Enter' && selectEpisode(episode)}
                                    tabindex="0"
                                    role="button"
                                >
                                    <div class="episode-info">
                                        <div class="episode-title">
                                            S{episode.episodeInfo.season}E{episode.episodeInfo.episode} - {episode.title}
                                        </div>
                                        {#if episode.year}
                                            <div class="episode-year">{episode.year}</div>
                                        {/if}
                                    </div>
                                    <div class="episode-action">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 18l6-6-6-6"/>
                                        </svg>
                                    </div>
                                </div>
                            {/each}
                        </div>
                    {/if}
                {:else if selectedTVShow && selectedEpisode && viewMode === 'releases'}
                    <!-- TV Show Releases View -->
                    <div class="navigation-header">
                        <button class="back-button" on:click={backToEpisodes}>
                            <span>← Powrót do odcinków</span>
                        </button>
                        <h3 class="tv-show-title">
                            {selectedTVShow.title} - S{selectedEpisode.episodeInfo.season}E{selectedEpisode.episodeInfo.episode}
                        </h3>
                    </div>

                    {#if isSearching}
                        <div class="loading-container">
                            <div class="loading"></div>
                            <p>Pobieranie wydań...</p>
                        </div>
                    {:else if episodeReleases.length === 0}
                        <div class="empty-state">
                            <p>Nie znaleziono wydań dla tego odcinka</p>
                        </div>
                    {:else}
                        <div class="releases-count">
                            <p>Znaleziono {episodeReleases.length} wydań</p>
                        </div>
                        <div class="sources-list">
                            {#each episodeReleases as source}
                                <div
                                    class="source-item"
                                    tabindex="0"
                                    role="button"
                                    aria-label="Play {source.title}"
                                    on:keydown={(e) => e.key === 'Enter' && handlePlayInBrowser(source)}
                                    on:click={() => handlePlayInBrowser(source)}
                                >
                                    <div class="source-info">
                                        <div class="source-title">{source.title}</div>
                                        <div class="source-meta">
                                            {#if source.quality}
                                                {@const formattedQuality = getFormattedQuality(source.quality)}
                                                <span class="source-quality {formattedQuality.class}">{formattedQuality.text}</span>
                                            {/if}
                                            {#if source.size}
                                                <span class="source-size">{source.size}</span>
                                            {/if}
                                        </div>
                                        {#if source.filename}
                                            <div class="source-filename" title={source.filename}>
                                                {source.filename}
                                            </div>
                                        {:else if source.originalTitle}
                                            <div class="source-filename" title={source.originalTitle}>
                                                {source.originalTitle}
                                            </div>
                                        {/if}
                                    </div>
                                    <div class="source-action">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                                        </svg>
                                    </div>
                                </div>
                            {/each}
                        </div>
                    {/if}
                {:else}
                    <!-- TV Shows Main View -->
                    <div class="section-title">
                        <h1>Seriale</h1>
                    </div>
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <input
                                type="text"
                                id="tvShowsSearchQuery"
                                bind:value={searchQuery}
                                on:input={handleSearchInput}
                                on:keydown={handleKeyPress}
                                placeholder="Wpisz tytuł serialu..."
                                class="search-input"
                            />

                            {#if isSearching}
                                <div class="search-loading-indicator">
                                    <Loader2 size={20} class="animate-spin" />
                                </div>
                            {:else if searchQuery.trim().length > 0}
                                <button class="search-clear-button" on:click={() => { searchQuery = ''; searchResults = []; }}>
                                    &times;
                                </button>
                            {/if}
                        </div>
                    </div>

                    <!-- Search results -->
                    {#if isSearching}
                        <div class="empty-state">
                            <div class="loading" style="width: 24px; height: 24px; margin: 0 auto;"></div>
                            <p style="margin-top: 10px;">Wyszukiwanie...</p>
                        </div>
                    {:else if searchError}
                        <div class="empty-state">
                            <p>Wystąpił błąd podczas wyszukiwania.<br/>{searchError}</p>
                            <button class="btn btn-outline" style="margin-top: 10px;" on:click={searchMedia}>Spróbuj ponownie</button>
                        </div>
                    {:else if searchResults.length > 0}
                        <div class="tv-shows-grid">
                            {#each searchResults.filter(result => result.type === 'tvshow' && !result.episodeInfo) as tvShow}
                                <div
                                    class="tv-show-card"
                                    tabindex="0"
                                    role="button"
                                    aria-label={tvShow.title}
                                    on:click={() => selectTVShow(tvShow)}
                                    on:keydown={(e) => e.key === 'Enter' && selectTVShow(tvShow)}
                                >
                                    <div class="tv-show-poster">
                                        {#if tvShow.poster}
                                            <img src={tvShow.poster} alt="{tvShow.title} poster" class="tv-show-poster-img" />
                                        {:else}
                                            <div class="tv-show-poster-placeholder">
                                                <Tv size={32} />
                                            </div>
                                        {/if}
                                    </div>
                                    <div class="tv-show-info">
                                        <div class="tv-show-title">{tvShow.title}</div>
                                        {#if tvShow.year}
                                            <div class="tv-show-year">{tvShow.year}</div>
                                        {/if}
                                    </div>
                                </div>
                            {/each}
                        </div>
                    {:else if searchQuery.trim().length >= 3}
                        <div class="empty-state">
                            <p>Nie znaleziono wyników dla "{searchQuery}"</p>
                        </div>
                    {:else}
                        <div class="empty-state">
                            <p>Wpisz co najmniej 3 znaki, aby rozpocząć wyszukiwanie seriali</p>
                        </div>
                    {/if}
                {/if}
            {:else if activeSection === 'searchMovies' || activeSection === 'searchTVShows'}
                <div class="section-title">
                    <h1>{activeSection === 'searchMovies' ? 'Szukaj filmów' : 'Szukaj seriali'}</h1>
                </div>

                <!-- Search input -->
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <input
                            type="text"
                            id="tvSearchQuery"
                            bind:value={searchQuery}
                            on:input={handleSearchInput}
                            on:keydown={handleKeyPress}
                            placeholder={activeSection === 'searchMovies' ? 'Wpisz tytuł filmu...' : 'Wpisz tytuł serialu...'}
                            class="search-input"
                        />

                        {#if isSearching}
                            <div class="search-loading-indicator">
                                <Loader2 size={20} class="animate-spin" />
                            </div>
                        {:else if searchQuery.trim().length > 0}
                            <button class="search-clear-button" on:click={() => { searchQuery = ''; searchResults = []; }}>
                                &times;
                            </button>
                        {/if}
                    </div>
                </div>

                <!-- Search results -->
                {#if isSearching}
                    <div class="empty-state">
                        <div class="loading" style="width: 24px; height: 24px; margin: 0 auto;"></div>
                        <p style="margin-top: 10px;">Wyszukiwanie...</p>
                    </div>
                {:else if searchError}
                    <div class="empty-state">
                        <p>Wystąpił błąd podczas wyszukiwania.<br/>{searchError}</p>
                        <button class="btn btn-outline" style="margin-top: 10px;" on:click={searchMedia}>Spróbuj ponownie</button>
                    </div>
                {:else if searchResults.length > 0}
                    <div class="movie-grid search-results-grid">
                        {#each searchResults as result}
                            <div
                                class="movie-card"
                                tabindex="0"
                                role="button"
                                aria-label={result.title}
                                on:click={() => openMovieDetails(result)}
                                on:keydown={(e) => e.key === 'Enter' && openMovieDetails(result)}
                            >
                                <div class="movie-poster">
                                    {#if result.poster}
                                        <img src={result.poster} alt="{result.title} poster" class="movie-poster-img" />
                                    {:else}
                                        <div class="movie-poster-placeholder">
                                            {#if result.type === 'movie'}
                                                <Film size={32} />
                                            {:else}
                                                <Tv size={32} />
                                            {/if}
                                        </div>
                                    {/if}
                                </div>
                                <div class="movie-info">
                                    <div class="movie-title">{result.title}</div>
                                    <div class="movie-year">{result.year}</div>
                                    {#if result.type === 'tvshow' && result.episodeInfo}
                                        <div class="episode-info">S{result.episodeInfo.season}E{result.episodeInfo.episode}</div>
                                    {/if}
                                </div>
                            </div>
                        {/each}
                    </div>
                {:else if searchQuery.trim().length >= 3}
                    <div class="empty-state">
                        <p>Nie znaleziono wyników dla "{searchQuery}"</p>
                    </div>
                {:else}
                    <div class="empty-state">
                        <p>Wpisz co najmniej 3 znaki, aby rozpocząć wyszukiwanie</p>
                    </div>
                {/if}
            {/if}
        {/if}
        </div> <!-- /tv-content-inner -->
    </main>

     <!-- Loading Indicator -->
    {#if isLoadingVideo}
        <div class="loading-overlay">
            <div class="loading-spinner">
                <div class="spinner-container">
                    <Loader2 size={40} class="animate-spin" />
                </div>
                <p>Przygotowywanie odtwarzacza...</p>
            </div>
        </div>
    {/if}

    <!-- Video Player Modal -->
    {#if showVideoPlayer && videoUrl}
        <div
            class="video-modal-overlay"
            on:keydown={(e) => e.key === 'Escape' && closeVideoPlayer()}
            role="dialog"
            aria-modal="true"
            aria-labelledby="video-modal-title"
            tabindex="-1"
        >
            <div
                class="video-modal"
                role="document"
            >
                <div class="video-modal-header">
                    <h3 id="video-modal-title">
                        {selectedMovie ? selectedMovie.title : 'Odtwarzacz wideo'}
                    </h3>
                    <button class="close-btn" on:click={closeVideoPlayer} aria-label="Zamknij">×</button>
                </div>
                <div class="video-container">
                    <video controls autoplay class="video-player" id="videoPlayer">
                        <source src={videoUrl} type="video/mp4">
                        <track kind="captions" src={subtitlesUrl || ''} label="Polski" srclang="pl" default>
                        Twoja przeglądarka nie obsługuje odtwarzania wideo.
                    </video>

                    {#if subtitlesUrl}
                    <div class="video-controls">
                        <div class="subtitle-controls">
                            <button on:click={() => toggleSubtitles(true)} class="control-button">
                                <Subtitles size={16} /> Włącz napisy
                            </button>
                            <button on:click={() => toggleSubtitles(false)} class="control-button">
                                <Subtitles size={16} /> Wyłącz napisy
                            </button>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    {/if}
</div>

<style>
    .tv-container {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        background-color: var(--bg-color);
        padding: 1rem;
    }

    /* Search icon styles */
    .search-icon-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .search-sub-icon {
        position: absolute;
        bottom: -5px;
        right: -5px;
        background-color: var(--primary);
        border-radius: 50%;
        padding: 2px;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    }

    /* Search input styles */
    .search-container {
        margin-bottom: 2rem;
    }

    .search-input-wrapper {
        display: flex;
        position: relative;
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
    }

    .search-input {
        flex: 1;
        padding: 0.75rem 1rem;
        padding-right: 3rem;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        background-color: rgba(0, 0, 0, 0.2);
        color: var(--text);
        font-size: 1rem;
        transition: all 0.2s ease;
        width: 100%;
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(244, 172, 203, 0.3);
    }

    .search-loading-indicator {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
    }

    .search-clear-button {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        line-height: 1;
    }

    .search-clear-button:hover {
        color: var(--text);
    }

    .search-results-grid {
        margin-top: 1.5rem;
    }

    .episode-info {
        font-size: 0.75rem;
        background-color: var(--primary);
        color: white;
        padding: 0.1rem 0.4rem;
        border-radius: 0.25rem;
        display: inline-block;
        margin-top: 0.25rem;
    }

    .tv-nav {
        display: flex;
        justify-content: center;
        gap: 2rem;
        padding: 1rem 0;
        margin-bottom: 2rem;
    }

    .tv-nav-button {
        background: none;
        border: none;
        color: var(--text-secondary);
        padding: 0.5rem;
        border-radius: 50%;
        width: 64px;
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .tv-nav-button:hover {
        color: var(--text);
        background-color: rgba(255, 255, 255, 0.1);
    }

    .tv-nav-button.active {
        color: var(--primary);
        background-color: rgba(244, 172, 203, 0.1);
    }

    .tv-content {
        flex: 1;
    }

    .section-title {
        margin-bottom: 2rem;
    }

    .section-title h1 {
        font-size: 2rem;
        font-weight: bold;
        color: var(--text);
    }

    .movie-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    @media (min-width: 768px) {
        .movie-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (min-width: 1280px) {
        .movie-grid {
            grid-template-columns: repeat(6, 1fr);
        }
    }

    .movie-card {
        position: relative;
        border-radius: 0.75rem;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
        border: 3px solid transparent;
        cursor: pointer;
        aspect-ratio: 2/3;
        transform-origin: center center;
    }

    .movie-card:hover, .movie-card:focus {
        border-color: var(--primary);
        box-shadow: 0 0 20px rgba(244, 172, 203, 0.3);
        outline: none;
        transform: scale(1.05);
    }

    .movie-card.focused {
        border-color: var(--primary);
        box-shadow: 0 0 30px rgba(244, 172, 203, 0.5);
        outline: none;
        transform: scale(1.1);
        z-index: 10;
    }

    .movie-poster {
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .movie-poster-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .movie-poster-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-secondary);
    }

    .movie-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1rem;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        color: white;
    }

    .movie-title {
        font-weight: bold;
        line-height: 1;
        margin-bottom: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .movie-year {
        font-size: 1rem;
        font-weight: bold;
        opacity: 0.66;
        margin-top: 0;
    }

    .loading-container, .error-container, .empty-container, .placeholder-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        text-align: center;
        color: var(--text-secondary);
    }

    .loading {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top-color: var(--primary);
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .btn {
        background-color: var(--primary);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        cursor: pointer;
        font-weight: bold;
        margin-top: 1rem;
    }

    .btn:hover {
        background-color: var(--primary-dark, #d6409f);
    }

    /* Movie details styles */
    .movie-details-container {
        position: relative;
        min-height: 100vh;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        border-radius: 10px;
    }

    .movie-details-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
        z-index: 1;
    }

    .movie-details-header {
        position: relative;
        z-index: 2;
        padding: 1rem;
    }

    .back-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(0, 0, 0, 0.5);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .back-button:hover {
        background: rgba(0, 0, 0, 0.7);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .movie-details-content {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        color: white;
    }

    @media (min-width: 768px) {
        .movie-details-content {
            flex-direction: row;
            gap: 2rem;
        }
    }

    .movie-details-poster {
        flex-shrink: 0;
        width: 100%;
        max-width: 300px;
        margin-bottom: 2rem;
        border-radius: 1rem;
        overflow: hidden;
    }

    .movie-details-poster-img {
        width: 100%;
        height: auto;
        object-fit: contain;
        border-radius: 1rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .movie-poster-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    @media (min-width: 768px) {
        .movie-details-poster {
            margin-bottom: 0;
        }
    }

    .movie-details-info {
        flex: 1;
    }

    .movie-logo {
        max-width: 100%;
        max-height: 100px;
        margin-bottom: 1rem;
    }

    .movie-title {
        line-height: 1;
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0;
    }

    .movie-meta {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
        color: rgba(255, 255, 255, 0.8);
    }

    .movie-overview {
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .movie-genres {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 2rem;
    }

    .genre-tag {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
    }

    .sources-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .sources-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .source-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }

    .source-item:hover {
        background: rgba(0, 0, 0, 0.5);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .source-info {
        flex: 1;
    }

    .source-title {
        font-weight: bold;
        margin-bottom: 0.25rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .source-meta {
        display: flex;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.7);
    }

    .source-quality {
        padding: 0.15rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: bold;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Quality badge styles */
    .quality-4k-hdr-dv {
        background-color: #9c27b0;
        color: white;
    }

    .quality-4k-hdr {
        background-color: #673ab7;
        color: white;
    }

    .quality-4k-dv {
        background-color: #7b1fa2;
        color: white;
    }

    /* Video player styles */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .loading-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: white;
        animation: none; /* Explicitly disable animation */
    }

    .spinner-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 1rem;
    }

    .animate-spin {
        animation: spin 1s linear infinite;
        display: inline-block;
    }

    .video-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .video-modal {
        width: 90%;
        max-width: 1200px;
        background-color: #111;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    }

    .video-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background-color: rgba(0, 0, 0, 0.3);
    }

    .video-modal-header h3 {
        margin: 0;
        color: white;
        font-size: 1.25rem;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-secondary);
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .close-btn:hover {
        background-color: var(--item-bg);
        color: var(--text);
    }

    .video-container {
        width: 100%;
        padding-top: 56.25%; /* 16:9 aspect ratio */
        position: relative;
    }

    .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: black;
    }

    .video-controls {
        position: absolute;
        bottom: 10px;
        left: 0;
        width: 100%;
        padding: 0 20px;
        z-index: 10;
        display: flex;
        justify-content: center;
        pointer-events: none;
    }

    .subtitle-controls {
        display: flex;
        gap: 10px;
        pointer-events: auto;
    }

    .control-button {
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 0.5rem 1rem;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .control-button:hover {
        background-color: rgba(0, 0, 0, 0.9);
        border-color: rgba(255, 255, 255, 0.5);
    }

    .quality-4k {
        background-color: #3f51b5;
        color: white;
    }

    .quality-1080p-hdr-dv {
        background-color: #e91e63;
        color: white;
    }

    .quality-1080p-hdr {
        background-color: #f44336;
        color: white;
    }

    .quality-1080p-dv {
        background-color: #d81b60;
        color: white;
    }

    .quality-1080p {
        background-color: #2196f3;
        color: white;
    }

    .quality-720p {
        background-color: #00bcd4;
        color: white;
    }

    .quality-480p {
        background-color: #4caf50;
        color: white;
    }

    .quality-dv {
        background-color: #9c27b0;
        color: white;
    }

    .quality-hdr {
        background-color: #ff9800;
        color: white;
    }

    .quality-unknown {
        background-color: #607d8b;
        color: white;
    }

    .source-size {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 0.15rem 0.5rem;
        border-radius: 0.25rem;
    }

    .source-filename {
        font-size: 11px;
        color: var(--text-secondary);
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        line-height: 1.4;
        max-height: 4.2em; /* 3 lines */
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
    }

    .source-action {
        flex-shrink: 0;
        color: var(--primary);
    }

    /* TV Show styles */
    .navigation-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 10px 0;
    }

    .back-button {
        display: flex;
        align-items: center;
        background: none;
        border: none;
        color: var(--text-primary);
        font-size: 16px;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 4px;
        transition: background-color 0.2s;
    }

    .back-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .tv-show-title {
        margin-left: 10px;
        font-size: 20px;
        font-weight: 600;
    }

    .season-filter {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 20px;
    }

    .season-btn {
        background-color: rgba(255, 255, 255, 0.1);
        border: none;
        color: var(--text-primary);
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .season-btn.active {
        background-color: var(--primary);
        color: #000;
    }

    .episodes-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-top: 20px;
    }

    .episode-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .episode-item:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .episode-info {
        flex: 1;
    }

    .episode-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 4px;
    }

    .episode-year {
        font-size: 14px;
        color: var(--text-secondary);
    }

    .episode-action {
        color: var(--primary);
    }

    .releases-count {
        margin-bottom: 16px;
        color: var(--text-secondary);
    }

    .tv-shows-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .tv-show-card {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        overflow: hidden;
        transition: transform 0.2s, box-shadow 0.2s;
        cursor: pointer;
    }

    .tv-show-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    .tv-show-poster {
        position: relative;
        width: 100%;
        padding-top: 150%; /* 2:3 aspect ratio */
        overflow: hidden;
    }

    .tv-show-poster-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .tv-show-poster-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.05);
        color: var(--text-secondary);
    }

    .tv-show-info {
        padding: 12px;
    }

    .tv-show-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .tv-show-year {
        font-size: 12px;
        color: var(--text-secondary);
    }
</style>
