<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    import { Upload } from 'lucide-svelte';
    
    /**
     * Accepted file types
     */
    export let accept: string = '*';
    
    /**
     * Maximum file size in bytes
     */
    export let maxSize: number = 10 * 1024 * 1024; // 10MB
    
    /**
     * Whether to allow multiple files
     */
    export let multiple: boolean = false;
    
    /**
     * Custom message to display
     */
    export let message: string = 'Drag and drop files here, or click to select files';
    
    /**
     * Whether the dropzone is currently active (drag over)
     */
    export let active: boolean = false;
    
    /**
     * Whether the dropzone is disabled
     */
    export let disabled: boolean = false;
    
    /**
     * Whether the dropzone is currently loading
     */
    export let loading: boolean = false;
    
    // Internal state
    let fileInput: HTMLInputElement;
    let dragCounter = 0;
    
    // Event dispatcher
    const dispatch = createEventDispatcher<{
        drop: { files: File[] };
        error: { message: string };
    }>();
    
    /**
     * Handle drag enter event
     */
    function handleDragEnter(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
        
        if (disabled || loading) return;
        
        dragCounter++;
        active = true;
    }
    
    /**
     * Handle drag leave event
     */
    function handleDragLeave(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
        
        if (disabled || loading) return;
        
        dragCounter--;
        if (dragCounter === 0) {
            active = false;
        }
    }
    
    /**
     * Handle drag over event
     */
    function handleDragOver(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
        
        if (disabled || loading) return;
        
        active = true;
    }
    
    /**
     * Handle drop event
     */
    function handleDrop(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
        
        if (disabled || loading) return;
        
        active = false;
        dragCounter = 0;
        
        const files = event.dataTransfer?.files;
        if (files) {
            handleFiles(files);
        }
    }
    
    /**
     * Handle file input change
     */
    function handleFileInputChange(event: Event) {
        const input = event.target as HTMLInputElement;
        const files = input.files;
        
        if (files) {
            handleFiles(files);
        }
        
        // Reset input value to allow selecting the same file again
        input.value = '';
    }
    
    /**
     * Handle files
     */
    function handleFiles(fileList: FileList) {
        const files = Array.from(fileList);
        
        // Check file types
        if (accept !== '*') {
            const acceptedTypes = accept.split(',').map(type => type.trim());
            const invalidFiles = files.filter(file => {
                return !acceptedTypes.some(type => {
                    if (type.startsWith('.')) {
                        // Check file extension
                        return file.name.toLowerCase().endsWith(type.toLowerCase());
                    } else if (type.includes('*')) {
                        // Check MIME type pattern (e.g., "image/*")
                        const [category] = type.split('/');
                        return file.type.startsWith(`${category}/`);
                    } else {
                        // Check exact MIME type
                        return file.type === type;
                    }
                });
            });
            
            if (invalidFiles.length > 0) {
                dispatch('error', { 
                    message: `Invalid file type. Accepted types: ${accept}` 
                });
                return;
            }
        }
        
        // Check file sizes
        const oversizedFiles = files.filter(file => file.size > maxSize);
        if (oversizedFiles.length > 0) {
            dispatch('error', { 
                message: `File too large. Maximum size: ${formatFileSize(maxSize)}` 
            });
            return;
        }
        
        // If multiple is false, only take the first file
        const selectedFiles = multiple ? files : files.slice(0, 1);
        
        // Dispatch drop event with files
        dispatch('drop', { files: selectedFiles });
    }
    
    /**
     * Open file dialog
     */
    function openFileDialog() {
        if (disabled || loading) return;
        fileInput.click();
    }
    
    /**
     * Format file size to human-readable string
     */
    function formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
</script>

<div 
    class="dropzone {active ? 'active' : ''} {disabled ? 'disabled' : ''} {loading ? 'loading' : ''}"
    on:dragenter={handleDragEnter}
    on:dragleave={handleDragLeave}
    on:dragover={handleDragOver}
    on:drop={handleDrop}
    on:click={openFileDialog}
    role="button"
    tabindex="0"
    aria-label="File upload area"
>
    <input
        bind:this={fileInput}
        type="file"
        {accept}
        {multiple}
        on:change={handleFileInputChange}
        class="file-input"
        tabindex="-1"
        aria-hidden="true"
    />
    
    <div class="dropzone-content">
        {#if loading}
            <div class="dropzone-spinner"></div>
            <p class="dropzone-message">Uploading...</p>
        {:else}
            <div class="dropzone-icon">
                <Upload size={32} />
            </div>
            <p class="dropzone-message">{message}</p>
            <p class="dropzone-hint">
                {multiple ? 'Maximum size per file' : 'Maximum size'}: {formatFileSize(maxSize)}
            </p>
        {/if}
    </div>
    
    <slot />
</div>

<style>
    .dropzone {
        position: relative;
        width: 100%;
        min-height: 10rem;
        border: 2px dashed rgba(255, 255, 255, 0.2);
        border-radius: 0.5rem;
        background-color: rgba(255, 255, 255, 0.05);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.2s ease;
        outline: none;
    }
    
    .dropzone:hover:not(.disabled):not(.loading) {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .dropzone:focus:not(.disabled):not(.loading) {
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(255, 105, 180, 0.2);
    }
    
    .dropzone.active {
        background-color: rgba(255, 105, 180, 0.1);
        border-color: var(--primary);
    }
    
    .dropzone.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    .dropzone.loading {
        cursor: wait;
    }
    
    .file-input {
        position: absolute;
        width: 0;
        height: 0;
        opacity: 0;
        overflow: hidden;
    }
    
    .dropzone-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .dropzone-icon {
        color: var(--text-secondary);
        margin-bottom: 1rem;
    }
    
    .dropzone-message {
        font-size: 1rem;
        color: var(--text);
        margin: 0 0 0.5rem 0;
    }
    
    .dropzone-hint {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin: 0;
    }
    
    .dropzone-spinner {
        width: 2rem;
        height: 2rem;
        border: 3px solid rgba(255, 255, 255, 0.1);
        border-top-color: var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }
    
    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>
