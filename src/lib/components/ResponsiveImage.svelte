<script lang="ts">
    import { onMount, createEventDispatcher } from 'svelte';
    import { Image } from 'lucide-svelte';
    
    /**
     * Image source URL
     */
    export let src: string | null = null;
    
    /**
     * Alternative text for the image
     */
    export let alt: string = '';
    
    /**
     * CSS class for the image
     */
    export let className: string = '';
    
    /**
     * Whether to lazy load the image
     */
    export let lazy: boolean = true;
    
    /**
     * Whether to show a placeholder while loading
     */
    export let showPlaceholder: boolean = true;
    
    /**
     * Placeholder text to show when no image is available
     */
    export let placeholderText: string = 'No Image';
    
    /**
     * Whether to blur the image while loading
     */
    export let blurEffect: boolean = true;
    
    /**
     * Whether to fade in the image when loaded
     */
    export let fadeIn: boolean = true;
    
    /**
     * Whether to use a skeleton loader while loading
     */
    export let skeleton: boolean = false;
    
    /**
     * Aspect ratio of the image (width/height)
     */
    export let aspectRatio: number | null = null;
    
    /**
     * Whether to use object-fit: cover
     */
    export let cover: boolean = true;
    
    /**
     * Whether to round the image corners
     */
    export let rounded: boolean = true;
    
    /**
     * Whether to make the image a circle
     */
    export let circle: boolean = false;
    
    /**
     * Whether to add a border to the image
     */
    export let border: boolean = false;
    
    // Internal state
    let loaded = false;
    let error = false;
    let imageElement: HTMLImageElement;
    
    // Event dispatcher
    const dispatch = createEventDispatcher<{
        load: { element: HTMLImageElement };
        error: { element: HTMLImageElement };
    }>();
    
    // Calculate aspect ratio style
    $: aspectRatioStyle = aspectRatio 
        ? `aspect-ratio: ${aspectRatio}; height: auto;` 
        : '';
    
    // Handle image load
    function handleLoad() {
        loaded = true;
        dispatch('load', { element: imageElement });
    }
    
    // Handle image error
    function handleError() {
        error = true;
        dispatch('error', { element: imageElement });
    }
    
    // Initialize intersection observer for lazy loading
    onMount(() => {
        if (!lazy || !src) return;
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    imageElement.src = src;
                    observer.unobserve(imageElement);
                }
            });
        }, {
            rootMargin: '200px 0px', // Start loading when image is 200px from viewport
            threshold: 0.01
        });
        
        observer.observe(imageElement);
        
        return () => {
            if (imageElement) {
                observer.unobserve(imageElement);
            }
        };
    });
</script>

<div 
    class="image-container {className} {rounded ? 'rounded' : ''} {circle ? 'circle' : ''} {border ? 'with-border' : ''}"
    style={aspectRatioStyle}
>
    {#if src}
        <img
            bind:this={imageElement}
            src={lazy ? '' : src}
            {alt}
            class="image {cover ? 'cover' : 'contain'} {loaded ? 'loaded' : ''} {blurEffect && !loaded ? 'blur' : ''} {fadeIn ? 'fade-in' : ''}"
            on:load={handleLoad}
            on:error={handleError}
        />
        
        {#if showPlaceholder && !loaded && !error}
            <div class="placeholder {skeleton ? 'skeleton' : ''}">
                {#if !skeleton}
                    <div class="placeholder-icon">
                        <Image size={24} />
                    </div>
                {/if}
            </div>
        {/if}
    {:else if error}
        <div class="placeholder error">
            <div class="placeholder-icon">
                <Image size={24} />
            </div>
            <div class="placeholder-text">Error</div>
        </div>
    {:else}
        <div class="placeholder">
            <div class="placeholder-icon">
                <Image size={24} />
            </div>
            <div class="placeholder-text">{placeholderText}</div>
        </div>
    {/if}
</div>

<style>
    .image-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        height: 100%;
        background-color: var(--card-bg);
    }
    
    .image-container.rounded {
        border-radius: 0.375rem;
    }
    
    .image-container.circle {
        border-radius: 50%;
    }
    
    .image-container.with-border {
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .image {
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 0.3s ease, filter 0.3s ease;
    }
    
    .image.cover {
        object-fit: cover;
    }
    
    .image.contain {
        object-fit: contain;
    }
    
    .image.loaded {
        opacity: 1;
    }
    
    .image.blur {
        filter: blur(10px);
    }
    
    .image.fade-in.loaded {
        animation: fadeIn 0.3s ease forwards;
    }
    
    .placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: var(--card-bg);
        color: var(--text-secondary);
        transition: opacity 0.3s ease;
    }
    
    .placeholder.skeleton {
        background: linear-gradient(
            90deg,
            var(--card-bg) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            var(--card-bg) 100%
        );
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
    }
    
    .placeholder.error {
        background-color: rgba(239, 68, 68, 0.1);
    }
    
    .placeholder-icon {
        opacity: 0.5;
        margin-bottom: 0.5rem;
    }
    
    .placeholder-text {
        font-size: 0.75rem;
        text-align: center;
    }
    
    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }
    
    @keyframes shimmer {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }
</style>
