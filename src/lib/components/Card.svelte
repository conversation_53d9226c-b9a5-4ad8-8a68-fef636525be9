<script lang="ts">
    /**
     * Whether to add padding to the card
     */
    export let padding: boolean = true;

    /**
     * Whether to add a hover effect to the card
     */
    export let hover: boolean = false;

    /**
     * Whether to add a border to the card
     */
    export let border: boolean = true;

    /**
     * Whether to add a shadow to the card
     */
    export let shadow: boolean = true;

    /**
     * Additional CSS classes to add to the card
     */
    export let className: string = '';
</script>

<div
    class="card {className} {padding ? 'with-padding' : ''} {hover ? 'with-hover' : ''} {border ? 'with-border' : ''} {shadow ? 'with-shadow' : ''}"
>
    <slot />
</div>

<style>
    .card {
        background-color: var(--card-bg);
        border-radius: 0.5rem;
        overflow: hidden;
        height: 100%;
    }

    /* Ensure mobile cards are always rounded */
    @media (max-width: 768px) {
        .card {
            border-radius: 0.5rem !important;
        }
    }

    .with-padding {
        padding: 1.25rem;
    }

    .with-border {
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .with-shadow {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .with-hover {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .with-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
</style>
