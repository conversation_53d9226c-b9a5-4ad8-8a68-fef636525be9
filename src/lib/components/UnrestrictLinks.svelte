<script lang="ts">
    import { unrestrictLink, extractFilename } from '$lib/services/debridService';
    import { onMount } from 'svelte';
    import DownloadSuccessModal from './DownloadSuccessModal.svelte';

    // Props
    const { initialLinks = '' } = $props();

    // Zmienne dla formularza
    let hosterLinks = $state(initialLinks || '');
    let isSubmitting = $state(false);

    // Tablica z odblokowanymi linkami
    let unrestrictedLinks = $state<Array<{
        url: string;
        status: 'pending' | 'success' | 'error';
        downloadUrl?: string;
        filename?: string;
        error?: string;
    }>>([]);

    // Zmienna do przechowywania danych modalu sukcesu pobierania
    let successModalData = $state({
        isOpen: false,
        coverUrl: '',
        title: ''
    });

    // Aktualizuj hosterLinks, gdy zmienia się initialLinks
    $effect(() => {
        if (initialLinks && initialLinks !== hosterLinks) {
            hosterLinks = initialLinks;
        }
    });

    // Sprawdź czy są linki z komponentu Sweech w localStorage
    onMount(() => {
        if (typeof window !== 'undefined') {
            const sweechLinks = localStorage.getItem('sweechLinks');
            if (sweechLinks) {
                hosterLinks = sweechLinks;
                // Wyczyść localStorage po wczytaniu linków
                localStorage.removeItem('sweechLinks');
            }

            // Focus na pole tekstowe
            setTimeout(() => {
                const textarea = document.getElementById('hosterLinks');
                if (textarea) {
                    textarea.focus();
                }
            }, 100);
        }
    });

    // Funkcja do odblokowania linków
    async function handleUnrestrictLinks() {
        if (!hosterLinks.trim()) {
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification('Wprowadź co najmniej jeden link', 'error');
            }
            return;
        }

        // Podziel tekst na linie i odfiltruj puste oraz linie zaczynające się od #
        const links = hosterLinks.split('\n')
            .map(link => link.trim())
            .filter(link => link.length > 0 && !link.startsWith('#'));

        if (links.length === 0) {
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification('Wprowadź co najmniej jeden poprawny link', 'error');
            }
            return;
        }

        isSubmitting = true;

        // Utwórz placeholdery dla wszystkich linków
        unrestrictedLinks = links.map(url => ({
            url,
            status: 'pending'
        }));

        // Przetwórz każdy link
        for (let i = 0; i < links.length; i++) {
            const link = links[i];

            try {
                const result = await unrestrictLink(link);

                // Aktualizuj UI z sukcesem
                const filename = result.filename || extractFilename(result.download) || 'Plik';

                unrestrictedLinks[i] = {
                    ...unrestrictedLinks[i],
                    status: 'success',
                    downloadUrl: result.download,
                    filename
                };
            } catch (error) {
                console.error('Błąd odblokowania linku:', error);

                // Aktualizuj UI z błędem
                unrestrictedLinks[i] = {
                    ...unrestrictedLinks[i],
                    status: 'error',
                    error: error instanceof Error ? error.message : 'Nieznany błąd'
                };
            }
        }

        isSubmitting = false;

        if (typeof window !== 'undefined' && (window as any).showNotification) {
            const successCount = unrestrictedLinks.filter(link => link.status === 'success').length;
            if (successCount > 0) {
                (window as any).showNotification(
                    `Odblokowano ${successCount} z ${links.length} linków`,
                    'success'
                );
            } else {
                (window as any).showNotification('Nie udało się odblokować żadnego linku', 'error');
            }
        }
    }

    // Funkcja do kopiowania linku do schowka
    async function copyToClipboard(text: string) {
        try {
            await navigator.clipboard.writeText(text);

            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification('Skopiowano do schowka', 'success');
            }
        } catch (error) {
            console.error('Błąd kopiowania do schowka:', error);

            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification('Nie udało się skopiować do schowka', 'error');
            }
        }
    }

    // Funkcja do usuwania linku z listy
    function removeLink(index: number) {
        unrestrictedLinks = unrestrictedLinks.filter((_, i) => i !== index);
    }
</script>

<div class="card">
    <h2>Hostingi</h2>

    <div class="input-group" style="margin-top: 20px;">
        <label for="hosterLinks">Linki z hostingów (każdy link w nowej linii)</label>
        <textarea id="hosterLinks" class="magnet-input"
                  style="height: 150px; resize: vertical;"
                  placeholder="Wklej linki tutaj, każdy w nowej linii, np:&#10;https://example1.com/file&#10;https://example2.com/file"
                  bind:value={hosterLinks}
                  disabled={isSubmitting}></textarea>
    </div>

    <button class="btn btn-primary"
            style="margin-top: 20px;"
            onclick={handleUnrestrictLinks}
            disabled={isSubmitting}>
        {#if isSubmitting}
            Odblokowanie...<div class="loading"></div>
        {:else}
            Odblokuj linki
        {/if}
    </button>

    {#if unrestrictedLinks.length > 0}
        <div style="margin-top: 20px;">
            {#each unrestrictedLinks as link, index}
                <div style="background-color: #2d2d2d; border-radius: 6px; padding: 15px; margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px;">
                        <div style="overflow: hidden; text-overflow: ellipsis; white-space: wrap; color: var(--text-secondary); font-family: monospace; font-size: 13px;">
                            {link.filename || link.url}
                        </div>
                        <div style="display: flex; gap: 8px;">
                            {#if link.status === 'success' && link.downloadUrl}
                                <button class="btn btn-outline" onclick={() => copyToClipboard(link.downloadUrl || '')}>
                                    Kopiuj
                                </button>
                                <button class="btn btn-primary" onclick={() => {
                                    // Pokaż modal sukcesu
                                    successModalData = {
                                        isOpen: true,
                                        coverUrl: '', // Nie mamy okładki dla linków
                                        title: link.filename || 'Plik'
                                    };

                                    // Utwórz ukryty link i kliknij go zamiast otwierać nowe okno
                                    setTimeout(() => {
                                        const downloadLink = document.createElement('a');
                                        downloadLink.href = link.downloadUrl || '';
                                        downloadLink.download = link.filename || 'download';
                                        downloadLink.style.display = 'none';
                                        document.body.appendChild(downloadLink);
                                        downloadLink.click();
                                        document.body.removeChild(downloadLink);
                                    }, 500); // Małe opóźnienie, aby animacja była widoczna przed rozpoczęciem pobierania
                                }}>
                                    Pobierz
                                </button>
                            {:else if link.status === 'pending'}
                                <div class="loading"></div>
                            {:else}
                                <button class="btn btn-error" onclick={() => removeLink(index)}>
                                    Usuń
                                </button>
                            {/if}
                        </div>
                    </div>
                    <div style="width: 100%; font-size: 13px; margin-top: 5px;" class={link.status}>
                        {#if link.status === 'pending'}
                            Przetwarzanie...
                        {:else if link.status === 'success'}
                            Odblokowano pomyślnie
                        {:else}
                            Błąd: {link.error || 'Nie udało się odblokować linku'}
                        {/if}
                    </div>
                </div>
            {/each}
        </div>
    {/if}
</div>

<!-- Modal sukcesu pobierania -->
<DownloadSuccessModal
    bind:open={successModalData.isOpen}
    coverUrl={successModalData.coverUrl}
    title={successModalData.title}
    autoCloseTimeout={3000}
/>
