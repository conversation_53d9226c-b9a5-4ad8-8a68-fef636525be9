<script lang="ts">
    /**
     * Size of the spinner in pixels
     */
    export let size: number = 40;
    
    /**
     * Color of the spinner
     */
    export let color: string = 'var(--primary)';
    
    /**
     * Thickness of the spinner in pixels
     */
    export let thickness: number = 4;
    
    /**
     * Whether to show a label below the spinner
     */
    export let showLabel: boolean = false;
    
    /**
     * Label text to show below the spinner
     */
    export let label: string = 'Loading...';
</script>

<div class="spinner-container">
    <div 
        class="spinner" 
        style="width: {size}px; height: {size}px; border-width: {thickness}px; border-top-color: {color};"
        role="status"
        aria-label="Loading"
    ></div>
    
    {#if showLabel}
        <div class="spinner-label">{label}</div>
    {/if}
</div>

<style>
    .spinner-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    
    .spinner {
        border-style: solid;
        border-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    .spinner-label {
        margin-top: 0.75rem;
        font-size: 0.875rem;
        color: var(--text-secondary);
    }
    
    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>
