<script lang="ts">
    import { fly, fade } from 'svelte/transition';
    import { toastStore, type Toast } from '$lib/stores/toastStore';
    import { 
        CheckCircle, 
        XCircle, 
        AlertTriangle, 
        Info, 
        X 
    } from 'lucide-svelte';
    
    /**
     * Position of the toast container
     */
    export let position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center' = 'top-right';
    
    /**
     * Maximum number of toasts to show at once
     */
    export let max: number = 5;
    
    // Get icon based on toast type
    function getIcon(type: Toast['type']) {
        switch (type) {
            case 'success':
                return CheckCircle;
            case 'error':
                return XCircle;
            case 'warning':
                return AlertTriangle;
            case 'info':
                return Info;
            default:
                return Info;
        }
    }
    
    // Get animation based on position
    function getAnimation(pos: typeof position) {
        if (pos.startsWith('top')) {
            return { y: -20 };
        } else {
            return { y: 20 };
        }
    }
</script>

<div class="toast-container {position}">
    {#each $toastStore.slice(0, max) as toast (toast.id)}
        <div 
            class="toast toast-{toast.type}"
            transition:fly={{ ...getAnimation(position), duration: 200 }}
            role="alert"
            aria-live="polite"
        >
            <div class="toast-icon">
                <svelte:component this={getIcon(toast.type)} size={20} />
            </div>
            
            <div class="toast-content">
                {#if toast.title}
                    <div class="toast-title">{toast.title}</div>
                {/if}
                <div class="toast-message">{toast.message}</div>
            </div>
            
            {#if toast.dismissible !== false}
                <button 
                    class="toast-close"
                    on:click={() => toastStore.remove(toast.id)}
                    aria-label="Close notification"
                >
                    <X size={16} />
                </button>
            {/if}
            
            {#if toast.duration && toast.duration > 0}
                <div 
                    class="toast-progress"
                    style="animation-duration: {toast.duration}ms;"
                ></div>
            {/if}
        </div>
    {/each}
</div>

<style>
    .toast-container {
        position: fixed;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        max-width: 24rem;
        width: calc(100% - 2rem);
        pointer-events: none;
    }
    
    /* Positions */
    .toast-container.top-right {
        top: 1rem;
        right: 1rem;
        align-items: flex-end;
    }
    
    .toast-container.top-left {
        top: 1rem;
        left: 1rem;
        align-items: flex-start;
    }
    
    .toast-container.bottom-right {
        bottom: 1rem;
        right: 1rem;
        align-items: flex-end;
    }
    
    .toast-container.bottom-left {
        bottom: 1rem;
        left: 1rem;
        align-items: flex-start;
    }
    
    .toast-container.top-center {
        top: 1rem;
        left: 50%;
        transform: translateX(-50%);
        align-items: center;
    }
    
    .toast-container.bottom-center {
        bottom: 1rem;
        left: 50%;
        transform: translateX(-50%);
        align-items: center;
    }
    
    .toast {
        display: flex;
        align-items: flex-start;
        padding: 1rem;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        pointer-events: auto;
        position: relative;
        overflow: hidden;
        width: 100%;
        max-width: 24rem;
    }
    
    /* Toast types */
    .toast-success {
        background-color: rgba(34, 197, 94, 0.1);
        border: 1px solid rgba(34, 197, 94, 0.2);
    }
    
    .toast-error {
        background-color: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .toast-warning {
        background-color: rgba(245, 158, 11, 0.1);
        border: 1px solid rgba(245, 158, 11, 0.2);
    }
    
    .toast-info {
        background-color: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.2);
    }
    
    .toast-icon {
        flex-shrink: 0;
        margin-right: 0.75rem;
        margin-top: 0.125rem;
    }
    
    .toast-success .toast-icon {
        color: rgb(34, 197, 94);
    }
    
    .toast-error .toast-icon {
        color: rgb(239, 68, 68);
    }
    
    .toast-warning .toast-icon {
        color: rgb(245, 158, 11);
    }
    
    .toast-info .toast-icon {
        color: rgb(59, 130, 246);
    }
    
    .toast-content {
        flex: 1;
        min-width: 0;
    }
    
    .toast-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: var(--text);
    }
    
    .toast-message {
        color: var(--text-secondary);
        font-size: 0.875rem;
        line-height: 1.4;
        word-break: break-word;
    }
    
    .toast-close {
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 0.25rem;
        margin: -0.25rem;
        margin-left: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.25rem;
        flex-shrink: 0;
    }
    
    .toast-close:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text);
    }
    
    .toast-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background-color: rgba(255, 255, 255, 0.2);
        width: 100%;
        transform-origin: left;
        animation: progress linear forwards;
    }
    
    @keyframes progress {
        0% {
            transform: scaleX(1);
        }
        100% {
            transform: scaleX(0);
        }
    }
    
    /* Responsive adjustments */
    @media (max-width: 640px) {
        .toast-container {
            width: calc(100% - 1rem);
            max-width: none;
        }
        
        .toast-container.top-right,
        .toast-container.top-left,
        .toast-container.top-center {
            top: 0.5rem;
            right: 0.5rem;
            left: 0.5rem;
            transform: none;
            align-items: stretch;
        }
        
        .toast-container.bottom-right,
        .toast-container.bottom-left,
        .toast-container.bottom-center {
            bottom: 0.5rem;
            right: 0.5rem;
            left: 0.5rem;
            transform: none;
            align-items: stretch;
        }
        
        .toast {
            max-width: none;
        }
    }
</style>
