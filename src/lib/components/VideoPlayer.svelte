<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import { X, Maximize, Minimize, SkipForward, Volume2, VolumeX, Loader2 } from 'lucide-svelte';
    import Hls from 'hls.js';

    export let url: string = '';
    export let title: string = '';
    export let onClose: () => void = () => {};
    export let skipTimes: number[] = [-1, -1, -1, -1]; // [openingStart, openingEnd, endingStart, endingEnd]
    export let onPrevious: (() => void) | null = null;
    export let onNext: (() => void) | null = null;
    export let hasPrevious: boolean = false;
    export let hasNext: boolean = false;

    let playerContainer: HTMLDivElement;
    let videoElement: HTMLVideoElement;
    let iframe: HTMLIFrameElement;
    let isFullscreen = false;
    let isMuted = false;
    let isPlaying = false;
    let playerType = 'unknown';
    let cdaId = '';
    let vkId = '';
    let directUrl = '';
    let showControls = true; // Always show controls by default
    let controlsTimeout: ReturnType<typeof setTimeout> = setTimeout(() => {}, 0); // Initialize with dummy timeout
    let showSkipButton = false;
    let currentSkipType = '';
    let skipInterval: ReturnType<typeof setInterval>;
    let isLoading = true; // Loading state for player initialization
    let hlsInstance: Hls | null = null; // HLS.js instance

    // Function to detect player type based on URL
    async function detectPlayerType(url: string): Promise<void> {
        if (url.includes('cda.pl')) {
            // Try to extract direct URL for CDA.pl
            try {
                // Use regular fetch since the endpoint is now public
                const response = await fetch(`/api/video/extract?url=${encodeURIComponent(url)}`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.directUrl) {
                        // If we got a direct URL, determine the player type
                        if (data.isHlsStream) {
                            playerType = 'hls';
                            console.log('Successfully extracted HLS stream URL from CDA.pl');
                        } else {
                            playerType = 'direct';
                            console.log('Successfully extracted direct URL from CDA.pl');
                        }
                        directUrl = data.directUrl;
                        return;
                    } else {
                        console.warn('Could not extract direct URL from CDA.pl, falling back to iframe');
                        // Show a notification to the user
                        if (typeof window !== 'undefined' && (window as any).showNotification) {
                            (window as any).showNotification('Nie udało się pobrać bezpośredniego linku do wideo. Otwieranie w trybie iframe.', 'warning');
                        }
                    }
                } else {
                    console.warn(`API error when extracting CDA.pl URL: ${response.status}, falling back to iframe`);
                }
            } catch (error) {
                console.error('Error extracting CDA.pl URL:', error);
            }

            // Fallback to iframe if extraction fails
            playerType = 'cda';
            // Extract CDA video ID
            const match = url.match(/[\/=]([\w-]{5,12})(?:$|[\/&?])/);
            if (match && match[1]) {
                cdaId = match[1];
            }
        } else if (url.includes('vk.com')) {
            playerType = 'vk';
            // Extract VK video ID
            const match = url.match(/video(-?\d+)_(\d+)/);
            if (match) {
                vkId = `${match[1]}_${match[2]}`;
            }
        } else if (url.match(/\.(mp4|webm|ogg|mkv)($|\?)/i)) {
            playerType = 'direct';
            directUrl = url;
        } else if (url.match(/\.(m3u8)($|\?)/i) || url.includes('.m3u8')) {
            // HLS stream (m3u8)
            playerType = 'hls';
            directUrl = url;
            console.log('Detected HLS stream:', url);
        } else {
            playerType = 'iframe';
        }
    }

    // Function to create the appropriate player
    function createPlayer(): void {
        if (!playerContainer) return;

        // Clear container
        playerContainer.innerHTML = '';

        if (playerType === 'cda' && cdaId) {
            // Create CDA player
            iframe = document.createElement('iframe');
            iframe.src = `https://www.cda.pl/video/embed/${cdaId}`;
            iframe.width = '100%';
            iframe.height = '100%';
            iframe.allowFullscreen = true;
            iframe.style.border = 'none';
            playerContainer.appendChild(iframe);
        } else if (playerType === 'vk' && vkId) {
            // Create VK player
            iframe = document.createElement('iframe');
            iframe.src = `https://vk.com/video_ext.php?oid=${vkId.split('_')[0]}&id=${vkId.split('_')[1]}&hd=2`;
            iframe.width = '100%';
            iframe.height = '100%';
            iframe.allowFullscreen = true;
            iframe.style.border = 'none';
            playerContainer.appendChild(iframe);
        } else if ((playerType === 'direct' || playerType === 'hls') && directUrl) {
            // Create video player for direct or HLS streams
            videoElement = document.createElement('video');

            if (playerType === 'hls') {
                // For HLS streams, use hls.js if supported
                if (Hls.isSupported()) {
                    console.log('Using HLS.js for m3u8 stream');

                    // Clean up any existing HLS instance
                    if (hlsInstance) {
                        hlsInstance.destroy();
                    }

                    // Create new HLS instance
                    hlsInstance = new Hls({
                        enableWorker: true,
                        lowLatencyMode: true,
                        backBufferLength: 90
                    });

                    // Bind HLS to video element
                    hlsInstance.loadSource(directUrl);
                    hlsInstance.attachMedia(videoElement);

                    // Handle HLS events
                    hlsInstance.on(Hls.Events.MANIFEST_PARSED, () => {
                        console.log('HLS manifest parsed, attempting to play');
                        videoElement.play().catch(error => {
                            console.error('Auto-play failed:', error);
                        });
                    });

                    hlsInstance.on(Hls.Events.ERROR, (_event, data) => {
                        console.error('HLS error:', data);
                        if (data.fatal) {
                            switch(data.type) {
                                case Hls.ErrorTypes.NETWORK_ERROR:
                                    console.log('Fatal network error, trying to recover');
                                    hlsInstance?.startLoad();
                                    break;
                                case Hls.ErrorTypes.MEDIA_ERROR:
                                    console.log('Fatal media error, trying to recover');
                                    hlsInstance?.recoverMediaError();
                                    break;
                                default:
                                    console.error('Fatal HLS error, cannot recover');
                                    break;
                            }
                        }
                    });
                } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
                    // For Safari which has native HLS support
                    console.log('Using native HLS support');
                    videoElement.src = directUrl;
                } else {
                    console.error('HLS is not supported in this browser');
                    // Show a notification to the user
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert('Ten format wideo nie jest obsługiwany w tej przeglądarce', 'error');
                    }
                }
            } else {
                // For direct video URLs
                videoElement.src = directUrl;
            }

            // Common video element setup
            videoElement.controls = false;
            videoElement.width = playerContainer.clientWidth;
            videoElement.height = playerContainer.clientHeight;
            videoElement.style.width = '100%';
            videoElement.style.height = '100%';
            videoElement.style.objectFit = 'contain';
            videoElement.addEventListener('play', () => { isPlaying = true; });
            videoElement.addEventListener('pause', () => { isPlaying = false; });
            videoElement.addEventListener('timeupdate', checkSkipTimes);
            playerContainer.appendChild(videoElement);

            // Auto play for direct videos (HLS videos are played after manifest is parsed)
            if (playerType === 'direct') {
                videoElement.play().catch(error => {
                    console.error('Auto-play failed:', error);
                });
            }
        } else {
            // Default to iframe for other sources
            iframe = document.createElement('iframe');
            iframe.src = url;
            iframe.width = '100%';
            iframe.height = '100%';
            iframe.allowFullscreen = true;
            iframe.style.border = 'none';
            playerContainer.appendChild(iframe);
        }
    }

    // Function to toggle fullscreen
    function toggleFullscreen(): void {
        if (!document.fullscreenElement) {
            playerContainer.requestFullscreen().catch(err => {
                console.error(`Error attempting to enable fullscreen: ${err.message}`);
            });
            isFullscreen = true;
        } else {
            document.exitFullscreen();
            isFullscreen = false;
        }
    }

    // Function to toggle play/pause for direct video
    function togglePlay(): void {
        if (!videoElement) return;

        if (videoElement.paused) {
            videoElement.play();
        } else {
            videoElement.pause();
        }
    }

    // Function to toggle mute for direct video
    function toggleMute(): void {
        if (!videoElement) return;

        videoElement.muted = !videoElement.muted;
        isMuted = videoElement.muted;
    }

    // Function to check if we should show skip button
    function checkSkipTimes(): void {
        if (!videoElement) return;

        const currentTime = videoElement.currentTime;

        // Check opening
        if (skipTimes[0] > 0 && skipTimes[1] > 0) {
            if (currentTime >= skipTimes[0] && currentTime < skipTimes[1]) {
                showSkipButton = true;
                currentSkipType = 'opening';
                return;
            }
        }

        // Check ending
        if (skipTimes[2] > 0 && skipTimes[3] > 0) {
            if (currentTime >= skipTimes[2] && currentTime < skipTimes[3]) {
                showSkipButton = true;
                currentSkipType = 'ending';
                return;
            }
        }

        showSkipButton = false;
    }

    // Function to skip opening/ending
    function skipSection(): void {
        if (!videoElement) return;

        if (currentSkipType === 'opening' && skipTimes[1] > 0) {
            videoElement.currentTime = skipTimes[1];
        } else if (currentSkipType === 'ending' && skipTimes[3] > 0) {
            videoElement.currentTime = skipTimes[3];
        }

        showSkipButton = false;
    }

    // Function to show controls - now they stay visible by default
    function showControlsTemporarily(): void {
        showControls = true;

        // Controls now stay visible by default - no timeout to hide them
        clearTimeout(controlsTimeout);
    }

    // Handle mouse movement to show controls
    function handleMouseMove(): void {
        showControlsTemporarily();
    }

    // Handle keyboard shortcuts
    function handleKeydown(event: KeyboardEvent): void {
        if (event.key === 'f') {
            toggleFullscreen();
        } else if (event.key === ' ' && (playerType === 'direct' || playerType === 'hls')) {
            togglePlay();
            event.preventDefault();
        } else if (event.key === 'm' && (playerType === 'direct' || playerType === 'hls')) {
            toggleMute();
        } else if (event.key === 'Escape' && !document.fullscreenElement) {
            onClose();
        }
    }

    // Clean up function
    function cleanup(): void {
        clearTimeout(controlsTimeout);
        clearInterval(skipInterval);

        if (videoElement) {
            videoElement.removeEventListener('timeupdate', checkSkipTimes);
        }

        // Clean up HLS instance if it exists
        if (hlsInstance) {
            hlsInstance.destroy();
            hlsInstance = null;
        }

        document.removeEventListener('keydown', handleKeydown);
    }

    onMount(async () => {
        isLoading = true;

        try {
            // Detect player type (now async)
            await detectPlayerType(url);

            // Create the player after type detection
            createPlayer();

            document.addEventListener('keydown', handleKeydown);

            // Set up interval to check skip times
            if ((playerType === 'direct' || playerType === 'hls') && (skipTimes[0] > 0 || skipTimes[2] > 0)) {
                skipInterval = setInterval(checkSkipTimes, 1000);
            }

            // Show controls initially, then hide after delay
            showControlsTemporarily();
        } catch (error) {
            console.error('Error initializing player:', error);
        } finally {
            isLoading = false;
        }
    });

    onDestroy(() => {
        cleanup();
    });
</script>

<div
    class="video-player-overlay"
    on:click={onClose}
    on:keydown={(e) => e.key === 'Escape' && onClose()}
    role="dialog"
    aria-label="Video player"
    tabindex="0"
>
    <div
        class="video-player-container"
        on:click|stopPropagation={() => {}}
        on:mousemove={handleMouseMove}
        bind:this={playerContainer}
        role="presentation"
    >
        <!-- Loading indicator -->
        {#if isLoading}
            <div class="loading-overlay">
                <div class="loading-spinner">
                    <Loader2 size={48} />
                </div>
                <p>Przygotowywanie odtwarzacza...</p>
            </div>
        {/if}

        <!-- Player will be created here by JavaScript -->

        <!-- Controls overlay -->
        {#if showControls || showSkipButton}
            <div class="player-controls" class:fade-out={!showControls && !showSkipButton}>
                <div class="top-controls">
                    <div class="title">{title}</div>
                    <div class="control-buttons">
                        {#if playerType === 'direct' || playerType === 'hls'}
                            <button on:click={toggleMute} class="control-button">
                                {#if isMuted}
                                    <VolumeX size={20} />
                                {:else}
                                    <Volume2 size={20} />
                                {/if}
                            </button>
                        {/if}
                        <button on:click={toggleFullscreen} class="control-button">
                            {#if isFullscreen}
                                <Minimize size={20} />
                            {:else}
                                <Maximize size={20} />
                            {/if}
                        </button>
                        <button on:click={onClose} class="control-button">
                            <X size={20} />
                        </button>
                    </div>
                </div>

                <div class="bottom-controls">
                    {#if showSkipButton}
                        <div class="skip-button-container">
                            <button on:click={skipSection} class="skip-button">
                                <SkipForward size={16} />
                                <span>Pomiń {currentSkipType === 'opening' ? 'opening' : 'ending'}</span>
                            </button>
                        </div>
                    {/if}

                    <div class="navigation-buttons">
                        {#if hasPrevious && onPrevious}
                            <button on:click={onPrevious} class="nav-button">
                                <span>Poprzedni odcinek</span>
                            </button>
                        {/if}

                        {#if hasNext && onNext}
                            <button on:click={onNext} class="nav-button">
                                <span>Następny odcinek</span>
                            </button>
                        {/if}
                    </div>
                </div>
            </div>
        {/if}
    </div>
</div>

<style>
    .video-player-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        z-index: 1000;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .video-player-container {
        position: relative;
        width: 80%;
        height: 70%;
        max-width: 1100px;
        background-color: #000;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        overflow: hidden;
        border-radius: 12px;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10;
        color: white;
    }

    .loading-overlay p {
        margin-top: 16px;
        font-size: 18px;
        font-weight: 500;
    }

    .loading-spinner {
        animation: spin 1.5s linear infinite;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    .player-controls {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7) 0%, transparent 30%, transparent 70%, rgba(0, 0, 0, 0.7) 100%);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 20px;
        box-sizing: border-box;
        opacity: 1;
        transition: opacity 0.3s ease;
    }

    .fade-out {
        opacity: 0;
    }

    .top-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .title {
        color: white;
        font-size: 18px;
        font-weight: 600;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
        max-width: 70%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .control-buttons {
        display: flex;
        gap: 10px;
    }

    .control-button {
        background: rgba(0, 0, 0, 0.5);
        border: none;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .control-button:hover {
        background: rgba(236, 72, 153, 0.7);
    }

    .skip-button-container {
        align-self: flex-end;
        margin-bottom: 40px;
    }

    .skip-button {
        background: rgba(236, 72, 153, 0.8);
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.2s;
    }

    .skip-button:hover {
        background: rgba(236, 72, 153, 1);
    }

    .bottom-controls {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        margin-bottom: 20px;
    }

    .navigation-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }

    .nav-button {
        background: rgba(0, 0, 0, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        padding: 8px 16px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.2s;
    }

    .nav-button:hover {
        background: rgba(0, 0, 0, 0.8);
        border-color: rgba(236, 72, 153, 0.7);
    }

    @media (max-width: 768px) {
        .video-player-container {
            width: 100%;
            height: 100%;
            max-width: none;
        }

        .title {
            font-size: 16px;
        }
    }
</style>
