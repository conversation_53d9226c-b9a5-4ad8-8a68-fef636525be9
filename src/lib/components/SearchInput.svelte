<script lang="ts">
    import { Search, X } from 'lucide-svelte';
    import { createEventDispatcher } from 'svelte';
    
    /**
     * Current search query
     */
    export let query: string = '';
    
    /**
     * Placeholder text
     */
    export let placeholder: string = 'Search...';
    
    /**
     * Whether to auto-focus the input
     */
    export let autofocus: boolean = false;
    
    /**
     * Minimum query length before search is triggered
     */
    export let minLength: number = 3;
    
    /**
     * Debounce time in milliseconds
     */
    export let debounceTime: number = 300;
    
    /**
     * Whether to show a clear button
     */
    export let showClear: boolean = true;
    
    /**
     * Whether to show a search button
     */
    export let showButton: boolean = true;
    
    /**
     * Whether the search is currently loading
     */
    export let loading: boolean = false;
    
    // Internal state
    let inputElement: HTMLInputElement;
    let debounceTimeout: number | null = null;
    
    // Event dispatcher
    const dispatch = createEventDispatcher<{
        search: { query: string };
        clear: void;
        input: { query: string };
    }>();
    
    /**
     * <PERSON>le input changes with debounce
     */
    function handleInput() {
        // Clear any existing timeout
        if (debounceTimeout) {
            clearTimeout(debounceTimeout);
        }
        
        // Dispatch input event immediately
        dispatch('input', { query });
        
        // If query is too short, don't trigger search
        if (query.length < minLength) {
            return;
        }
        
        // Set a new timeout
        debounceTimeout = setTimeout(() => {
            dispatch('search', { query });
        }, debounceTime) as unknown as number;
    }
    
    /**
     * Handle search button click
     */
    function handleSearch() {
        if (query.length >= minLength) {
            dispatch('search', { query });
        }
    }
    
    /**
     * Clear the search input
     */
    function clearSearch() {
        query = '';
        dispatch('clear');
        inputElement.focus();
    }
    
    /**
     * Handle key press events
     */
    function handleKeyDown(event: KeyboardEvent) {
        if (event.key === 'Enter') {
            handleSearch();
        } else if (event.key === 'Escape') {
            clearSearch();
        }
    }
</script>

<div class="search-container">
    <div class="search-input-wrapper">
        <div class="search-icon">
            <Search size={18} />
        </div>
        
        <input
            bind:this={inputElement}
            bind:value={query}
            on:input={handleInput}
            on:keydown={handleKeyDown}
            type="text"
            class="search-input"
            {placeholder}
            autocomplete="off"
            autocorrect="off"
            spellcheck="false"
            {autofocus}
            aria-label={placeholder}
        />
        
        {#if showClear && query}
            <button 
                type="button"
                class="clear-button"
                on:click={clearSearch}
                aria-label="Clear search"
            >
                <X size={16} />
            </button>
        {/if}
    </div>
    
    {#if showButton}
        <button 
            type="button"
            class="search-button"
            on:click={handleSearch}
            disabled={query.length < minLength || loading}
            aria-label="Search"
        >
            {#if loading}
                <div class="search-spinner"></div>
            {:else}
                Search
            {/if}
        </button>
    {/if}
</div>

<style>
    .search-container {
        display: flex;
        width: 100%;
        gap: 0.5rem;
    }
    
    .search-input-wrapper {
        position: relative;
        flex: 1;
        display: flex;
        align-items: center;
    }
    
    .search-icon {
        position: absolute;
        left: 0.75rem;
        color: var(--text-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .search-input {
        width: 100%;
        height: 2.5rem;
        padding: 0 2.5rem;
        border-radius: 0.375rem;
        background-color: var(--card-bg);
        color: var(--text);
        border: 1px solid rgba(255, 255, 255, 0.1);
        font-size: 0.875rem;
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(255, 105, 180, 0.2);
    }
    
    .clear-button {
        position: absolute;
        right: 0.75rem;
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.25rem;
        border-radius: 50%;
    }
    
    .clear-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .search-button {
        height: 2.5rem;
        padding: 0 1rem;
        border-radius: 0.375rem;
        background-color: var(--primary);
        color: #000;
        border: none;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 5rem;
    }
    
    .search-button:hover:not(:disabled) {
        background-color: var(--primary-dark, #ff9eb3);
    }
    
    .search-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    .search-spinner {
        width: 1rem;
        height: 1rem;
        border: 2px solid rgba(0, 0, 0, 0.3);
        border-top-color: #000;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>
