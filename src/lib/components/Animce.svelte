<script lang="ts">
    import { onMount } from 'svelte';
    import { Search, Loader2, Play, History, ArrowLeft, ExternalLink, Star, StarOff } from 'lucide-svelte';
    import { getWithAuth } from '$lib/utils/apiUtils';
    import {
        saveWatchHistory,
        getWatchHistory,
        addToWatchlist,
        removeFromWatchlist,
        isInWatchlist,
        getWatchlist
    } from '$lib/services/playerService';
    import VideoPlayer from './VideoPlayer.svelte';

    // State variables
    let animeSearchQuery = '';
    let isSearching = false;
    let searchResults: any[] = [];
    let searchError: string | null = null;
    let selectedAnime: any = null;
    let episodeList: number[] = [];
    let totalEpisodes: number = 0;
    let isLoadingDetails = false;
    let watchHistory = getWatchHistory();
    let showHistory = false;
    let selectedEpisode: number | null = null;
    let players: any[] = [];
    let isLoadingPlayers = false;

    // Watchlist state
    let watchlist = getWatchlist();
    let showWatchlist = false;

    // Video player state
    let showVideoPlayer = false;
    let currentPlayerUrl = '';
    let currentPlayerTitle = '';
    let skipTimes: number[] = [-1, -1, -1, -1]; // [openingStart, openingEnd, endingStart, endingEnd]

    // Function to toggle anime in watchlist
    function toggleWatchlist(anime: any, event?: Event) {
        if (event) {
            event.stopPropagation(); // Prevent event bubbling
        }

        const isInList = isInWatchlist(anime.slug);

        if (isInList) {
            removeFromWatchlist(anime.slug);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Usunięto "${anime.title}" z listy do obejrzenia`, 'success');
            }
        } else {
            addToWatchlist(anime.slug, anime.title, anime.cover);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Dodano "${anime.title}" do listy do obejrzenia`, 'success');
            }
        }

        // Refresh watchlist
        watchlist = getWatchlist();
    }

    // Function to handle search input changes with debounce
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 500; // ms

    function handleSearchInput() {
        // Clear any existing timer
        clearTimeout(typingTimer);

        // Reset results if search query is cleared
        if (animeSearchQuery.trim().length === 0) {
            searchResults = [];
            return;
        }

        // Start a new timer for search
        if (animeSearchQuery.trim().length >= 3) {
            // Set a timer to execute search after the debounce interval
            typingTimer = setTimeout(() => {
                searchAnime();
            }, doneTypingInterval);
        }
    }

    // Function to handle key press in search input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && animeSearchQuery.trim().length >= 3) {
            // Clear any existing timer to prevent duplicate searches
            clearTimeout(typingTimer);
            searchAnime();
        }
    }

    // Function to search for anime
    async function searchAnime() {
        if (animeSearchQuery.trim().length < 3) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wpisz co najmniej 3 znaki', 'error');
            }
            return;
        }

        isSearching = true;
        searchResults = [];
        searchError = null;
        selectedAnime = null;
        showHistory = false;

        try {
            const response = await getWithAuth(`/api/animce/search?query=${encodeURIComponent(animeSearchQuery)}`);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Error ${response.status}`);
            }

            const data = await response.json();
            searchResults = data.results || [];

            if (searchResults.length === 0) {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(`Nie znaleziono wyników dla "${animeSearchQuery}"`, 'error');
                }
            }
        } catch (error: any) {
            console.error('Error searching anime:', error);
            searchError = error.message || 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd wyszukiwania: ${searchError}`, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Function to get anime details
    async function getAnimeDetails(anime: any) {
        selectedAnime = anime;
        isLoadingDetails = true;
        episodeList = [];
        totalEpisodes = 0;
        showHistory = false;

        try {
            const response = await getWithAuth(`/api/animce/details?slug=${encodeURIComponent(anime.slug)}`);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Error ${response.status}`);
            }

            const data = await response.json();

            // Update selected anime with additional details
            selectedAnime = { ...selectedAnime, ...data.details };

            // Create episode list
            totalEpisodes = data.episodeCount || 0;
            episodeList = Array.from({ length: totalEpisodes }, (_, i) => i + 1);
        } catch (error: any) {
            console.error('Error getting anime details:', error);
            const errorMsg = error.message || 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd pobierania szczegółów: ${errorMsg}`, 'error');
            }
        } finally {
            isLoadingDetails = false;
        }
    }

    // Function to get episode players
    async function getEpisodePlayers(episode: number) {
        selectedEpisode = episode;
        isLoadingPlayers = true;
        players = [];
        skipTimes = [-1, -1, -1, -1]; // Reset skip times

        try {
            const response = await getWithAuth(`/api/animce/players?slug=${encodeURIComponent(selectedAnime.slug)}&episode=${episode}&skipInfo=true`);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Error ${response.status}`);
            }

            const data = await response.json();
            players = data.players || [];

            // Get skip times if available
            if (data.skipTimes) {
                skipTimes = data.skipTimes;
                console.log('Skip times:', skipTimes);
            }

            // Save to watch history
            if (selectedAnime) {
                saveWatchHistory(
                    selectedAnime.slug,
                    episode,
                    selectedAnime.title || selectedAnime.title_en
                );
                // Refresh history
                watchHistory = getWatchHistory();
            }
        } catch (error: any) {
            console.error('Error getting episode players:', error);
            const errorMsg = error.message || 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd pobierania odtwarzaczy: ${errorMsg}`, 'error');
            }
        } finally {
            isLoadingPlayers = false;
        }
    }

    // Function to play episode
    function playEpisode(player: any) {
        // Get skip times if available
        const details = selectedAnime;
        if (details && details.mal_id && selectedEpisode !== null && settings.skipEnabled) {
            // We would fetch skip times here, but we already have them from the API
            // The API endpoint already provides skipTimes if requested
        }

        // Set player details
        currentPlayerUrl = player.player;
        currentPlayerTitle = `${selectedAnime?.title || ''} - Odcinek ${selectedEpisode}`;

        // Show the video player
        showVideoPlayer = true;
    }

    // Function to close the video player
    function closeVideoPlayer() {
        showVideoPlayer = false;
        currentPlayerUrl = '';
    }

    // Settings for the player
    const settings = {
        skipEnabled: true // Whether to enable opening/ending skipping
    };

    // Function to go back to anime list
    function backToAnimeList() {
        selectedAnime = null;
        episodeList = [];
        selectedEpisode = null;
        players = [];
    }

    // Function to go back to episode list
    function backToEpisodeList() {
        selectedEpisode = null;
        players = [];
    }

    // Function to toggle history view
    function toggleHistory() {
        if (showHistory) {
            // Jeśli historia jest już pokazana, ukryj ją
            showHistory = false;
        } else {
            // Pokaż historię i ukryj watchlistę
            showHistory = true;
            showWatchlist = false;
            // Refresh history when showing
            watchHistory = getWatchHistory();
        }
    }

    // Function to continue watching from history
    function continueWatching(historyItem: any) {
        // Find the anime in search results or fetch it
        const anime = searchResults.find(a => a.slug === historyItem.slug);

        if (anime) {
            getAnimeDetails(anime).then(() => {
                getEpisodePlayers(historyItem.episode);
            });
        } else {
            // Need to fetch the anime details first
            getWithAuth(`/api/animce/details?slug=${encodeURIComponent(historyItem.slug)}`)
                .then(response => response.json())
                .then(data => {
                    selectedAnime = data.details;
                    totalEpisodes = data.episodeCount || 0;
                    episodeList = Array.from({ length: totalEpisodes }, (_, i) => i + 1);
                    getEpisodePlayers(historyItem.episode);
                })
                .catch(error => {
                    console.error('Error fetching anime for history item:', error);
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert(`Błąd pobierania anime: ${error.message}`, 'error');
                    }
                });
        }

        showHistory = false;
    }

    // Function to format date
    function formatDate(timestamp: number): string {
        const date = new Date(timestamp);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    // Function to toggle watchlist view
    function toggleWatchlistView() {
        if (showWatchlist) {
            // Jeśli watchlista jest już pokazana, ukryj ją
            showWatchlist = false;
        } else {
            // Pokaż watchlistę i ukryj historię
            showWatchlist = true;
            showHistory = false;
        }
    }

    // Function to load anime from watchlist or history
    async function loadAnimeFromList(slug: string) {
        try {
            // Get anime details
            const response = await getWithAuth(`/api/animce/details?slug=${encodeURIComponent(slug)}`);

            if (!response.ok) {
                throw new Error(`Error ${response.status}`);
            }

            const data = await response.json();
            selectedAnime = data.details;
            totalEpisodes = data.episodeCount || 0;
            episodeList = Array.from({ length: totalEpisodes }, (_, i) => i + 1);

            // Hide lists
            showWatchlist = false;
            showHistory = false;
        } catch (error: any) {
            console.error('Error loading anime:', error);
            const errorMsg = error.message || 'Unknown error';
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd ładowania anime: ${errorMsg}`, 'error');
            }
        }
    }

    onMount(() => {
        // Initialize component
        watchHistory = getWatchHistory();
        watchlist = getWatchlist();

        // Focus on search input
        setTimeout(() => {
            const searchInput = document.getElementById('animeSearchQuery');
            if (searchInput) {
                searchInput.focus();
            }
        }, 100);
    });
</script>

{#if showVideoPlayer}
    <VideoPlayer
        url={currentPlayerUrl}
        title={currentPlayerTitle}
        skipTimes={skipTimes}
        onClose={closeVideoPlayer}
        onPrevious={selectedEpisode !== null && selectedEpisode > 1 ? () => selectedEpisode && getEpisodePlayers(selectedEpisode - 1) : null}
        onNext={selectedEpisode !== null && selectedEpisode < totalEpisodes ? () => selectedEpisode && getEpisodePlayers(selectedEpisode + 1) : null}
        hasPrevious={selectedEpisode !== null && selectedEpisode > 1}
        hasNext={selectedEpisode !== null && selectedEpisode < totalEpisodes}
    />
{/if}

<div class="rounded-tl-none rounded-bl-none card">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="mb-1 text-2xl font-semibold">Otaku (BETA)</h1>
            <p class="text-[var(--text-secondary)]">Wyszukaj i oglądaj anime</p>
        </div>
        {#if selectedAnime === null}
            <div class="header-actions">
                <button class="btn btn-icon" on:click={toggleWatchlistView} title="Lista do obejrzenia" class:active={showWatchlist}>
                    <Star size={24} />
                </button>
                <button class="btn btn-icon" on:click={toggleHistory} title="Historia oglądania" class:active={showHistory}>
                    <History size={24} />
                </button>
            </div>
        {/if}
    </div>

    <!-- Main content area -->
    {#if selectedAnime === null}
        <!-- Search and results view -->


        <!-- Search input -->
        <div style="display: flex; gap: 10px; position: relative;">
            <input type="text" id="animeSearchQuery" class="magnet-input"
                   style="flex: 1; padding-right: 40px;"
                   placeholder="Wpisz tytuł anime (min. 3 znaki)..."
                   bind:value={animeSearchQuery}
                   on:input={handleSearchInput}
                   on:keypress={handleKeyPress}
                   disabled={isSearching}>

            {#if isSearching}
                <div class="search-loading-indicator">
                    <Loader2 size={20} class="loading-spinner" />
                </div>
            {:else if animeSearchQuery.trim().length > 0}
                <button class="search-clear-button" on:click={() => { animeSearchQuery = ''; searchResults = []; }}>
                    &times;
                </button>
            {/if}

            <button class="btn btn-pink"
                    on:click={searchAnime}
                    disabled={isSearching || animeSearchQuery.trim().length < 3}>
                <Search size={18} strokeWidth={2} />
                <span>Szukaj</span>
            </button>
        </div>

        {#if showWatchlist && watchlist.length > 0}
            <!-- Watchlist -->
            <div class="section-title">
                <h2>Lista do obejrzenia</h2>
            </div>
            <div class="anime-grid">
                {#each watchlist as item}
                    <div class="anime-card">
                        <div
                            class="anime-card-inner"
                            on:click={() => loadAnimeFromList(item.slug)}
                            on:keydown={(e) => e.key === 'Enter' && loadAnimeFromList(item.slug)}
                            role="button"
                            tabindex="0"
                        >
                            <div class="anime-cover">
                                {#if item.cover}
                                    <img src={item.cover} alt={item.title} loading="lazy">
                                {:else}
                                    <div class="anime-cover-placeholder">
                                        <Play size={32} />
                                    </div>
                                {/if}
                                <div
                                    class="watchlist-button"
                                    on:click={(e) => toggleWatchlist(item, e)}
                                    on:keydown={(e) => e.key === 'Enter' && toggleWatchlist(item, e)}
                                    title="Usuń z listy"
                                    role="button"
                                    tabindex="0"
                                >
                                    <StarOff size={20} />
                                </div>
                            </div>
                            <div class="anime-info">
                                <h3 class="anime-title">{item.title}</h3>
                                <p class="anime-date">Dodano: {formatDate(item.addedAt)}</p>
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
        {:else if showHistory && watchHistory.length > 0}
            <!-- Watch history -->
            <div class="section-title">
                <h2>Historia oglądania</h2>
            </div>
            <div class="anime-grid">
                {#each watchHistory.slice(0, 8) as item}
                    <div class="anime-card">
                        <div
                            class="anime-card-inner"
                            on:click={() => continueWatching(item)}
                            on:keydown={(e) => e.key === 'Enter' && continueWatching(item)}
                            role="button"
                            tabindex="0"
                        >
                            <div class="anime-cover">
                                {#if item.cover}
                                    <img src={item.cover} alt={item.title} loading="lazy">
                                {:else}
                                    <div class="anime-cover-placeholder">
                                        <Play size={32} />
                                    </div>
                                {/if}
                                <div
                                    class="watchlist-button"
                                    class:watchlist-active={isInWatchlist(item.slug)}
                                    on:click={(e) => toggleWatchlist(item, e)}
                                    on:keydown={(e) => e.key === 'Enter' && toggleWatchlist(item, e)}
                                    title={isInWatchlist(item.slug) ? "Usuń z listy" : "Dodaj do listy"}
                                    role="button"
                                    tabindex="0"
                                >
                                    {#if isInWatchlist(item.slug)}
                                        <Star size={20} />
                                    {:else}
                                        <Star size={20} />
                                    {/if}
                                </div>
                                <div class="anime-episode-badge">Odcinek {item.episode}</div>
                            </div>
                            <div class="anime-info">
                                <h3 class="anime-title">{item.title}</h3>
                                <p class="anime-date">Oglądano: {formatDate(item.timestamp)}</p>
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
        {:else if isSearching}
            <!-- Loading state -->
            <div class="empty-state">
                <Loader2 size={32} class="loading-spinner" />
                <p>Wyszukiwanie...</p>
            </div>
        {:else if searchError}
            <!-- Error state -->
            <div class="empty-state">
                <p>Wystąpił błąd podczas wyszukiwania.<br/>{searchError}</p>
                <button class="btn btn-outline" style="margin-top: 10px;" on:click={searchAnime}>Spróbuj ponownie</button>
            </div>
        {:else if searchResults.length > 0}
            <!-- Search results -->
            <div class="mt-6 section-title">
                <h2>Wyniki wyszukiwania</h2>
            </div>
            <div class="anime-grid">
                {#each searchResults as anime}
                    <div class="anime-card">
                        <div
                            class="anime-card-inner"
                            on:click={() => getAnimeDetails(anime)}
                            on:keydown={(e) => e.key === 'Enter' && getAnimeDetails(anime)}
                            role="button"
                            tabindex="0"
                            aria-label="Pokaż szczegóły anime {anime.title}"
                        >
                            <div class="anime-cover">
                                {#if anime.cover}
                                    <img src={anime.cover} alt={anime.title} loading="lazy">
                                {:else}
                                    <div class="anime-cover-placeholder">
                                        <Play size={32} />
                                    </div>
                                {/if}
                                <div
                                    class="watchlist-button"
                                    class:watchlist-active={isInWatchlist(anime.slug)}
                                    on:click={(e) => toggleWatchlist(anime, e)}
                                    on:keydown={(e) => e.key === 'Enter' && toggleWatchlist(anime, e)}
                                    title={isInWatchlist(anime.slug) ? "Usuń z listy" : "Dodaj do listy"}
                                    role="button"
                                    tabindex="0"
                                >
                                    {#if isInWatchlist(anime.slug)}
                                        <Star size={20} />
                                    {:else}
                                        <Star size={20} />
                                    {/if}
                                </div>
                            </div>
                            <div class="anime-info">
                                <h3 class="anime-title">{anime.title}</h3>
                                {#if anime.title_en && anime.title_en !== anime.title}
                                    <p class="anime-title-en">{anime.title_en}</p>
                                {/if}
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
        {:else if animeSearchQuery.trim().length >= 3}
            <!-- No results -->
            <div class="empty-state">
                <p>Nie znaleziono wyników dla "{animeSearchQuery}"</p>
            </div>
        {:else}
            <!-- Initial state -->
            <div class="empty-state">
                <p>Wpisz tytuł anime, aby rozpocząć wyszukiwanie</p>
            </div>
        {/if}
    {:else if selectedEpisode === null}
        <!-- Anime details and episode list view -->
        <button
            class="back-button"
            on:click={backToAnimeList}
            aria-label="Powrót do listy anime"
        >
            <ArrowLeft size={20} />
            <span>Powrót do listy</span>
        </button>

        <div class="anime-header">
            {#if selectedAnime.cover}
                <div class="anime-cover-large">
                    <img src={selectedAnime.cover} alt={selectedAnime.title}>
                </div>
            {/if}
            <div class="anime-info-large">
                <div class="anime-title-row">
                    <h2 class="anime-title-large">{selectedAnime.title}</h2>
                    <div
                        class="watchlist-button-large"
                        class:watchlist-active={isInWatchlist(selectedAnime.slug)}
                        on:click={(e) => toggleWatchlist(selectedAnime, e)}
                        on:keydown={(e) => e.key === 'Enter' && toggleWatchlist(selectedAnime, e)}
                        title={isInWatchlist(selectedAnime.slug) ? "Usuń z listy" : "Dodaj do listy"}
                        role="button"
                        tabindex="0"
                    >
                        <Star size={24} />
                    </div>
                </div>

                {#if selectedAnime.title_en && selectedAnime.title_en !== selectedAnime.title}
                    <p class="anime-title-en-large">{selectedAnime.title_en}</p>
                {/if}

                {#if selectedAnime.genres && selectedAnime.genres.length > 0}
                    <div class="anime-genres">
                        {#each selectedAnime.genres as genre}
                            <span class="genre-tag">{genre}</span>
                        {/each}
                    </div>
                {/if}

                {#if selectedAnime.description}
                    <div class="anime-description">
                        <p>{selectedAnime.description}</p>
                    </div>
                {/if}
            </div>
        </div>

        <div class="section-title">
            <h2>Odcinki</h2>
            <span class="episode-count">{totalEpisodes} odcinków</span>
        </div>

        {#if isLoadingDetails}
            <div class="loading-container">
                <Loader2 size={32} class="loading-spinner" />
                <p>Ładowanie odcinków...</p>
            </div>
        {:else if episodeList.length === 0}
            <div class="empty-state">
                <p>Nie znaleziono odcinków</p>
            </div>
        {:else}
            <div class="episode-grid">
                {#each episodeList as episode}
                    <button
                        class="episode-button"
                        on:click={() => getEpisodePlayers(episode)}
                    >
                        {episode}
                    </button>
                {/each}
            </div>
        {/if}
    {:else}
        <!-- Episode players view -->
        <button
            class="back-button"
            on:click={backToEpisodeList}
            aria-label="Powrót do listy odcinków"
        >
            <ArrowLeft size={20} />
            <span>Powrót do odcinków</span>
        </button>

        <div class="episode-header">
            <h2>{selectedAnime.title} - Odcinek {selectedEpisode}</h2>
        </div>

        {#if isLoadingPlayers}
            <div class="loading-container">
                <Loader2 size={32} class="loading-spinner" />
                <p>Ładowanie odtwarzaczy...</p>
            </div>
        {:else if players.length === 0}
            <div class="empty-state">
                <p>Nie znaleziono odtwarzaczy dla tego odcinka</p>
            </div>
        {:else}
            <div class="players-list">
                {#each players as player}
                    <div class="player-item">
                        <div class="player-info">
                            <div class="player-host">{player.player_hosting}</div>
                        </div>
                        <div class="player-actions">
                            <button class="btn btn-primary" on:click={() => playEpisode(player)}>
                                <Play size={18} />
                                <span>Odtwórz</span>
                            </button>
                            <button class="btn btn-outline" on:click={() => window.open(player.player, '_blank')}>
                                <ExternalLink size={18} />
                                <span>Otwórz link</span>
                            </button>
                        </div>
                    </div>
                {/each}
            </div>

            <div class="episode-navigation">
                {#if selectedEpisode !== null && selectedEpisode > 1}
                    <button
                        class="btn btn-outline"
                        on:click={() => selectedEpisode && getEpisodePlayers(selectedEpisode - 1)}
                    >
                        Poprzedni odcinek
                    </button>
                {/if}

                {#if selectedEpisode !== null && selectedEpisode < totalEpisodes}
                    <button
                        class="btn btn-outline"
                        on:click={() => selectedEpisode && getEpisodePlayers(selectedEpisode + 1)}
                    >
                        Następny odcinek
                    </button>
                {/if}
            </div>
        {/if}
    {/if}
</div>

<style>
    .header-actions {
        display: flex;
        gap: 8px;
    }

    h2 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 16px;
    }

    h3 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
    }

    .card {
        background-color: var(--card-bg);
        border-radius: 0px 8px 8px 0;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        background-color: var(--card-bg);
        border-radius: 8px;
        text-align: center;
        margin-bottom: 20px;
    }

    .search-loading-indicator {
        position: absolute;
        right: 120px;
        top: 50%;
        transform: translateY(-50%);
    }

    .search-clear-button {
        position: absolute;
        right: 120px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: var(--text-secondary);
    }

    :global(.loading-spinner) {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    /* Section title */
    .section-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border-color, #333);
    }

    .episode-count {
        font-size: 14px;
        color: var(--text-secondary);
    }

    /* Anime grid */
    .anime-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .anime-card {
        background-color: var(--card-bg);
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .anime-card-inner {
        width: 100%;
        display: block;
        transition: transform 0.2s, box-shadow 0.2s;
        cursor: pointer;
        border: none;
        padding: 0;
        margin: 0;
        background: transparent;
        text-align: left;
        color: inherit;
    }

    .anime-card-inner:hover {
        transform: translateY(-5px);
    }

    .watchlist-button {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: rgba(0, 0, 0, 0.6);
        color: #ec4899;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 5;
        transition: background-color 0.2s, transform 0.2s;
    }

    .watchlist-button:hover {
        background-color: rgba(0, 0, 0, 0.8);
        transform: scale(1.1);
    }

    .watchlist-button.watchlist-active {
        background-color: #ec4899;
        color: white;
    }

    .watchlist-button-large {
        background-color: rgba(0, 0, 0, 0.6);
        color: #ec4899;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s, transform 0.2s;
    }

    .watchlist-button-large:hover {
        background-color: rgba(0, 0, 0, 0.8);
        transform: scale(1.1);
    }

    .watchlist-button-large.watchlist-active {
        background-color: #ec4899;
        color: white;
    }

    .anime-title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .anime-cover {
        position: relative;
        width: 100%;
        padding-top: 140%; /* Aspect ratio 10:14 */
        overflow: hidden;
    }

    .anime-cover img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
    }

    .anime-card:hover .anime-cover img {
        transform: scale(1.05);
    }

    .anime-cover-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #2a2a2a;
        color: #666;
    }

    .anime-episode-badge {
        position: absolute;
        bottom: 8px;
        left: 8px;
        padding: 4px 8px;
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        border-radius: 4px;
        font-size: 0.75rem;
    }

    .anime-info {
        padding: 12px;
    }

    .anime-title {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 4px 0;
        line-height: 1.3;
    }

    .anime-title-en {
        font-size: 14px;
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.3;
    }

    /* Anime details */

    .back-button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
        padding: 8px 16px;
        background-color: var(--button-bg, #333);
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .back-button:hover {
        background-color: var(--button-hover-bg, #444);
    }

    .anime-header {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
    }

    .anime-cover-large {
        width: 200px;
        min-width: 200px;
        height: 280px;
        border-radius: 8px;
        overflow: hidden;
    }

    .anime-cover-large img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .anime-info-large {
        flex: 1;
    }

    .anime-title-large {
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 8px 0;
    }

    .anime-title-en-large {
        font-size: 18px;
        color: var(--text-secondary);
        margin: 0 0 16px 0;
    }

    .anime-genres {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 16px;
    }

    .genre-tag {
        padding: 4px 10px;
        background-color: #ec4899;
        color: white;
        border-radius: 16px;
        font-size: 12px;
    }

    .anime-description {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-top: 16px;
    }

    /* Episode grid */
    .episode-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 10px;
        margin-top: 20px;
    }

    .episode-button {
        padding: 10px;
        background-color: var(--button-bg, #333);
        border: none;
        border-radius: 4px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s, transform 0.1s;
    }

    .episode-button:hover {
        background-color: var(--button-hover-bg, #444);
        transform: translateY(-2px);
    }

    /* Loading container */
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        text-align: center;
    }

    /* Episode players */

    .episode-header {
        margin-bottom: 20px;
    }

    .players-list {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-bottom: 30px;
    }

    .player-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background-color: var(--item-bg, #222);
        border-radius: 6px;
    }

    .player-host {
        font-weight: 600;
        font-size: 16px;
    }

    .player-actions {
        display: flex;
        gap: 10px;
    }

    .episode-navigation {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
    }

    /* Anime date */
    .anime-date {
        font-size: 12px;
        color: var(--text-tertiary, #666);
        margin-top: 4px;
    }

    /* Button styles */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s, transform 0.1s;
    }

    .btn:hover {
        transform: translateY(-2px);
    }

    .btn-primary {
        background-color: #ec4899;
        color: white;
    }

    .btn-primary:hover {
        background-color: #d61f85;
    }

    .btn-outline {
        background-color: transparent;
        border: 1px solid #ec4899;
        color: #ec4899;
    }

    .btn-outline:hover {
        background-color: rgba(236, 72, 153, 0.1);
    }

    .btn-icon {
        padding: 8px;
        border-radius: 4px;
        background-color: var(--button-bg, #333);
    }

    .btn-icon:hover {
        background-color: var(--button-hover-bg, #444);
    }

    .btn-icon.active {
        background-color: rgba(236, 72, 153, 0.1);
        color: var(--primary);
    }

    .btn-pink {
        background-color: #ec4899;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .btn-pink:hover {
        background-color: #db2777;
    }

    .btn-pink:disabled {
        background-color: #f9a8d4;
        cursor: not-allowed;
    }



    @media (max-width: 768px) {
        .anime-grid {
            grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            gap: 15px;
        }

        .anime-header {
            flex-direction: column;
        }

        .anime-cover-large {
            width: 100%;
            height: auto;
            aspect-ratio: 10/14;
            max-width: 200px;
            margin: 0 auto 20px;
        }

        .episode-grid {
            grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
        }

        .player-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .player-actions {
            width: 100%;
            justify-content: space-between;
        }
    }
</style>
