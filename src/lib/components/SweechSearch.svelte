<script lang="ts">
    import { onMount } from 'svelte';
    import { Gamepad, Download, Loader2, Settings, AlertTriangle, ChevronDown, ChevronUp } from 'lucide-svelte';
    import { getWithAuth, fetchWithAuth } from '$lib/utils/apiUtils';

    // State variables
    let sweechSearchQuery = '';
    let searchResults: any[] = [];
    let isSearching = false;
    let selectedGame: any = null;
    let isDownloading = false;
    let showConfig = false;
    let isSavingConfig = false;
    let errorMessage = '';
    let showDebugInfo = false;
    let debugInfo: any = null;

    // Debounce variables
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 3500; // 3.5 seconds debounce time

    // Configuration
    let config = {
        download_dir: '/Users/<USER>/Downloads', // Keep this for backward compatibility
        preferred_format: 'NSP',
        download_updates: true,
        download_dlc: true,
        preferred_regions: ['USA', 'EUR'],
        preferred_languages: ['English'],
        base_url: 'https://nxbrew.net/'
    };

    // UI state

    // Function to handle search input changes with debounce
    function handleSearchInput() {
        // Clear any existing timer
        clearTimeout(typingTimer);

        // Reset results if search query is cleared
        if (sweechSearchQuery.trim().length === 0) {
            searchResults = [];
            return;
        }

        // Start a new timer for search
        if (sweechSearchQuery.trim().length >= 3) {
            // Set a timer to execute search after the debounce interval
            typingTimer = setTimeout(() => {
                searchGames();
            }, doneTypingInterval);
        }
    }

    // Function to handle key press in search input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && sweechSearchQuery.trim().length >= 3) {
            // Clear any existing timer to prevent duplicate searches
            clearTimeout(typingTimer);
            searchGames();
        }
    }

    // Function to search for games
    async function searchGames() {
        if (sweechSearchQuery.trim().length < 3) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wpisz co najmniej 3 znaki', 'error');
            }
            return;
        }

        isSearching = true;
        searchResults = [];
        errorMessage = '';
        debugInfo = null;
        showDebugInfo = false;

        try {
            const response = await getWithAuth(`/api/sweech/search?query=${encodeURIComponent(sweechSearchQuery)}`);

            const data = await response.json();

            if (!response.ok) {
                errorMessage = data.error || `Błąd wyszukiwania: ${response.status}`;
                debugInfo = data.debug || null;
                console.error('Błąd wyszukiwania:', errorMessage, debugInfo);

                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(errorMessage, 'error');
                }
                return;
            }

            searchResults = data.results || [];

            if (searchResults.length === 0) {
                errorMessage = 'Nie znaleziono gier';
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(errorMessage, 'error');
                }
            }
        } catch (error) {
            console.error('Błąd wyszukiwania:', error);
            errorMessage = 'Błąd wyszukiwania: ' + (error instanceof Error ? error.message : 'Nieznany błąd');

            if (error instanceof Error) {
                debugInfo = {
                    error: error.toString(),
                    stack: error.stack,
                    message: error.message
                };
            } else {
                debugInfo = {
                    error: String(error),
                    message: 'Nieznany błąd'
                };
            }

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(errorMessage, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Toggle debug info display
    function toggleDebugInfo() {
        showDebugInfo = !showDebugInfo;
    }

    // Funkcja do przełączania zakładek (będzie przekazana z komponentu nadrzędnego)
    export let switchTab: (tab: string) => void = () => {};

    // Funkcja do formatowania linków do hostingu
    function formatLinksForHosting(links: any[]): string {
        if (!links || links.length === 0) return '';

        // Sortuj linki według kategorii
        const baseGameLinks = links.filter(link => link.category === 'Base Game');
        const updateLinks = links.filter(link => link.category === 'Update');
        const dlcLinks = links.filter(link => link.category === 'DLC');

        let formattedText = '';

        // Dodaj linki do gry podstawowej
        if (baseGameLinks.length > 0) {
            formattedText += '# Gra podstawowa\n';
            baseGameLinks.forEach(link => {
                formattedText += `${link.url}\n`;
            });
            formattedText += '\n';
        }

        // Dodaj linki do aktualizacji
        if (updateLinks.length > 0) {
            formattedText += '# Aktualizacje\n';
            updateLinks.forEach(link => {
                formattedText += `${link.url}\n`;
            });
            formattedText += '\n';
        }

        // Dodaj linki do DLC
        if (dlcLinks.length > 0) {
            formattedText += '# DLC\n';
            dlcLinks.forEach(link => {
                formattedText += `${link.url}\n`;
            });
        }

        return formattedText.trim();
    }

    // Funkcja do przekazania linków do komponentu UnrestrictLinks
    function sendLinksToUnrestrict(links: any[]) {
        const formattedLinks = formatLinksForHosting(links);

        // Zapisz linki do localStorage, aby były dostępne w komponencie UnrestrictLinks
        if (typeof window !== 'undefined') {
            localStorage.setItem('sweechLinks', formattedLinks);

            // Przełącz na zakładkę "dodaj link z hostingu"
            switchTab('hoster');
        }
    }

    // Function to download a game
    async function downloadGame(game: any) {
        if (isDownloading) return;

        isDownloading = true;
        selectedGame = game;
        errorMessage = '';
        debugInfo = null;
        showDebugInfo = false;

        try {
            // Nie pokazujemy alertu, tylko spinner w przycisku

            const response = await fetchWithAuth('/api/sweech/download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ gameUrl: game.url })
            });

            const data = await response.json();

            if (!response.ok) {
                errorMessage = `Błąd pobierania linków: ${response.status}`;
                debugInfo = data.debug || data;
                throw new Error(data.message || `Błąd pobierania linków: ${response.status}`);
            }

            if (data.success) {
                // Sprawdź czy mamy linki do pobrania
                if (data.game_info && data.game_info.links && data.game_info.links.length > 0) {
                    const links = data.game_info.links;
                    const linksCount = links.length;

                    // Zapisz informacje o linkach do debugInfo
                    debugInfo = {
                        title: data.game_info.title || game.title,
                        links: links
                    };

                    // Automatycznie przekieruj do strony dodawania linków z hostingu bez pokazywania alertu
                    sendLinksToUnrestrict(links);

                    // Pokaż krótki komunikat o sukcesie bez przycisku OK
                    if (typeof window !== 'undefined' && (window as any).showNotification) {
                        (window as any).showNotification(
                            `Znaleziono ${linksCount} linków do pobrania`,
                            'success'
                        );
                    }
                } else {
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert(
                            'Nie znaleziono linków do pobrania.',
                            'error'
                        );
                    }
                }
            } else {
                errorMessage = data.message || 'Nieznany błąd podczas pobierania linków';
                debugInfo = data.debug || data;
                throw new Error(data.message || 'Nieznany błąd podczas pobierania linków');
            }
        } catch (error) {
            console.error('Błąd pobierania linków:', error);

            if (!errorMessage) {
                errorMessage = 'Błąd pobierania linków: ' + (error instanceof Error ? error.message : 'Nieznany błąd');
            }

            if (error instanceof Error && !debugInfo) {
                debugInfo = {
                    error: error.toString(),
                    stack: error.stack,
                    message: error.message
                };
            }

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(errorMessage, 'error');
            }
        } finally {
            isDownloading = false;
            selectedGame = null;
        }
    }

    // Function to load configuration
    async function loadConfig() {
        try {
            const response = await getWithAuth('/api/sweech/config');

            if (!response.ok) {
                throw new Error(`Błąd wczytywania konfiguracji: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.config) {
                config = data.config;
            }
        } catch (error) {
            console.error('Błąd wczytywania konfiguracji:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd wczytywania konfiguracji: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        }
    }

    // Function to save configuration
    async function saveConfig() {
        if (isSavingConfig) return;

        isSavingConfig = true;

        try {
            const response = await fetchWithAuth('/api/sweech/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            if (!response.ok) {
                throw new Error(`Błąd zapisywania konfiguracji: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert('Konfiguracja zapisana pomyślnie!', 'success');
                }
                showConfig = false;
            } else {
                throw new Error(data.message || 'Nieznany błąd podczas zapisywania konfiguracji');
            }
        } catch (error) {
            console.error('Błąd zapisywania konfiguracji:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd zapisywania konfiguracji: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        } finally {
            isSavingConfig = false;
        }
    }

    // Toggle configuration panel
    function toggleConfig() {
        showConfig = !showConfig;
    }



    onMount(() => {
        // Focus on search input when component mounts
        const searchInput = document.getElementById('sweechSearchQuery');
        if (searchInput) {
            searchInput.focus();
        }

        // Load configuration
        loadConfig();
    });
</script>



<div class="card">
    <div class="flex items-center justify-between mb-4">
        <div>
            <h2>Szukaj gry na Switcha</h2>
            <p class="text-[var(--text-secondary)]">Wyszukaj i pobierz gry na Nintendo Switch</p>
        </div>
        <div class="flex gap-2">
            <button class="btn btn-icon" on:click={toggleConfig} title="Konfiguracja">
                <Settings size={32} color="white" />
            </button>
        </div>
    </div>

    {#if showConfig}
        <div class="config-panel">
            <h3 class="mb-4">Konfiguracja Sweech</h3>

            <div class="config-section">
                <h4>Podstawowe ustawienia</h4>

                <div class="hidden form-group">
                    <label for="download_dir">Katalog pobierania</label>
                    <input type="text" id="download_dir" bind:value={config.download_dir} placeholder="/Users/<USER>/Downloads">
                </div>

                <div class="form-group">
                    <label for="base_url">Adres URL nxbrew</label>
                    <input type="text" id="base_url" bind:value={config.base_url} placeholder="https://nxbrew.net/">
                </div>

                <div class="form-group">
                    <div class="label-text">Preferowany format</div>
                    <div class="radio-group">
                        <label>
                            <input type="radio" bind:group={config.preferred_format} value="NSP">
                            NSP
                        </label>
                        <label>
                            <input type="radio" bind:group={config.preferred_format} value="XCI">
                            XCI
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="label-text">Opcje pobierania</div>
                    <div class="checkbox-group">
                        <label>
                            <input type="checkbox" bind:checked={config.download_updates}>
                            Pobieraj aktualizacje
                        </label>
                        <label>
                            <input type="checkbox" bind:checked={config.download_dlc}>
                            Pobieraj DLC
                        </label>
                    </div>
                </div>
            </div>



            <div class="config-buttons">
                <button class="btn btn-secondary" on:click={toggleConfig}>Anuluj</button>
                <button class="btn btn-primary" on:click={saveConfig} disabled={isSavingConfig}>
                    {#if isSavingConfig}
                        Zapisywanie...<div class="loading"></div>
                    {:else}
                        Zapisz
                    {/if}
                </button>
            </div>
        </div>
    {:else}
        <div style="display: flex; gap: 10px; margin-top: 20px; position: relative;">
            <input type="text" id="sweechSearchQuery" class="magnet-input"
                   style="flex: 1; padding-right: 40px;"
                   placeholder="Wpisz tytuł gry (min. 3 znaki) - wyszukiwanie automatyczne..."
                   bind:value={sweechSearchQuery}
                   on:input={handleSearchInput}
                   on:keypress={handleKeyPress}
                   disabled={isSearching}>

            {#if isSearching}
                <div class="search-indicator">
                    <div class="loading"></div>
                </div>
            {/if}
        </div>
    {/if}

    {#if errorMessage}
        <div class="mt-6 error-container">
            <button
                class="w-full text-left error-header"
                on:click={toggleDebugInfo}
                on:keydown={(e) => e.key === 'Enter' && toggleDebugInfo()}
                type="button"
                aria-expanded={showDebugInfo}
                aria-controls="debug-info-panel">
                <AlertTriangle size={20} class="text-[var(--error)]" />
                <span>{errorMessage}</span>
                {#if debugInfo}
                    <div class="ml-auto btn-icon-small">
                        {#if showDebugInfo}
                            <ChevronUp size={16} />
                        {:else}
                            <ChevronDown size={16} />
                        {/if}
                    </div>
                {/if}
            </button>

            {#if showDebugInfo && debugInfo}
                <div id="debug-info-panel" class="debug-info">
                    <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
                </div>
            {/if}
        </div>
    {/if}

    {#if searchResults.length > 0}
        <div class="search-results">
            <h3 class="mt-6 mb-3">Wyniki wyszukiwania</h3>
            <div class="results-list">
                {#each searchResults as game}
                    <div class="game-item">
                        <div class="game-info">
                            <div class="game-icon">
                                <Gamepad size={24} />
                            </div>
                            <div class="game-details">
                                <h4 class="game-title">{game.title}</h4>
                                <div class="game-meta">
                                    {#if game.date}
                                        <span class="game-date">{game.date}</span>
                                    {/if}
                                    {#if game.description}
                                        <span class="game-description">{game.description.substring(0, 100)}{game.description.length > 100 ? '...' : ''}</span>
                                    {/if}
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-secondary download-btn"
                                on:click={() => downloadGame(game)}
                                disabled={isDownloading && selectedGame?.url === game.url}>
                            {#if isDownloading && selectedGame?.url === game.url}
                                <div class="flex items-center gap-2">
                                    <Loader2 size={18} class="animate-spin" />
                                    <span>Szukanie...</span>
                                </div>
                            {:else}
                                <Download size={18} />
                            {/if}
                        </button>
                    </div>
                {/each}
            </div>
        </div>
    {:else if isSearching}
        <div class="searching-indicator">
            <div class="loading-large"></div>
            <p>Wyszukiwanie gier...</p>
        </div>
    {/if}
</div>

<style>
    .search-results {
        margin-top: 20px;
    }

    .results-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: 10px;
    }

    .game-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background-color: var(--item-bg);
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .game-item:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .game-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .game-icon {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
    }

    .game-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .game-title {
        font-weight: 600;
        margin: 0;
        font-size: 1rem;
    }

    .game-meta {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .game-date, .game-description {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }

    .download-btn {
        min-width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
    }

    .download-btn:disabled {
        opacity: 0.8;
        min-width: 130px;
    }

    .searching-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        gap: 16px;
    }

    .loading-large {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top: 4px solid var(--primary);
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .config-panel {
        background-color: var(--item-bg);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 20px;
    }

    .config-section {
        margin-bottom: 24px;
    }

    .config-section h4 {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 12px;
        color: var(--primary);
    }

    .form-group {
        margin-bottom: 16px;
    }

    .form-group label, .label-text {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
    }

    .form-group input[type="text"] {
        width: 100%;
        padding: 8px 12px;
        background-color: var(--card-bg);
        border: 1px solid var(--border);
        border-radius: 4px;
        color: var(--text);
    }

    .radio-group, .checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .radio-group label, .checkbox-group label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
    }

    .config-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 24px;
    }

    .btn-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 48px;
        min-height: 48px;
        padding: 8px;
        border-radius: 8px;
        background-color: var(--item-bg);
        color: var(--text-secondary);
        transition: all 0.2s ease;
    }

    .btn-icon:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text);
    }

    .btn-icon-small {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        color: var(--text-secondary);
        transition: all 0.2s ease;
    }

    .btn-icon-small:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text);
    }

    .error-container {
        background-color: rgba(var(--error-rgb, 248, 113, 113), 0.1);
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 20px;
    }

    .error-header {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 16px;
        cursor: pointer;
        font-weight: 500;
    }

    .debug-info {
        padding: 12px 16px;
        background-color: rgba(0, 0, 0, 0.2);
        border-top: 1px solid rgba(var(--error-rgb, 248, 113, 113), 0.2);
        overflow-x: auto;
    }

    .debug-info pre {
        font-family: monospace;
        font-size: 0.85rem;
        white-space: pre-wrap;
        color: var(--text-secondary);
    }

    .search-indicator {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
    }


</style>
