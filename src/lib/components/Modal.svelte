<script lang="ts">
    import { createEventDispatcher, onMount, onDestroy } from 'svelte';
    import { fade, scale } from 'svelte/transition';
    import { X } from 'lucide-svelte';
    import { modalStore } from '$lib/stores/modalStore';

    /**
     * Whether the modal is open
     * If not provided, uses modalStore
     */
    export let open: boolean | undefined = undefined;

    /**
     * Modal title
     * If not provided, uses modalStore
     */
    export let title: string | undefined = undefined;

    /**
     * Modal message
     * If not provided, uses modalStore
     */
    export let message: string | undefined = undefined;

    /**
     * Whether to show the close button
     */
    export let showClose: boolean = true;

    /**
     * Whether to close the modal when clicking outside
     */
    export let closeOnOutsideClick: boolean = true;

    /**
     * Whether to close the modal when pressing Escape
     */
    export let closeOnEscape: boolean = true;

    /**
     * Modal size: 'sm', 'md', 'lg', 'xl', 'full'
     */
    export let size: 'sm' | 'md' | 'lg' | 'xl' | 'full' = 'md';

    /**
     * Whether to show a blur effect on the backdrop
     */
    export let blurBackdrop: boolean = true;

    // Event dispatcher
    const dispatch = createEventDispatcher<{
        close: void;
        open: void;
    }>();

    // Internal state
    let modalElement: HTMLDivElement;
    let previouslyFocused: HTMLElement | null = null;

    // Determine if we're using modalStore or props
    $: usingStore = open === undefined;
    $: isOpen = usingStore ? $modalStore.isOpen : open;
    $: modalTitle = usingStore ? $modalStore.title : title;
    $: modalMessage = usingStore ? $modalStore.message : message;

    /**
     * Close the modal
     */
    function closeModal() {
        if (usingStore) {
            modalStore.close();
        } else {
            open = false;
            dispatch('close');
        }
    }

    /**
     * Handle backdrop click
     */
    function handleBackdropClick(event: MouseEvent) {
        if (closeOnOutsideClick && event.target === event.currentTarget) {
            closeModal();
        }
    }

    /**
     * Handle key down events
     */
    function handleKeyDown(event: KeyboardEvent) {
        if (closeOnEscape && event.key === 'Escape' && isOpen) {
            closeModal();
        }
    }

    /**
     * Focus trap for accessibility
     */
    function setupFocusTrap() {
        if (!modalElement) return;

        // Save previously focused element
        previouslyFocused = document.activeElement as HTMLElement;

        // Find all focusable elements
        const focusableElements = modalElement.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        // Focus the first element
        firstElement.focus();

        // Add event listener for tab key
        modalElement.addEventListener('keydown', (event) => {
            if (event.key === 'Tab') {
                if (event.shiftKey) {
                    // If shift + tab and focus is on first element, move to last element
                    if (document.activeElement === firstElement) {
                        event.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    // If tab and focus is on last element, move to first element
                    if (document.activeElement === lastElement) {
                        event.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        });
    }

    /**
     * Restore focus when modal closes
     */
    function restoreFocus() {
        if (previouslyFocused) {
            previouslyFocused.focus();
        }
    }

    // Watch for open state changes
    $: if (isOpen) {
        document.body.style.overflow = 'hidden';
        if (!usingStore) dispatch('open');
        // Use setTimeout to ensure DOM is updated
        setTimeout(setupFocusTrap, 0);
    } else {
        document.body.style.overflow = '';
        restoreFocus();
    }

    // Add global event listener for Escape key
    onMount(() => {
        window.addEventListener('keydown', handleKeyDown);
    });

    // Clean up event listeners
    onDestroy(() => {
        window.removeEventListener('keydown', handleKeyDown);
        document.body.style.overflow = '';
    });
</script>

{#if isOpen}
    <!-- Backdrop with blur effect -->
    <div
        class="modal-backdrop {blurBackdrop ? 'with-blur' : ''}"
        on:click={handleBackdropClick}
        transition:fade={{ duration: 200 }}
        role="presentation"
    >
        <!-- Modal content -->
        <div
            bind:this={modalElement}
            class="modal-container {size}"
            on:click|stopPropagation
            on:keydown={() => {}}
            transition:scale={{ duration: 200, start: 0.95 }}
            role="dialog"
            tabindex="-1"
            aria-modal="true"
            aria-labelledby={modalTitle ? 'modal-title' : undefined}
        >
            <!-- Header -->
            {#if modalTitle || showClose}
                <div class="modal-header">
                    {#if modalTitle}
                        <h2 id="modal-title" class="modal-title">{modalTitle}</h2>
                    {/if}

                    {#if showClose}
                        <button
                            class="modal-close-btn"
                            on:click={closeModal}
                            aria-label="Close"
                        >
                            <X size={20} />
                        </button>
                    {/if}
                </div>
            {/if}

            <!-- Body -->
            <div class="modal-body">
                {#if usingStore}
                    <p>{modalMessage}</p>

                    <!-- Store-based buttons -->
                    {#if $modalStore.buttons && $modalStore.buttons.length > 0}
                        <div class="modal-footer">
                            {#each $modalStore.buttons as button}
                                <button
                                    class="btn {button.style ? `btn-${button.style}` : 'btn-primary'}"
                                    on:click={button.action}
                                >
                                    {button.text}
                                </button>
                            {/each}
                        </div>
                    {/if}
                {:else}
                    {#if modalMessage}
                        <p>{modalMessage}</p>
                    {/if}
                    <slot />
                {/if}
            </div>

            <!-- Footer slot -->
            {#if $$slots.footer && !usingStore}
                <div class="modal-footer">
                    <slot name="footer" />
                </div>
            {/if}
        </div>
    </div>
{/if}

<style>
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        padding: 1rem;
    }

    .modal-backdrop.with-blur {
        backdrop-filter: blur(4px);
    }

    .modal-container {
        background-color: var(--card-bg);
        border-radius: 8px;
        padding: 0;
        width: 100%;
        max-height: calc(100vh - 2rem);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        border: 1px solid var(--border);
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    /* Modal sizes */
    .modal-container.sm {
        max-width: 24rem;
    }

    .modal-container.md {
        max-width: 32rem;
    }

    .modal-container.lg {
        max-width: 48rem;
    }

    .modal-container.xl {
        max-width: 64rem;
    }

    .modal-container.full {
        max-width: calc(100vw - 2rem);
        max-height: calc(100vh - 2rem);
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid var(--border);
    }

    .modal-header h2 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .modal-close-btn {
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s;
    }

    .modal-close-btn:hover {
        color: var(--text);
        background-color: rgba(255, 255, 255, 0.1);
    }

    .modal-body {
        padding: 20px;
        overflow-y: auto;
        flex: 1;
    }

    .modal-body p {
        margin: 0;
        line-height: 1.5;
    }

    .modal-footer {
        padding: 16px 20px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        border-top: 1px solid var(--border);
    }
</style>
