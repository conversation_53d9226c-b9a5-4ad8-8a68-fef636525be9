<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    
    /**
     * Button text
     */
    export let text: string = '';
    
    /**
     * Button icon (Lucide icon component)
     */
    export let icon: any = null;
    
    /**
     * Button variant: 'primary', 'secondary', 'danger', 'success', 'warning', 'info'
     */
    export let variant: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'info' = 'primary';
    
    /**
     * Button size: 'sm', 'md', 'lg'
     */
    export let size: 'sm' | 'md' | 'lg' = 'md';
    
    /**
     * Whether the button is disabled
     */
    export let disabled: boolean = false;
    
    /**
     * Whether the button is loading
     */
    export let loading: boolean = false;
    
    /**
     * Whether the button is a circle (icon only)
     */
    export let circle: boolean = false;
    
    /**
     * Whether the button is full width
     */
    export let fullWidth: boolean = false;
    
    /**
     * Button type: 'button', 'submit', 'reset'
     */
    export let type: 'button' | 'submit' | 'reset' = 'button';
    
    /**
     * Additional CSS classes
     */
    export let className: string = '';
    
    /**
     * ARIA label for the button
     */
    export let ariaLabel: string = text;
    
    // Event dispatcher
    const dispatch = createEventDispatcher<{
        click: MouseEvent;
    }>();
    
    /**
     * Handle button click
     */
    function handleClick(event: MouseEvent) {
        if (disabled || loading) return;
        dispatch('click', event);
    }
    
    // Compute icon size based on button size
    $: iconSize = size === 'sm' ? 16 : size === 'md' ? 18 : 20;
</script>

<button
    {type}
    class="action-button {variant} {size} {circle ? 'circle' : ''} {fullWidth ? 'full-width' : ''} {className}"
    on:click={handleClick}
    disabled={disabled || loading}
    aria-label={ariaLabel}
    aria-disabled={disabled || loading}
>
    {#if loading}
        <div class="spinner"></div>
    {:else if icon}
        <svelte:component this={icon} size={iconSize} />
    {/if}
    
    {#if text && !circle}
        <span class="button-text">{text}</span>
    {/if}
    
    <slot />
</button>

<style>
    .action-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
        outline: none;
    }
    
    .action-button:focus-visible {
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
    }
    
    .action-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }
    
    /* Sizes */
    .action-button.sm {
        height: 2rem;
        padding: 0 0.75rem;
        font-size: 0.75rem;
    }
    
    .action-button.md {
        height: 2.5rem;
        padding: 0 1rem;
        font-size: 0.875rem;
    }
    
    .action-button.lg {
        height: 3rem;
        padding: 0 1.25rem;
        font-size: 1rem;
    }
    
    /* Circle buttons */
    .action-button.circle {
        border-radius: 50%;
        padding: 0;
    }
    
    .action-button.circle.sm {
        width: 2rem;
    }
    
    .action-button.circle.md {
        width: 2.5rem;
    }
    
    .action-button.circle.lg {
        width: 3rem;
    }
    
    /* Full width */
    .action-button.full-width {
        width: 100%;
    }
    
    /* Variants */
    .action-button.primary {
        background-color: var(--primary);
        color: #000;
    }
    
    .action-button.primary:hover:not(:disabled) {
        background-color: var(--primary-dark, #ff9eb3);
    }
    
    .action-button.secondary {
        background-color: var(--card-bg);
        color: var(--text);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .action-button.secondary:hover:not(:disabled) {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .action-button.danger {
        background-color: rgba(239, 68, 68, 0.9);
        color: white;
    }
    
    .action-button.danger:hover:not(:disabled) {
        background-color: rgb(239, 68, 68);
    }
    
    .action-button.success {
        background-color: rgba(34, 197, 94, 0.9);
        color: white;
    }
    
    .action-button.success:hover:not(:disabled) {
        background-color: rgb(34, 197, 94);
    }
    
    .action-button.warning {
        background-color: rgba(245, 158, 11, 0.9);
        color: white;
    }
    
    .action-button.warning:hover:not(:disabled) {
        background-color: rgb(245, 158, 11);
    }
    
    .action-button.info {
        background-color: rgba(59, 130, 246, 0.9);
        color: white;
    }
    
    .action-button.info:hover:not(:disabled) {
        background-color: rgb(59, 130, 246);
    }
    
    /* Spinner */
    .spinner {
        width: 1rem;
        height: 1rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: currentColor;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>
