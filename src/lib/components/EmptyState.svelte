<script lang="ts">
    /**
     * Title to display
     */
    export let title: string = 'No items found';
    
    /**
     * Message to display
     */
    export let message: string = 'There are no items to display.';
    
    /**
     * Icon component to display (Lucide icon)
     */
    export let icon: any = null;
    
    /**
     * Whether to show an action button
     */
    export let showAction: boolean = false;
    
    /**
     * Action button text
     */
    export let actionText: string = 'Add Item';
    
    /**
     * Function to call when action button is clicked
     */
    export let onAction: () => void = () => {};
</script>

<div class="empty-state">
    {#if icon}
        <div class="empty-state-icon">
            <svelte:component this={icon} size={48} strokeWidth={1.5} />
        </div>
    {/if}
    
    <h3 class="empty-state-title">{title}</h3>
    <p class="empty-state-message">{message}</p>
    
    {#if showAction}
        <button class="empty-state-action" on:click={onAction}>
            {actionText}
        </button>
    {/if}
    
    <slot />
</div>

<style>
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 3rem 1.5rem;
    }
    
    .empty-state-icon {
        color: var(--text-secondary);
        margin-bottom: 1.5rem;
    }
    
    .empty-state-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
        color: var(--text);
    }
    
    .empty-state-message {
        font-size: 1rem;
        color: var(--text-secondary);
        margin: 0 0 1.5rem 0;
        max-width: 24rem;
    }
    
    .empty-state-action {
        background-color: var(--primary);
        color: #000;
        border: none;
        border-radius: 0.375rem;
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .empty-state-action:hover {
        background-color: var(--primary-dark, #ff9eb3);
    }
</style>
