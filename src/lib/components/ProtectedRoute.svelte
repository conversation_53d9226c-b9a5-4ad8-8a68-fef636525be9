<script lang="ts">
    import { onMount } from 'svelte';
    import { goto } from '$app/navigation';
    import { authStore } from '$lib/stores/authStore';

    // Check authentication on mount
    onMount(() => {
        // Check if token is expired
        authStore.checkExpiration();
        
        // If not authenticated, redirect to login
        if (!$authStore.isAuthenticated) {
            goto('/login');
        }
    });
</script>

<slot />
