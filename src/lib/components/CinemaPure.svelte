<script lang="ts">
    import { onMount } from 'svelte';
    import { Film, Tv, Search, Loader2, Download, Copy, Subtitles, RefreshCw, TrendingUp, List, ExternalLink } from 'lucide-svelte';
    import * as m from '$lib/paraglide/messages';
    import { getWithAuth, fetchWithAuth } from '$lib/utils/apiUtils';
    import { getTraktList, getTrendingShows, getTrendingMovies, enrichWithTmdbData, type TraktItem } from '$lib/services/traktService';
    import { addMagnet, selectFiles, getTorrentInfo, unrestrictLink } from '$lib/services/debridService';

    // Props
    export let setMagnetAndSwitchTab: (magnetLink: string) => void;

    // State variables
    let cinemaSearchQuery = '';
    let searchResults: any[] = [];
    let isSearching = false;
    let searchError: string | null = null;
    let activeCategory: 'all' | 'movies' | 'tvshows' = 'all';
    let watchedItems: Set<string> = new Set();

    // Movie grouping state
    let groupedMovies: Map<string, any[]> = new Map(); // Map to store grouped movies by title+year
    let expandedMovieGroups: Set<string> = new Set(); // Set to track which groups are expanded
    let selectedMovieGroup: string | null = null; // Currently selected movie group
    let selectedMovieDetails: any = null; // Details for the selected movie
    let isLoadingMovieDetails = false; // Loading state for movie details

    // Content type selection
    let contentType: 'selection' | 'movies' | 'tvshows' = 'selection';

    // TV show navigation
    let selectedTVShow: any = null;
    let selectedEpisode: any = null;
    let episodeReleases: any[] = [];
    let viewMode: 'search' | 'episodes' | 'releases' = 'search';

    // Season filtering
    let availableSeasons: number[] = [];
    let selectedSeason: number | null = null;

    // Video player
    let showVideoPlayer = false;
    let videoUrl = '';
    let isLoadingVideo = false;
    let subtitlesUrl = '';
    let isSearchingSubtitles = false;

    // Pagination
    let currentPage = 1;
    const itemsPerPage = 10;
    let visibleResults: any[] = [];

    // Trakt.tv watchlist
    let traktShowItems: TraktItem[] = [];
    let traktMovieItems: TraktItem[] = [];
    let isTraktShowsLoading = false;
    let isTraktMoviesLoading = false;
    let traktShowsError: string | null = null;
    let traktMoviesError: string | null = null;
    let showTraktGrid = true; // Show Trakt grid by default

    // Platformy streamingowe
    let amazonPrimeShows: TraktItem[] = [];
    let disneyShows: TraktItem[] = [];
    let netflixShows: TraktItem[] = [];
    let huluShows: TraktItem[] = [];
    let hboShows: TraktItem[] = [];
    let isStreamingPlatformsLoading = false;
    let streamingPlatformsError: string | null = null;
    let showStreamingPlatforms = false; // Nie pokazuj platform streamingowych domyślnie

    // Debounce variables
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 1000; // 1 second debounce time

    // Load watched items from localStorage and fetch Trakt data
    onMount(() => {
        try {
            const storedWatchedItems = localStorage.getItem('watchedCinemaItems');
            if (storedWatchedItems) {
                watchedItems = new Set(JSON.parse(storedWatchedItems));
            }

            // Load Trakt.tv watchlist
            loadTraktWatchlist();
        } catch (error) {
            console.error('Error loading watched items:', error);
        }
    });

    // Function to load Trakt.tv watchlist
    async function loadTraktWatchlist() {
        await Promise.all([
            loadTraktShows(),
            loadTraktMovies()
        ]);
    }

    // Function to load Trakt.tv shows
    async function loadTraktShows() {
        try {
            isTraktShowsLoading = true;
            traktShowsError = null;

            try {
                // Get trending shows from Trakt.tv (limited to 18)
                const shows = await getTrendingShows(18);
                console.log(`Loaded ${shows.length} trending shows from Trakt.tv`);

                // Enrich with poster images from TMDB
                const enrichedShows = await enrichWithTmdbData(shows);
                console.log('Enriched shows with posters:', enrichedShows.filter(item => item.poster).length);

                // Update state
                traktShowItems = enrichedShows;

                // Używamy standardowych list trendujących seriali zamiast list niestandardowych
            } catch (apiError) {
                console.error('Error loading Trakt.tv shows data:', apiError);

                // If we get a 401 error, it's likely an authentication issue
                if (apiError instanceof Error && apiError.message && apiError.message.includes('401')) {
                    traktShowsError = 'Authentication error. Please log in again.';

                    // Show a more specific error notification for auth errors
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert('Wymagane ponowne logowanie. Odśwież stronę i zaloguj się ponownie.', 'error');
                    }
                } else {
                    traktShowsError = apiError instanceof Error ? apiError.message : 'Unknown error';

                    // Show error notification for non-auth errors
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert(`Błąd ładowania seriali z Trakt.tv: ${traktShowsError}`, 'error');
                    }
                }
            }
        } catch (error) {
            console.error('Error in loadTraktShows:', error);
            traktShowsError = error instanceof Error ? error.message : 'Unknown error';

            // Show error notification
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Error loading Trakt.tv shows: ${traktShowsError}`, 'error');
            }
        } finally {
            isTraktShowsLoading = false;
        }
    }

    // Function to load Trakt.tv movies
    async function loadTraktMovies() {
        try {
            isTraktMoviesLoading = true;
            traktMoviesError = null;

            try {
                // Get trending movies from Trakt.tv (limited to 18)
                const movies = await getTrendingMovies(18);
                console.log(`Loaded ${movies.length} trending movies from Trakt.tv`);

                // Enrich with poster images from TMDB
                const enrichedMovies = await enrichWithTmdbData(movies);
                console.log('Enriched movies with posters:', enrichedMovies.filter(item => item.poster).length);

                // Update state
                traktMovieItems = enrichedMovies;

                // Używamy standardowych list trendujących filmów zamiast list niestandardowych
            } catch (apiError) {
                console.error('Error loading Trakt.tv movies data:', apiError);

                // If we get a 401 error, it's likely an authentication issue
                if (apiError instanceof Error && apiError.message && apiError.message.includes('401')) {
                    traktMoviesError = 'Authentication error. Please log in again.';

                    // Show a more specific error notification for auth errors
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert('Wymagane ponowne logowanie. Odśwież stronę i zaloguj się ponownie.', 'error');
                    }
                } else {
                    traktMoviesError = apiError instanceof Error ? apiError.message : 'Unknown error';

                    // Show error notification for non-auth errors
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert(`Błąd ładowania filmów z Trakt.tv: ${traktMoviesError}`, 'error');
                    }
                }
            }
        } catch (error) {
            console.error('Error in loadTraktMovies:', error);
            traktMoviesError = error instanceof Error ? error.message : 'Unknown error';

            // Show error notification
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Error loading Trakt.tv movies: ${traktMoviesError}`, 'error');
            }
        } finally {
            isTraktMoviesLoading = false;
        }
    }

    // Save watched items to localStorage
    function saveWatchedItems() {
        try {
            localStorage.setItem('watchedCinemaItems', JSON.stringify([...watchedItems]));
        } catch (error) {
            console.error('Error saving watched items:', error);
        }
    }

    // Funkcja do ładowania list platform streamingowych
    async function loadStreamingPlatforms() {
        try {
            isStreamingPlatformsLoading = true;
            streamingPlatformsError = null;

            // Ładowanie seriali Amazon Prime
            try {
                const amazonShows = await getTraktList('garycrawfordgc', 'amazon-prime-shows', 18);
                console.log(`Loaded ${amazonShows.length} Amazon Prime shows`);

                // Wzbogacenie o obrazki
                const enrichedAmazonShows = await enrichWithTmdbData(amazonShows);
                amazonPrimeShows = enrichedAmazonShows;
            } catch (error) {
                console.error('Error loading Amazon Prime shows:', error);
            }

            // Ładowanie seriali Disney+
            try {
                const disneyPlusShows = await getTraktList('garycrawfordgc', 'disney-shows', 18);
                console.log(`Loaded ${disneyPlusShows.length} Disney+ shows`);

                // Wzbogacenie o obrazki
                const enrichedDisneyShows = await enrichWithTmdbData(disneyPlusShows);
                disneyShows = enrichedDisneyShows;
            } catch (error) {
                console.error('Error loading Disney+ shows:', error);
            }

            // Ładowanie seriali Netflix
            try {
                const netflixShowsList = await getTraktList('garycrawfordgc', 'netflix-shows', 18);
                console.log(`Loaded ${netflixShowsList.length} Netflix shows`);

                // Wzbogacenie o obrazki
                const enrichedNetflixShows = await enrichWithTmdbData(netflixShowsList);
                netflixShows = enrichedNetflixShows;
            } catch (error) {
                console.error('Error loading Netflix shows:', error);
            }

            // Ładowanie seriali Hulu
            try {
                const huluShowsList = await getTraktList('garycrawfordgc', 'hulu-shows', 18);
                console.log(`Loaded ${huluShowsList.length} Hulu shows`);

                // Wzbogacenie o obrazki
                const enrichedHuluShows = await enrichWithTmdbData(huluShowsList);
                huluShows = enrichedHuluShows;
            } catch (error) {
                console.error('Error loading Hulu shows:', error);
            }

            // Ładowanie seriali HBO/Max
            try {
                const hboShowsList = await getTraktList('garycrawfordgc', 'hbo-shows', 18);
                console.log(`Loaded ${hboShowsList.length} HBO/Max shows`);

                // Wzbogacenie o obrazki
                const enrichedHboShows = await enrichWithTmdbData(hboShowsList);
                hboShows = enrichedHboShows;
            } catch (error) {
                console.error('Error loading HBO/Max shows:', error);
            }

        } catch (error) {
            console.error('Error loading streaming platforms:', error);
            streamingPlatformsError = error instanceof Error ? error.message : 'Unknown error';
        } finally {
            isStreamingPlatformsLoading = false;
        }
    }

    // Function to set content type
    function setContentType(type: 'selection' | 'movies' | 'tvshows') {
        contentType = type;
        // Reset search when changing content type
        cinemaSearchQuery = '';
        searchResults = [];
        visibleResults = [];
        resetTVShowNavigation();

        // Set the appropriate category based on content type
        if (type === 'movies') {
            activeCategory = 'movies';
            showStreamingPlatforms = false;

            // Focus on search input after a short delay to ensure the DOM is updated
            setTimeout(() => {
                const searchInput = document.getElementById('cinemaSearchQuery');
                if (searchInput) {
                    searchInput.focus();
                }
            }, 100);
        } else if (type === 'tvshows') {
            activeCategory = 'tvshows';

            // Pokaż platformy streamingowe tylko gdy pole wyszukiwania jest puste
            showStreamingPlatforms = true;

            // Załaduj listy platform streamingowych
            loadStreamingPlatforms();

            // Focus on search input after a short delay to ensure the DOM is updated
            setTimeout(() => {
                const searchInput = document.getElementById('cinemaSearchQuery');
                if (searchInput) {
                    searchInput.focus();
                }
            }, 100);
        } else {
            activeCategory = 'all';
            showStreamingPlatforms = false;
        }

        // Show Trakt grid only in selection mode
        showTraktGrid = type === 'selection';
    }

    // Function to search for a show or movie in Trakt watchlist
    function searchTraktShow(item: TraktItem) {
        // Set content type based on item type
        if (item.type === 'movie') {
            setContentType('movies');
        } else {
            setContentType('tvshows');
        }

        // Set search query to the title
        cinemaSearchQuery = item.title;

        // Trigger search
        searchMedia();
    }

    // This function is no longer needed with the content type selection

    // Function to reset TV show navigation
    function resetTVShowNavigation() {
        selectedTVShow = null;
        selectedEpisode = null;
        episodeReleases = [];
        viewMode = 'search';
    }

    // Function to select a TV show
    function selectTVShow(tvShow: any) {
        selectedTVShow = tvShow;
        selectedEpisode = null;
        selectedSeason = null; // Reset selected season
        viewMode = 'episodes';

        // Extract all episodes for this TV show
        const episodes = searchResults.filter(result =>
            result.type === 'tvshow' &&
            result.imdbID === tvShow.imdbID &&
            result.episodeInfo !== null
        );

        // Extract available seasons
        availableSeasons = [];
        episodes.forEach(episode => {
            if (episode.episodeInfo && !availableSeasons.includes(episode.episodeInfo.season)) {
                availableSeasons.push(episode.episodeInfo.season);
            }
        });

        // Sort seasons in ascending order
        availableSeasons.sort((a, b) => a - b);

        console.log(`Found ${availableSeasons.length} seasons for ${tvShow.title}`);

        // Update visible results to show episodes
        updateVisibleResults();
        currentPage = 1;
    }

    // Function to select an episode
    function selectEpisode(episode: any) {
        selectedEpisode = episode;
        viewMode = 'releases';

        // Get all releases for this episode
        const imdbId = selectedTVShow.imdbID;
        const season = episode.episodeInfo.season;
        const episodeNum = episode.episodeInfo.episode;

        console.log(`Selected episode: ${selectedTVShow.title} S${season}E${episodeNum}`);

        // Fetch episode releases from the API
        fetchEpisodeReleases(imdbId, season, episodeNum);
    }

    // Function to fetch episode releases
    async function fetchEpisodeReleases(imdbId: string, season: number, episode: number) {
        try {
            isSearching = true;
            episodeReleases = []; // Clear previous releases

            console.log(`Fetching releases for ${imdbId} S${season}E${episode}`);

            // Call the API to get all releases for this episode
            // Use getWithAuth instead of fetch to include the JWT token
            const response = await getWithAuth(`/api/cinema/episode?imdbId=${imdbId}&season=${season}&episode=${episode}`);

            const data = await response.json();

            if (!response.ok) {
                // Handle error response with structured data
                if (data && data.error) {
                    console.error(`API error: ${data.error}`);

                    // Show a toast notification with the error message
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        let errorMessage = `Nie znaleziono wydań dla S${season}E${episode}`;

                        // If there's a more specific error message, use it
                        if (data.error) {
                            errorMessage = data.error;
                        }

                        // If there's an alternative IMDB ID, try using it
                        if (data.alternativeImdbId) {
                            errorMessage += `. Próbuję użyć alternatywnego ID: ${data.alternativeImdbId}`;
                            (window as any).showAlert(errorMessage, 'info');

                            // Try again with the alternative IMDB ID
                            console.log(`Retrying with alternative IMDB ID: ${data.alternativeImdbId}`);

                            // Update the selected TV show with the new IMDB ID
                            selectedTVShow = {
                                ...selectedTVShow,
                                imdbID: data.alternativeImdbId,
                                _originalImdbID: selectedTVShow.imdbID // Keep the original for reference
                            };

                            // Try fetching episodes again with the new IMDB ID
                            fetchEpisodeReleases(data.alternativeImdbId, season, episode);
                            return;
                        }

                        (window as any).showAlert(errorMessage, 'error');
                    }
                } else {
                    throw new Error(`API error: ${response.status}`);
                }
                return;
            }

            if (Array.isArray(data) && data.length > 0) {
                console.log(`Found ${data.length} releases for S${season}E${episode}`);

                // Debug the first few releases
                console.log('First 3 releases:', data.slice(0, 3));

                episodeReleases = data;

                // Sort releases by seeders (highest first)
                episodeReleases.sort((a, b) => b.seeders - a.seeders);

                // Make sure we're in releases view mode
                viewMode = 'releases';

                // Update visible results to show the releases
                updateVisibleResults();
            } else {
                console.error(`No releases found for S${season}E${episode}`);

                // Show a toast notification
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(`Nie znaleziono wydań dla S${season}E${episode}`, 'error');
                }
            }
        } catch (error) {
            console.error('Error fetching episode releases:', error);
            episodeReleases = [];

            // Show a toast notification
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Błąd podczas pobierania wydań', 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Function to go back to episodes view
    function backToEpisodes() {
        selectedEpisode = null;
        episodeReleases = [];
        viewMode = 'episodes';
        currentPage = 1;
        updateVisibleResults();
    }

    // Function to filter episodes by season
    function filterBySeason(season: number | null) {
        selectedSeason = season;
        currentPage = 1;
        updateVisibleResults();
    }

    // Function to update visible results based on pagination, category, and view mode
    function updateVisibleResults() {
        // If we're in a specific view mode, handle accordingly
        if (viewMode === 'releases' && selectedEpisode !== null) {
            // In releases view, show all releases for the selected episode
            console.log(`Showing ${episodeReleases.length} releases for the selected episode`);

            // Debug the releases
            if (episodeReleases.length > 0) {
                console.log('First release:', episodeReleases[0]);
            }

            visibleResults = episodeReleases;
            return;
        }

        if (viewMode === 'episodes' && selectedTVShow !== null) {
            // In episodes view, show episodes for the selected TV show
            let episodes = searchResults.filter(result =>
                result.type === 'tvshow' &&
                result.imdbID === selectedTVShow.imdbID &&
                result.episodeInfo !== null &&
                result.isEpisode === true
            );

            // Filter by season if a season is selected
            if (selectedSeason !== null) {
                episodes = episodes.filter(result =>
                    result.episodeInfo && result.episodeInfo.season === selectedSeason
                );
                console.log(`Filtered to ${episodes.length} episodes for season ${selectedSeason}`);
            }

            // Sort episodes by season and episode (ascending)
            episodes.sort((a, b) => {
                // Sort by season (ascending)
                if (a.episodeInfo.season !== b.episodeInfo.season) {
                    return a.episodeInfo.season - b.episodeInfo.season;
                }
                // Then by episode (ascending)
                return a.episodeInfo.episode - b.episodeInfo.episode;
            });

            console.log(`Showing ${episodes.length} episodes for the selected TV show`);
            visibleResults = episodes;
            return;
        }

        // Otherwise, we're in search view - filter by category
        let filteredResults = searchResults;

        if (activeCategory === 'movies') {
            // Filter movies
            const movies = searchResults.filter(result => result.type === 'movie');
            resetTVShowNavigation();

            // Group movies by title and year
            groupedMovies = new Map();

            movies.forEach(movie => {
                const groupKey = `${movie.title} (${movie.year})`;

                if (!groupedMovies.has(groupKey)) {
                    groupedMovies.set(groupKey, []);
                }

                groupedMovies.get(groupKey)?.push(movie);
            });

            // Create representative entries for each group
            filteredResults = Array.from(groupedMovies.entries()).map(([groupKey, movies]) => {
                // Use the first movie in the group as the representative
                const firstMovie = movies[0];

                return {
                    ...firstMovie,
                    isMovieGroup: true,
                    groupKey: groupKey,
                    groupCount: movies.length,
                    expanded: expandedMovieGroups.has(groupKey)
                };
            });

            // Sort groups by title
            filteredResults.sort((a, b) => a.title.localeCompare(b.title));

            // If a group is expanded, add its movies after the group entry
            if (selectedMovieGroup) {
                const groupIndex = filteredResults.findIndex(result => result.groupKey === selectedMovieGroup);

                if (groupIndex !== -1) {
                    const group = filteredResults[groupIndex];
                    const groupMovies = groupedMovies.get(selectedMovieGroup) || [];

                    // Sort group movies by quality (highest first)
                    const sortedGroupMovies = [...groupMovies].sort((a, b) => {
                        // Sort by seeders first
                        if (b.seeders !== a.seeders) {
                            return b.seeders - a.seeders;
                        }

                        // Then by quality
                        const qualityOrder = {
                            '4K': 4,
                            '1080p': 3,
                            '720p': 2,
                            '480p': 1,
                            'Unknown': 0
                        };

                        const aQuality = a.quality.split(' ')[0];
                        const bQuality = b.quality.split(' ')[0];

                        return (qualityOrder[bQuality as keyof typeof qualityOrder] || 0) - (qualityOrder[aQuality as keyof typeof qualityOrder] || 0);
                    });

                    // Insert the movies after the group entry
                    filteredResults.splice(groupIndex + 1, 0, ...sortedGroupMovies.map(movie => ({
                        ...movie,
                        isGroupItem: true,
                        parentGroupKey: selectedMovieGroup
                    })));
                }
            }
        } else if (activeCategory === 'tvshows') {
            // For TV shows, group by unique TV show (imdbID)
            const tvShows = searchResults.filter(result => result.type === 'tvshow');

            // Create a map of unique TV shows by imdbID
            const uniqueTVShows = new Map();

            // First, look for base TV show entries
            const baseTVShows = tvShows.filter(result => result.isBaseTVShow);

            // Add base TV shows to the map
            baseTVShows.forEach(result => {
                uniqueTVShows.set(result.imdbID, result);
            });

            // If we don't have a base TV show for an imdbID, use the first occurrence
            tvShows.forEach(result => {
                if (!uniqueTVShows.has(result.imdbID)) {
                    // Create a copy of the result without episode info for display in search
                    const tvShowEntry = {
                        ...result,
                        episodeInfo: null // Remove episode info for the search view
                    };
                    uniqueTVShows.set(result.imdbID, tvShowEntry);
                }
            });

            // Convert map values to array
            filteredResults = Array.from(uniqueTVShows.values());
        } else {
            // For 'all' category, we need to deduplicate TV shows
            const movies = searchResults.filter(result => result.type === 'movie');
            const tvShows = searchResults.filter(result => result.type === 'tvshow');

            // Create a map of unique TV shows by imdbID
            const uniqueTVShows = new Map();

            // First, look for base TV show entries
            const baseTVShows = tvShows.filter(result => result.isBaseTVShow);

            // Add base TV shows to the map
            baseTVShows.forEach(result => {
                uniqueTVShows.set(result.imdbID, result);
            });

            // If we don't have a base TV show for an imdbID, use the first occurrence
            tvShows.forEach(result => {
                if (!uniqueTVShows.has(result.imdbID)) {
                    // Create a copy of the result without episode info for display in search
                    const tvShowEntry = {
                        ...result,
                        episodeInfo: null // Remove episode info for the search view
                    };
                    uniqueTVShows.set(result.imdbID, tvShowEntry);
                }
            });

            // Combine movies and unique TV shows
            filteredResults = [...movies, ...Array.from(uniqueTVShows.values())];
        }

        // Calculate pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        visibleResults = filteredResults.slice(startIndex, endIndex);

        // Reset page if we're beyond the available pages
        const totalPages = Math.ceil(filteredResults.length / itemsPerPage);
        if (currentPage > totalPages && totalPages > 0) {
            currentPage = 1;
            updateVisibleResults();
        }
    }

    // These functions are no longer needed with the new navigation flow

    // Function to handle pagination
    function goToPage(page: number) {
        currentPage = page;
        updateVisibleResults();
    }

    // Function to toggle movie group expansion
    function toggleMovieGroup(groupKey: string) {
        if (selectedMovieGroup === groupKey) {
            // If already selected, deselect it
            selectedMovieGroup = null;
            selectedMovieDetails = null;
            expandedMovieGroups.delete(groupKey);
        } else {
            // Otherwise, select it and load details
            selectedMovieGroup = groupKey;
            expandedMovieGroups.add(groupKey);
            loadMovieDetails(groupKey);
        }

        // Force Svelte to recognize the Set has changed
        expandedMovieGroups = new Set(expandedMovieGroups);

        updateVisibleResults();
    }

    // Function to load movie details from OMDB
    async function loadMovieDetails(groupKey: string) {
        try {
            isLoadingMovieDetails = true;

            // Get the first movie in the group
            const movies = groupedMovies.get(groupKey) || [];
            if (movies.length === 0) {
                throw new Error('No movies found in group');
            }

            const movie = movies[0];

            // Extract title and year from the movie
            const title = movie.title;
            const year = movie.year;
            const imdbId = movie.imdbID;

            console.log(`Loading details for movie: ${title} (${year}), IMDB ID: ${imdbId}`);

            // Use the existing poster from the movie
            const poster = movie.poster;

            // Create a basic details object with what we already know
            selectedMovieDetails = {
                title,
                year,
                imdbId,
                poster,
                overview: "Ładowanie opisu...",
                cast: [],
                director: "",
                runtime: "",
                genres: [],
                rating: "",
                traktUrl: `https://trakt.tv/movies/${imdbId}`,
                imdbUrl: `https://www.imdb.com/title/${imdbId}/`
            };

            // Try to get more details from OMDB API
            try {
                // First try direct API call to OMDB
                const omdbApiKey = 'c24805a2'; // OMDB API key
                const omdbDirectUrl = `https://www.omdbapi.com/?apikey=${omdbApiKey}&i=${imdbId}&plot=full`;

                const omdbResponse = await fetch(omdbDirectUrl);

                if (omdbResponse.ok) {
                    const omdbData = await omdbResponse.json();

                    if (omdbData && !omdbData.Error) {
                        // Update details with OMDB data
                        selectedMovieDetails = {
                            ...selectedMovieDetails,
                            overview: omdbData.Plot || "Brak opisu",
                            cast: omdbData.Actors ? omdbData.Actors.split(', ') : [],
                            director: omdbData.Director || "Nieznany",
                            runtime: omdbData.Runtime || "",
                            genres: omdbData.Genre ? omdbData.Genre.split(', ') : [],
                            rating: omdbData.imdbRating || "",
                            poster: omdbData.Poster && omdbData.Poster !== 'N/A' ? omdbData.Poster : selectedMovieDetails.poster
                        };

                        console.log('Updated movie details with OMDB data:', selectedMovieDetails);
                    } else {
                        console.error('OMDB API error:', omdbData.Error);
                        selectedMovieDetails.overview = "Nie udało się załadować opisu.";
                    }
                } else {
                    console.error('OMDB API response not OK:', omdbResponse.status);
                    selectedMovieDetails.overview = "Nie udało się załadować opisu.";
                }
            } catch (error) {
                console.error('Error loading movie details from OMDB:', error);
                selectedMovieDetails.overview = "Błąd podczas ładowania opisu.";
            }
        } catch (error) {
            console.error('Error in loadMovieDetails:', error);
            selectedMovieDetails = null;
        } finally {
            isLoadingMovieDetails = false;
        }
    }

    // Function to handle search input changes with debounce
    function handleSearchInput() {
        clearTimeout(typingTimer);
        if (cinemaSearchQuery.trim().length >= 3) {
            typingTimer = setTimeout(searchMedia, doneTypingInterval);
        } else {
            searchResults = [];
            visibleResults = [];
        }
    }

    // Function to handle key press in search input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && cinemaSearchQuery.trim().length >= 3) {
            clearTimeout(typingTimer);
            searchMedia();
        }
    }

    // Function to search for movies and TV shows
    async function searchMedia() {
        if (cinemaSearchQuery.trim().length < 3) {
            searchResults = [];
            visibleResults = [];

            // Jeśli jesteśmy w sekcji seriali i pole wyszukiwania jest puste, pokaż platformy streamingowe
            if (contentType === 'tvshows') {
                showStreamingPlatforms = true;
            } else {
                showStreamingPlatforms = false;
            }

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wpisz co najmniej 3 znaki', 'error');
            }
            return;
        }

        // Jeśli wyszukujemy, ukryj platformy streamingowe
        showStreamingPlatforms = false;

        isSearching = true;
        searchResults = [];
        visibleResults = [];
        searchError = null;

        try {
            // Add content type to the search URL if not in selection mode
            let url = `/api/cinema/search?query=${encodeURIComponent(cinemaSearchQuery)}`;

            if (contentType === 'movies') {
                url += '&type=movie';
            } else if (contentType === 'tvshows') {
                url += '&type=series';
            }

            console.log(`Searching with URL: ${url}`);

            // Use getWithAuth instead of fetch to include the JWT token
            const response = await getWithAuth(url);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Error ${response.status}`);
            }

            const data = await response.json();
            searchResults = data.results || [];

            // If we're in a specific content type mode, filter the results
            if (contentType === 'movies') {
                searchResults = searchResults.filter(result => result.type === 'movie');
            } else if (contentType === 'tvshows') {
                searchResults = searchResults.filter(result => result.type === 'tvshow');
            }

            // Reset pagination and navigation
            currentPage = 1;
            resetTVShowNavigation();

            // Update visible results
            updateVisibleResults();

            if (searchResults.length === 0) {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(`Nie znaleziono wyników dla "${cinemaSearchQuery}"`, 'error');
                }
            }
        } catch (error) {
            console.error('Błąd wyszukiwania:', error);
            searchError = error instanceof Error ? error.message : 'Nieznany błąd';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Nie udało się wyszukać: ' + searchError, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Function to handle download button click
    function handleDownload(result: any) {
        try {
            // Only switch to the "Add Torrent" tab with the magnet link
            setMagnetAndSwitchTab(result.magnetLink);

            // Show notification
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przekierowano do zakładki "Dodaj torrent"`, 'info');
            }
        } catch (error) {
            console.error('Error handling download:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd: ${error instanceof Error ? error.message : 'Nieznany błąd'}`, 'error');
            }
        }
    }

    // Function to handle VLC stream button click
    async function handleVlcStream(item: any) {
        try {
            // Show notification about stream preparation
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie streamu dla VLC...`, 'info');
            }

            // Get the magnet link
            const magnetLink = item.magnetLink;
            if (!magnetLink) {
                throw new Error('Brak linku magnet');
            }

            // Użyj funkcji addMagnet z debridService zamiast bezpośredniego wywołania API
            console.log(`[CinemaPure] Adding magnet link using debridService`);
            const result = await addMagnet(magnetLink);
            let torrentId = result.id;

            if (!torrentId) {
                throw new Error('Nie udało się dodać torrenta');
            }

            console.log(`Successfully added magnet with torrent ID: ${torrentId}`);

            // Wait for the torrent to be processed
            let torrentInfo;
            let attempts = 0;
            const maxAttempts = 20;
            let lastStatus = '';

            console.log(`Waiting for torrent ${torrentId} to be processed...`);

            // Pokaż powiadomienie o oczekiwaniu
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Dodano do Real-Debrid. Oczekiwanie na przetworzenie...`, 'info');
            }

            while (attempts < maxAttempts) {
                attempts++;

                // Get torrent info using debridService
                torrentInfo = await getTorrentInfo(torrentId);

                // Jeśli status się zmienił, wyświetl go
                if (torrentInfo.status !== lastStatus) {
                    console.log(`Torrent status: ${torrentInfo.status}`);
                    lastStatus = torrentInfo.status;
                }

                // Jeśli torrent oczekuje na wybór plików, wybierz wszystkie pliki
                if (torrentInfo.status === 'waiting_files_selection') {
                    console.log('Torrent is waiting for files selection, selecting all files...');

                    try {
                        // Pobierz listę dostępnych plików
                        if (torrentInfo.files && torrentInfo.files.length > 0) {
                            // Wybierz wszystkie pliki używając funkcji z debridService
                            try {
                                await selectFiles(torrentId);
                                console.log('Successfully selected all files');
                                // Poczekaj chwilę, aby Real-Debrid mógł przetworzyć wybór
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                continue; // Kontynuuj pętlę, aby sprawdzić nowy status
                            } catch (error) {
                                console.error('Failed to select files:', error);
                            }
                        }
                    } catch (selectError) {
                        console.error('Error selecting files:', selectError);
                    }
                }

                // Check if the torrent is ready
                if (torrentInfo.status === 'downloaded' || torrentInfo.status === 'ready') {
                    console.log('Torrent is ready!');
                    break;
                }

                // Jeśli torrent jest w trakcie pobierania, sprawdź czy pliki są już dostępne
                if (torrentInfo.status === 'downloading' && torrentInfo.files && torrentInfo.files.length > 0) {
                    // Sprawdź czy jakiś plik ma już link do pobrania
                    const anyFileReady = torrentInfo.files.some((file: any) => file.selected && file.download);

                    if (anyFileReady) {
                        console.log('Some files are already available while downloading');
                        break;
                    }
                }

                // Wait before checking again
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Jeśli torrent nie jest gotowy, ale mamy informacje o plikach, spróbujmy kontynuować
            if (!torrentInfo || (torrentInfo.status !== 'downloaded' && torrentInfo.status !== 'ready' &&
                !(torrentInfo.status === 'downloading' && torrentInfo.files && torrentInfo.files.length > 0))) {
                throw new Error(`Torrent nie jest gotowy (status: ${torrentInfo ? torrentInfo.status : 'unknown'})`);
            }

            // Get the files
            const files = torrentInfo.files;

            if (!files || files.length === 0) {
                throw new Error('Brak plików w torrencie');
            }

            console.log(`Found ${files.length} files in torrent`);

            // Sprawdź, czy pliki są wybrane
            const selectedFiles = files.filter((file: any) => file.selected);
            console.log(`${selectedFiles.length} files are selected`);

            // Jeśli żaden plik nie jest wybrany, spróbuj jeszcze raz wybrać wszystkie
            if (selectedFiles.length === 0) {
                console.log('No files are selected, trying to select all files again');

                try {
                    // Select all files using the 'all' parameter
                    const selectResponse = await fetchWithAuth(`/proxy/torrents/selectFiles/${torrentId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: `files=all`
                    });

                    if (selectResponse.ok) {
                        console.log('Successfully selected all files in second attempt');

                        // Pobierz ponownie informacje o torrencie
                        const refreshResponse = await fetchWithAuth(`/proxy/torrents/info/${torrentId}`);
                        if (refreshResponse.ok) {
                            const refreshedInfo = await refreshResponse.json();
                            if (refreshedInfo.files && refreshedInfo.files.length > 0) {
                                torrentInfo = refreshedInfo;
                                console.log('Successfully refreshed torrent info');
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error in second attempt to select files:', error);
                }
            }

            // Find the appropriate video file based on context
            let videoFile;

            // If we're in episodes view and have a selected episode, find the matching episode file
            if (viewMode === 'episodes' && selectedEpisode) {
                const season = selectedEpisode.episodeInfo.season;
                const episode = selectedEpisode.episodeInfo.episode;

                console.log(`Looking for episode S${season}E${episode}`);

                // Look for a file that matches the selected episode
                for (const file of files) {
                    if (!file.selected) continue; // Skip unselected files

                    const filename = file.path.toLowerCase();
                    console.log(`Checking file: ${filename}`);

                    // Check if the filename contains the season and episode pattern
                    const s00e00 = `s${season.toString().padStart(2, '0')}e${episode.toString().padStart(2, '0')}`;
                    const s0e0 = `s${season}e${episode}`;
                    const x00 = `${season}x${episode.toString().padStart(2, '0')}`;
                    const seasonEpisode = `season ${season}` && `episode ${episode}`;

                    if (isVideoFile(filename) &&
                        (filename.includes(s00e00) ||
                         filename.includes(s0e0) ||
                         filename.includes(x00) ||
                         filename.includes(seasonEpisode))) {
                        videoFile = file;
                        console.log(`Found matching episode file: ${filename}`);
                        break;
                    }
                }
            }

            // If no specific episode file found or we're not in episodes view, use the largest video file
            if (!videoFile) {
                console.log('No specific episode file found, using largest video file');

                // Filter only selected and video files
                const selectedVideoFiles = files.filter((file: any) =>
                    file.selected && isVideoFile(file.path.toLowerCase())
                );

                if (selectedVideoFiles.length > 0) {
                    // Find the largest video file
                    videoFile = selectedVideoFiles.reduce((largest: any, current: any) =>
                        current.bytes > largest.bytes ? current : largest
                    , selectedVideoFiles[0]);

                    console.log(`Selected largest video file: ${videoFile.path} (${videoFile.bytes} bytes)`);
                } else if (files.length > 0) {
                    // If no selected video files, try any file
                    videoFile = files[0];
                    console.log(`No selected video files, using first available file: ${videoFile.path}`);
                } else {
                    throw new Error('Brak dostępnych plików wideo');
                }
            }

            // Sprawdź czy mamy link do pobrania
            console.log('Selected video file:', videoFile);
            console.log('Torrent info:', torrentInfo);

            if (!videoFile) {
                throw new Error('Nie wybrano pliku wideo');
            }

            // Sprawdź, czy mamy link do pobrania w videoFile
            let downloadLink = '';

            if (videoFile.download) {
                // Standardowa struktura - link jest w pliku
                downloadLink = videoFile.download;
                console.log(`Found download link in videoFile: ${downloadLink}`);
            } else if (torrentInfo.links && torrentInfo.links.length > 0) {
                // Alternatywna struktura - link jest w tablicy links na poziomie głównym
                downloadLink = torrentInfo.links[0];
                console.log(`Found download link in torrentInfo.links: ${downloadLink}`);
            } else {
                console.error('Brak linku do pobrania w pliku ani w torrencie:', videoFile, torrentInfo);
                throw new Error('Brak linku do pobrania dla wybranego pliku');
            }

            console.log(`Getting unrestricted link for: ${videoFile.path}, download link: ${downloadLink}`);

            // Pokaż powiadomienie o przygotowywaniu streamu
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie streamu dla VLC...`, 'info');
            }

            // Get the unrestricted link
            console.log(`Sending request to unrestrict link: ${downloadLink}`);

            let streamUrl = '';

            try {
                // Użyj funkcji unrestrictLink z debridService
                const unrestrictData = await unrestrictLink(downloadLink);
                console.log(`Unrestrict data:`, unrestrictData);

                if (!unrestrictData.download) {
                    console.error(`Missing download property in unrestrict response:`, unrestrictData);
                    throw new Error('Nie udało się pobrać linku do streamowania');
                }

                streamUrl = unrestrictData.download;
                console.log(`Successfully got unrestricted link: ${streamUrl.substring(0, 50)}...`);
            } catch (error) {
                console.error(`Error during unrestrict API call:`, error);
                throw error;
            }
            // Spróbuj znaleźć napisy dla tego pliku
            let subtitleUrl = '';
            if (videoFile && videoFile.path) {
                try {
                    // Pokaż powiadomienie o wyszukiwaniu napisów
                    if (typeof window !== 'undefined' && (window as any).showNotification) {
                        (window as any).showNotification(`Wyszukiwanie napisów...`, 'info');
                    }

                    // Wyszukaj napisy
                    const foundSubtitles = await searchSubtitlesForPlayer(videoFile.path);

                    if (foundSubtitles && subtitlesUrl) {
                        subtitleUrl = subtitlesUrl;
                        // Pokaż powiadomienie o znalezieniu napisów
                        if (typeof window !== 'undefined' && (window as any).showNotification) {
                            (window as any).showNotification(`Znaleziono napisy!`, 'success');
                        }
                    }
                } catch (subtitleError) {
                    console.error('Error searching subtitles for VLC:', subtitleError);
                    // Nie przerywaj odtwarzania, jeśli nie znaleziono napisów
                }
            }

            // Otwórz link w VLC (z napisami, jeśli są dostępne)
            let vlcCallbackUrl = '';
            if (subtitleUrl) {
                // Dla VLC potrzebujemy pełnego URL, więc używamy absolutnego URL do naszego proxy
                const baseUrl = window.location.origin;
                const proxySubtitleUrl = `${baseUrl}/api/subtitles/proxy?url=${encodeURIComponent(subtitleUrl)}`;

                vlcCallbackUrl = `vlc-x-callback://x-callback-url/stream?url=${encodeURIComponent(streamUrl)}&sub=${encodeURIComponent(proxySubtitleUrl)}`;
                console.log(`Opening VLC with video and subtitles: ${vlcCallbackUrl}`);
            } else {
                vlcCallbackUrl = `vlc-x-callback://x-callback-url/stream?url=${encodeURIComponent(streamUrl)}`;
                console.log(`Opening VLC with video only: ${vlcCallbackUrl}`);
            }

            // Otwórz VLC z linkiem do streamowania
            window.open(vlcCallbackUrl, '_blank');

            // Pokaż powiadomienie o sukcesie
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Otwieranie w VLC...`, 'success');
            }

            // Mark as watched
            if (!watchedItems.has(result.infoHash)) {
                watchedItems.add(result.infoHash);
                saveWatchedItems();
            }

        } catch (error) {
            console.error('Error handling VLC stream:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd: ${error instanceof Error ? error.message : 'Nieznany błąd'}`, 'error');
            }
        }
    }

    // Function to play video in browser
    async function handlePlayInBrowser(result: any) {
        try {
            // Pokaż powiadomienie o rozpoczęciu procesu
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie odtwarzacza...`, 'info');
            }

            // Get the magnet link
            const magnetLink = result.magnetLink;

            if (!magnetLink) {
                throw new Error('Brak linku magnet');
            }

            // Użyj funkcji addMagnet z debridService
            console.log(`Adding magnet with instant availability check`);
            const data = await addMagnet(magnetLink);
            let torrentId = data.id;

            if (!torrentId) {
                throw new Error('Nie udało się dodać torrenta');
            }

            console.log(`Successfully added magnet with torrent ID: ${torrentId}`);

            // Wait for the torrent to be processed
            let torrentInfo;
            let attempts = 0;
            const maxAttempts = 20;
            let lastStatus = '';

            console.log(`Waiting for torrent ${torrentId} to be processed...`);

            // Pokaż powiadomienie o oczekiwaniu
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Dodano do Real-Debrid. Oczekiwanie na przetworzenie...`, 'info');
            }

            while (attempts < maxAttempts) {
                attempts++;

                // Get torrent info using debridService
                torrentInfo = await getTorrentInfo(torrentId);

                // Jeśli status się zmienił, wyświetl go
                if (torrentInfo.status !== lastStatus) {
                    console.log(`Torrent status: ${torrentInfo.status}`);
                    lastStatus = torrentInfo.status;
                }

                // Jeśli torrent oczekuje na wybór plików, wybierz wszystkie pliki
                if (torrentInfo.status === 'waiting_files_selection') {
                    console.log('Torrent is waiting for files selection, selecting all files...');

                    try {
                        // Pobierz listę dostępnych plików
                        if (torrentInfo.files && torrentInfo.files.length > 0) {
                            // Wybierz wszystkie pliki używając funkcji z debridService
                            try {
                                await selectFiles(torrentId);
                                console.log('Successfully selected all files');
                                // Poczekaj chwilę, aby Real-Debrid mógł przetworzyć wybór
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                continue; // Kontynuuj pętlę, aby sprawdzić nowy status
                            } catch (error) {
                                console.error('Failed to select files:', error);
                            }
                        }
                    } catch (selectError) {
                        console.error('Error selecting files:', selectError);
                    }
                }

                // Check if the torrent is ready
                if (torrentInfo.status === 'downloaded' || torrentInfo.status === 'ready') {
                    console.log('Torrent is ready!');
                    break;
                }

                // Jeśli torrent jest w trakcie pobierania, sprawdź czy pliki są już dostępne
                if (torrentInfo.status === 'downloading' && torrentInfo.files && torrentInfo.files.length > 0) {
                    // Sprawdź czy jakiś plik ma już link do pobrania
                    const anyFileReady = torrentInfo.files.some((file: any) => file.selected && file.download);

                    if (anyFileReady) {
                        console.log('Some files are already available while downloading');
                        break;
                    }
                }

                // Wait before checking again
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Jeśli torrent nie jest gotowy, ale mamy informacje o plikach, spróbujmy kontynuować
            if (!torrentInfo || (torrentInfo.status !== 'downloaded' && torrentInfo.status !== 'ready' &&
                !(torrentInfo.status === 'downloading' && torrentInfo.files && torrentInfo.files.length > 0))) {
                throw new Error(`Torrent nie jest gotowy (status: ${torrentInfo ? torrentInfo.status : 'unknown'})`);
            }

            // Get the files
            const files = torrentInfo.files;

            if (!files || files.length === 0) {
                throw new Error('Brak plików w torrencie');
            }

            console.log(`Found ${files.length} files in torrent`);

            // Sprawdź, czy pliki są wybrane
            const selectedFiles = files.filter((file: any) => file.selected);
            console.log(`${selectedFiles.length} files are selected`);

            // Jeśli żaden plik nie jest wybrany, spróbuj jeszcze raz wybrać wszystkie
            if (selectedFiles.length === 0) {
                console.log('No files are selected, trying to select all files again');

                try {
                    // Select all files using the 'all' parameter
                    const selectResponse = await fetchWithAuth(`/proxy/torrents/selectFiles/${torrentId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: `files=all`
                    });

                    if (selectResponse.ok) {
                        console.log('Successfully selected all files in second attempt');

                        // Pobierz ponownie informacje o torrencie
                        const refreshResponse = await fetchWithAuth(`/proxy/torrents/info/${torrentId}`);
                        if (refreshResponse.ok) {
                            const refreshedInfo = await refreshResponse.json();
                            if (refreshedInfo.files && refreshedInfo.files.length > 0) {
                                torrentInfo = refreshedInfo;
                                console.log('Successfully refreshed torrent info');
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error in second attempt to select files:', error);
                }
            }

            // Find the appropriate video file based on context
            let videoFile;

            // If we're in episodes view and have a selected episode, find the matching episode file
            if (viewMode === 'episodes' && selectedEpisode) {
                const season = selectedEpisode.episodeInfo.season;
                const episode = selectedEpisode.episodeInfo.episode;

                console.log(`Looking for episode S${season}E${episode}`);

                // Look for a file that matches the selected episode
                for (const file of files) {
                    if (!file.selected) continue; // Skip unselected files

                    const filename = file.path.toLowerCase();
                    console.log(`Checking file: ${filename}`);

                    // Check if the filename contains the season and episode pattern
                    const s00e00 = `s${season.toString().padStart(2, '0')}e${episode.toString().padStart(2, '0')}`;
                    const s0e0 = `s${season}e${episode}`;
                    const x00 = `${season}x${episode.toString().padStart(2, '0')}`;
                    const seasonEpisode = `season ${season}` && `episode ${episode}`;

                    if (isVideoFile(filename) &&
                        (filename.includes(s00e00) ||
                         filename.includes(s0e0) ||
                         filename.includes(x00) ||
                         filename.includes(seasonEpisode))) {
                        videoFile = file;
                        console.log(`Found matching episode file: ${filename}`);
                        break;
                    }
                }
            }

            // If no specific episode file found or we're not in episodes view, use the largest video file
            if (!videoFile) {
                console.log('No specific episode file found, using largest video file');

                // Filter only selected and video files
                const selectedVideoFiles = files.filter((file: any) =>
                    file.selected && isVideoFile(file.path.toLowerCase())
                );

                if (selectedVideoFiles.length > 0) {
                    // Find the largest video file
                    videoFile = selectedVideoFiles.reduce((largest: any, current: any) =>
                        current.bytes > largest.bytes ? current : largest
                    , selectedVideoFiles[0]);

                    console.log(`Selected largest video file: ${videoFile.path} (${videoFile.bytes} bytes)`);
                } else if (files.length > 0) {
                    // If no selected video files, try any file
                    videoFile = files[0];
                    console.log(`No selected video files, using first available file: ${videoFile.path}`);
                } else {
                    throw new Error('Brak dostępnych plików wideo');
                }
            }

            // Sprawdź czy mamy link do pobrania
            console.log('Selected video file:', videoFile);
            console.log('Torrent info:', torrentInfo);

            if (!videoFile) {
                throw new Error('Nie wybrano pliku wideo');
            }

            // Sprawdź, czy mamy link do pobrania w videoFile lub w torrentInfo
            let downloadLink = '';

            if (videoFile.download) {
                // Standardowa struktura - link jest w pliku
                downloadLink = videoFile.download;
                console.log(`Found download link in videoFile: ${downloadLink}`);
            } else if (torrentInfo.links && torrentInfo.links.length > 0) {
                // Alternatywna struktura - link jest w tablicy links na poziomie głównym
                downloadLink = torrentInfo.links[0];
                console.log(`Found download link in torrentInfo.links: ${downloadLink}`);
            } else {
                console.error('Brak linku do pobrania w pliku ani w torrencie:', videoFile, torrentInfo);
                throw new Error('Brak linku do pobrania dla wybranego pliku');
            }

            console.log(`Getting unrestricted link for file: ${videoFile.path}, using download link: ${downloadLink}`);

            // Pokaż powiadomienie o przygotowywaniu odtwarzacza
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie odtwarzacza...`, 'info');
                // lol
            }

            // Get the unrestricted link
            console.log(`Sending request to unrestrict link: ${downloadLink}`);

            let streamUrl = '';

            try {
                // Użyj funkcji unrestrictLink z debridService
                const unrestrictData = await unrestrictLink(downloadLink);
                console.log(`Unrestrict data:`, unrestrictData);

                if (!unrestrictData.download) {
                    console.error(`Missing download property in unrestrict response:`, unrestrictData);
                    throw new Error('Nie udało się pobrać linku do odtwarzania');
                }

                streamUrl = unrestrictData.download;
                console.log(`Successfully got unrestricted link: ${streamUrl.substring(0, 50)}...`);
            } catch (error) {
                console.error(`Error during unrestrict API call:`, error);
                throw error;
            }

            // Set the video URL
            videoUrl = streamUrl;

            // Napisy będą wyszukiwane w tle po uruchomieniu odtwarzacza

            // Pokaż odtwarzacz
            showVideoPlayer = true;

            // Pokaż powiadomienie o sukcesie
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Odtwarzanie rozpoczęte!`, 'success');
            }

            // Wyszukaj napisy dla pliku w tle
            if (videoFile && videoFile.path) {
                // Uruchom wyszukiwanie napisów w tle
                setTimeout(async () => {
                    try {
                        const found = await searchSubtitlesForPlayer(videoFile.path);
                        if (found && subtitlesUrl) {
                            // Aktualizuj ścieżkę napisów w odtwarzaczu
                            updateSubtitlesTrack(subtitlesUrl);
                        }
                    } catch (subtitleError) {
                        console.error('Error searching subtitles in background:', subtitleError);
                    }
                }, 1000);
            }

            // Mark as watched
            if (!watchedItems.has(result.infoHash)) {
                watchedItems.add(result.infoHash);
                saveWatchedItems();
            }

        } catch (error) {
            console.error('Error handling play in browser:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd: ${error instanceof Error ? error.message : 'Nieznany błąd'}`, 'error');
            }
        }
    }

    // Function to handle stream link button click (on desktop)
    async function handleCopyStreamLink(result: any) {
        try {
            // Pokaż powiadomienie o rozpoczęciu procesu
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie linku do streamowania...`, 'info');
            }

            // Get the magnet link
            const magnetLink = result.magnetLink;

            if (!magnetLink) {
                throw new Error('Brak linku magnet');
            }

            // Użyj funkcji addMagnet z debridService
            console.log(`Adding magnet with instant availability check`);
            const data = await addMagnet(magnetLink);
            let torrentId = data.id;

            if (!torrentId) {
                throw new Error('Nie udało się dodać torrenta');
            }

            console.log(`Successfully added magnet with torrent ID: ${torrentId}`);

            // Wait for the torrent to be processed
            let torrentInfo;
            let attempts = 0;
            const maxAttempts = 20;
            let lastStatus = '';

            console.log(`Waiting for torrent ${torrentId} to be processed...`);

            // Pokaż powiadomienie o oczekiwaniu
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Dodano do Real-Debrid. Oczekiwanie na przetworzenie...`, 'info');
            }

            while (attempts < maxAttempts) {
                attempts++;

                // Get torrent info using debridService
                torrentInfo = await getTorrentInfo(torrentId);

                // Jeśli status się zmienił, wyświetl go
                if (torrentInfo.status !== lastStatus) {
                    console.log(`Torrent status: ${torrentInfo.status}`);
                    lastStatus = torrentInfo.status;
                }

                // Jeśli torrent oczekuje na wybór plików, wybierz wszystkie pliki
                if (torrentInfo.status === 'waiting_files_selection') {
                    console.log('Torrent is waiting for files selection, selecting all files...');

                    try {
                        // Pobierz listę dostępnych plików
                        if (torrentInfo.files && torrentInfo.files.length > 0) {
                            // Wybierz wszystkie pliki używając funkcji z debridService
                            try {
                                await selectFiles(torrentId);
                                console.log('Successfully selected all files');
                                // Poczekaj chwilę, aby Real-Debrid mógł przetworzyć wybór
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                continue; // Kontynuuj pętlę, aby sprawdzić nowy status
                            } catch (error) {
                                console.error('Failed to select files:', error);
                            }
                        }
                    } catch (selectError) {
                        console.error('Error selecting files:', selectError);
                    }
                }

                // Check if the torrent is ready
                if (torrentInfo.status === 'downloaded' || torrentInfo.status === 'ready') {
                    console.log('Torrent is ready!');
                    break;
                }

                // Jeśli torrent jest w trakcie pobierania, sprawdź czy pliki są już dostępne
                if (torrentInfo.status === 'downloading' && torrentInfo.files && torrentInfo.files.length > 0) {
                    // Sprawdź czy jakiś plik ma już link do pobrania
                    const anyFileReady = torrentInfo.files.some((file: any) => file.selected && file.download);

                    if (anyFileReady) {
                        console.log('Some files are already available while downloading');
                        break;
                    }
                }

                // Wait before checking again
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Jeśli torrent nie jest gotowy, ale mamy informacje o plikach, spróbujmy kontynuować
            if (!torrentInfo || (torrentInfo.status !== 'downloaded' && torrentInfo.status !== 'ready' &&
                !(torrentInfo.status === 'downloading' && torrentInfo.files && torrentInfo.files.length > 0))) {
                throw new Error(`Torrent nie jest gotowy (status: ${torrentInfo ? torrentInfo.status : 'unknown'})`);
            }

            // Get the files
            const files = torrentInfo.files;

            if (!files || files.length === 0) {
                throw new Error('Brak plików w torrencie');
            }

            console.log(`Found ${files.length} files in torrent`);

            // Sprawdź, czy pliki są wybrane
            const selectedFiles = files.filter((file: any) => file.selected);
            console.log(`${selectedFiles.length} files are selected`);

            // Jeśli żaden plik nie jest wybrany, spróbuj jeszcze raz wybrać wszystkie
            if (selectedFiles.length === 0) {
                console.log('No files are selected, trying to select all files again');

                try {
                    // Select all files using debridService
                    await selectFiles(torrentId);
                    console.log('Successfully selected all files in second attempt');

                    // Pobierz ponownie informacje o torrencie
                    torrentInfo = await getTorrentInfo(torrentId);
                    console.log('Successfully refreshed torrent info');
                } catch (error) {
                    console.error('Error in second attempt to select files:', error);
                }
            }

            // Find the appropriate video file based on context
            let videoFile;

            // If we're in episodes view and have a selected episode, find the matching episode file
            if (viewMode === 'episodes' && selectedEpisode) {
                const season = selectedEpisode.episodeInfo.season;
                const episode = selectedEpisode.episodeInfo.episode;

                console.log(`Looking for episode S${season}E${episode}`);

                // Look for a file that matches the selected episode
                for (const file of files) {
                    if (!file.selected) continue; // Skip unselected files

                    const filename = file.path.toLowerCase();
                    console.log(`Checking file: ${filename}`);

                    // Check if the filename contains the season and episode pattern
                    const s00e00 = `s${season.toString().padStart(2, '0')}e${episode.toString().padStart(2, '0')}`;
                    const s0e0 = `s${season}e${episode}`;
                    const x00 = `${season}x${episode.toString().padStart(2, '0')}`;
                    const seasonEpisode = `season ${season}` && `episode ${episode}`;

                    if (isVideoFile(filename) &&
                        (filename.includes(s00e00) ||
                         filename.includes(s0e0) ||
                         filename.includes(x00) ||
                         filename.includes(seasonEpisode))) {
                        videoFile = file;
                        console.log(`Found matching episode file: ${filename}`);
                        break;
                    }
                }
            }

            // If no specific episode file found or we're not in episodes view, use the largest video file
            if (!videoFile) {
                console.log('No specific episode file found, using largest video file');

                // Filter only selected and video files
                const selectedVideoFiles = files.filter((file: any) =>
                    file.selected && isVideoFile(file.path.toLowerCase())
                );

                if (selectedVideoFiles.length > 0) {
                    // Find the largest video file
                    videoFile = selectedVideoFiles.reduce((largest: any, current: any) =>
                        current.bytes > largest.bytes ? current : largest
                    , selectedVideoFiles[0]);

                    console.log(`Selected largest video file: ${videoFile.path} (${videoFile.bytes} bytes)`);
                } else if (files.length > 0) {
                    // If no selected video files, try any file
                    videoFile = files[0];
                    console.log(`No selected video files, using first available file: ${videoFile.path}`);
                } else {
                    throw new Error('Brak dostępnych plików wideo');
                }
            }

            // Sprawdź czy mamy link do pobrania
            console.log('Selected video file:', videoFile);
            console.log('Torrent info:', torrentInfo);

            if (!videoFile) {
                throw new Error('Nie wybrano pliku wideo');
            }

            // Sprawdź, czy mamy link do pobrania w videoFile lub w torrentInfo
            let downloadLink = '';

            if (videoFile.download) {
                // Standardowa struktura - link jest w pliku
                downloadLink = videoFile.download;
                console.log(`Found download link in videoFile: ${downloadLink}`);
            } else if (torrentInfo.links && torrentInfo.links.length > 0) {
                // Alternatywna struktura - link jest w tablicy links na poziomie głównym
                downloadLink = torrentInfo.links[0];
                console.log(`Found download link in torrentInfo.links: ${downloadLink}`);
            } else {
                console.error('Brak linku do pobrania w pliku ani w torrencie:', videoFile, torrentInfo);
                throw new Error('Brak linku do pobrania dla wybranego pliku');
            }

            console.log(`Getting unrestricted link for file: ${videoFile.path}, using download link: ${downloadLink}`);

            // Pokaż powiadomienie o przygotowywaniu linku
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie linku do streamowania...`, 'info');
            }

            // Get the unrestricted link
            console.log(`Sending request to unrestrict link: ${downloadLink}`);

            let streamUrl = '';

            try {
                // Użyj funkcji unrestrictLink z debridService
                const unrestrictData = await unrestrictLink(downloadLink);
                console.log(`Unrestrict data:`, unrestrictData);

                if (!unrestrictData.download) {
                    console.error(`Missing download property in unrestrict response:`, unrestrictData);
                    throw new Error('Nie udało się pobrać linku do streamowania');
                }

                streamUrl = unrestrictData.download;
                console.log(`Successfully got unrestricted link: ${streamUrl.substring(0, 50)}...`);
            } catch (error) {
                console.error(`Error during unrestrict API call:`, error);
                throw error;
            }

            // Funkcja pomocnicza do kopiowania przez fallback
            const copyTextToClipboardFallback = (text: string) => {
                const textArea = document.createElement('textarea');
                textArea.value = text;

                // Ukryj element, ale upewnij się, że jest w DOM
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);

                // Zaznacz i skopiuj tekst
                textArea.focus();
                textArea.select();

                let success = false;
                try {
                    success = document.execCommand('copy');
                } catch (err) {
                    console.error('Fallback: Nie udało się skopiować tekstu', err);
                }

                // Usuń element
                document.body.removeChild(textArea);
                return success;
            };

            let copySuccess = false;

            // Najpierw spróbuj użyć nowoczesnego API Clipboard
            if (navigator && navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
                try {
                    await navigator.clipboard.writeText(streamUrl);
                    copySuccess = true;
                } catch (clipboardError) {
                    console.warn('Clipboard API nie zadziałało, próbuję fallback', clipboardError);
                }
            } else {
                console.warn('Clipboard API niedostępne, używam fallbacku');
            }

            // Jeśli nowoczesne API nie zadziałało, użyj fallbacku
            if (!copySuccess) {
                copySuccess = copyTextToClipboardFallback(streamUrl);
            }

            // Pokaż odpowiednie powiadomienie
            if (typeof window !== 'undefined') {
                if (copySuccess) {
                    (window as any).showNotification(`Link do streamowania skopiowany do schowka`, 'success');
                } else {
                    (window as any).showAlert(`Nie udało się skopiować linku do schowka. Link: ${streamUrl}`, 'warning');
                }
            }

            // Mark as watched
            if (!watchedItems.has(result.infoHash)) {
                watchedItems.add(result.infoHash);
                saveWatchedItems();
            }

        } catch (error) {
            console.error('Error handling copy stream link:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd: ${error instanceof Error ? error.message : 'Nieznany błąd'}`, 'error');
            }
        }
    }

    // Function to copy magnet link to clipboard
    function copyMagnetLink(result: any) {
        if (!result || !result.magnetLink) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Brak linku magnet do skopiowania', 'error');
            }
            return;
        }

        // Funkcja pomocnicza do kopiowania przez fallback
        const copyTextToClipboardFallback = (text: string) => {
            const textArea = document.createElement('textarea');
            textArea.value = text;

            // Ukryj element, ale upewnij się, że jest w DOM
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            // Zaznacz i skopiuj tekst
            textArea.focus();
            textArea.select();

            let success = false;
            try {
                success = document.execCommand('copy');
            } catch (err) {
                console.error('Fallback: Nie udało się skopiować tekstu', err);
            }

            // Usuń element
            document.body.removeChild(textArea);
            return success;
        };

        let copySuccess = false;

        // Najpierw spróbuj użyć nowoczesnego API Clipboard
        if (navigator && navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
            try {
                navigator.clipboard.writeText(result.magnetLink);
                copySuccess = true;
            } catch (clipboardError) {
                console.warn('Clipboard API nie zadziałało, próbuję fallback', clipboardError);
            }
        } else {
            console.warn('Clipboard API niedostępne, używam fallbacku');
        }

        // Jeśli nowoczesne API nie zadziałało, użyj fallbacku
        if (!copySuccess) {
            copySuccess = copyTextToClipboardFallback(result.magnetLink);
        }

        // Pokaż odpowiednie powiadomienie
        if (typeof window !== 'undefined') {
            if (copySuccess) {
                (window as any).showNotification('Link magnet skopiowany do schowka', 'success');
            } else {
                (window as any).showAlert(`Nie udało się skopiować linku do schowka. Link: ${result.magnetLink}`, 'warning');
            }
        }
    }

    // Function to handle watch button click
    async function handleWatch(result: any) {
        try {
            // Zatrzymaj animację modalu
            document.body.style.overflow = 'hidden';
            isLoadingVideo = true;

            // Show notification
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie odtwarzacza...`, 'info');
            }

            // Get the magnet link
            const magnetLink = result.magnetLink;

            if (!magnetLink) {
                throw new Error('Brak linku magnet');
            }

            // Call the Real-Debrid API to get the streaming URL
            const response = await fetch('/proxy/torrents/addMagnet', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `magnet=${encodeURIComponent(magnetLink)}`
            });

            if (!response.ok) {
                throw new Error(`Błąd dodawania magnet: ${response.status}`);
            }

            const data = await response.json();
            let torrentId = data.id;

            if (!torrentId) {
                throw new Error('Nie udało się dodać torrenta');
            }

            // Zamiast próbować wybierać pliki, dodajmy nowy torrent z opcją automatycznego wyboru
            try {
                console.log(`Trying to add magnet with instant availability check`);

                // Dodaj magnet z opcją instant_availability
                const instantResponse = await fetch('/proxy/torrents/addMagnet', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `magnet=${encodeURIComponent(magnetLink)}&instant_availability=1`
                });

                if (!instantResponse.ok) {
                    console.warn(`Could not add magnet with instant availability: ${instantResponse.status}`);
                } else {
                    const instantData = await instantResponse.json();
                    console.log('Instant availability response:', instantData);

                    // Jeśli mamy instant_availability, użyjmy go
                    if (instantData.id) {
                        torrentId = instantData.id;
                        console.log(`Using new torrent ID with instant availability: ${torrentId}`);
                    }
                }
            } catch (selectError) {
                console.error('Error with instant availability:', selectError);
                // Continue anyway with the original torrentId
            }

            // Wait for the torrent to be processed
            let torrentInfo;
            let attempts = 0;
            const maxAttempts = 20; // Zwiększamy liczbę prób
            let lastStatus = '';

            console.log(`Waiting for torrent ${torrentId} to be processed...`);

            // Pokaż powiadomienie o oczekiwaniu
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Dodano do Real-Debrid. Oczekiwanie na przetworzenie...`, 'info');
            }

            while (attempts < maxAttempts) {
                attempts++;

                // Get torrent info
                const infoResponse = await fetch(`/proxy/torrents/info/${torrentId}`);

                if (!infoResponse.ok) {
                    throw new Error(`Błąd pobierania informacji o torrencie: ${infoResponse.status}`);
                }

                torrentInfo = await infoResponse.json();

                // Jeśli status się zmienił, wyświetl go
                if (torrentInfo.status !== lastStatus) {
                    console.log(`Torrent status: ${torrentInfo.status}`);
                    lastStatus = torrentInfo.status;
                }

                // Check if the torrent is ready - akceptujemy różne statusy
                if (torrentInfo.status === 'downloaded' || torrentInfo.status === 'ready') {
                    console.log('Torrent is ready!');
                    break;
                }

                // Jeśli torrent jest w trakcie pobierania, sprawdź czy pliki są już dostępne
                if (torrentInfo.status === 'downloading' && torrentInfo.files && torrentInfo.files.length > 0) {
                    // Sprawdź czy jakiś plik ma już link do pobrania
                    const anyFileReady = torrentInfo.files.some((file: any) => file.selected && file.download);

                    if (anyFileReady) {
                        console.log('Some files are already available while downloading');
                        break;
                    }
                }

                // Wait before checking again
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Jeśli torrent nie jest gotowy, ale mamy informacje o plikach, spróbujmy kontynuować
            if (!torrentInfo || (torrentInfo.status !== 'downloaded' && torrentInfo.status !== 'ready' &&
                !(torrentInfo.status === 'downloading' && torrentInfo.files && torrentInfo.files.length > 0))) {
                throw new Error(`Torrent nie jest gotowy (status: ${torrentInfo ? torrentInfo.status : 'unknown'})`);
            }

            // Get the files
            const files = torrentInfo.files;

            if (!files || files.length === 0) {
                throw new Error('Brak plików w torrencie');
            }

            console.log(`Found ${files.length} files in torrent`);

            // Sprawdź, czy pliki są wybrane
            const selectedFiles = files.filter((file: any) => file.selected);
            console.log(`${selectedFiles.length} files are selected`);

            // Jeśli żaden plik nie jest wybrany, spróbuj jeszcze raz wybrać wszystkie
            if (selectedFiles.length === 0) {
                console.log('No files are selected, trying to select all files again');

                try {
                    // Create a comma-separated list of all file IDs
                    const fileIds = files.map((_: any, index: number) => index).join(',');

                    // Select the files using the correct API format
                    const selectResponse = await fetch(`/proxy/torrents/selectFiles/${torrentId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        body: `files=${fileIds}`
                    });

                    if (!selectResponse.ok) {
                        console.warn(`Could not select files in second attempt: ${selectResponse.status}`);
                    } else {
                        console.log('Successfully selected all files in second attempt');

                        // Pobierz ponownie informacje o torrencie
                        const refreshResponse = await fetch(`/proxy/torrents/info/${torrentId}`);
                        if (refreshResponse.ok) {
                            const refreshedInfo = await refreshResponse.json();
                            if (refreshedInfo.files && refreshedInfo.files.length > 0) {
                                torrentInfo = refreshedInfo;
                                console.log('Successfully refreshed torrent info');
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error in second attempt to select files:', error);
                }
            }

            // Find the appropriate video file based on context
            let videoFile;

            // If we're in episodes view and have a selected episode, find the matching episode file
            if (viewMode === 'episodes' && selectedEpisode) {
                const season = selectedEpisode.episodeInfo.season;
                const episode = selectedEpisode.episodeInfo.episode;

                console.log(`Looking for episode S${season}E${episode}`);

                // Look for a file that matches the selected episode
                for (const file of files) {
                    if (!file.selected) continue; // Skip unselected files

                    const filename = file.path.toLowerCase();
                    console.log(`Checking file: ${filename}`);

                    // Check if the filename contains the season and episode pattern
                    const s00e00 = `s${season.toString().padStart(2, '0')}e${episode.toString().padStart(2, '0')}`;
                    const s0e0 = `s${season}e${episode}`;
                    const x00 = `${season}x${episode.toString().padStart(2, '0')}`;
                    const seasonEpisode = `season ${season}` && `episode ${episode}`;

                    if (isVideoFile(filename) &&
                        (filename.includes(s00e00) ||
                         filename.includes(s0e0) ||
                         filename.includes(x00) ||
                         filename.includes(seasonEpisode))) {
                        videoFile = file;
                        console.log(`Found matching episode file: ${filename}`);
                        break;
                    }
                }
            }

            // If no specific episode file found or we're not in episodes view, use the largest video file
            if (!videoFile) {
                console.log('No specific episode file found, using largest video file');

                // Filter only selected and video files
                const selectedVideoFiles = files.filter((file: any) =>
                    file.selected && isVideoFile(file.path.toLowerCase())
                );

                if (selectedVideoFiles.length > 0) {
                    // Find the largest video file
                    videoFile = selectedVideoFiles.reduce((largest: any, current: any) =>
                        current.bytes > largest.bytes ? current : largest
                    , selectedVideoFiles[0]);

                    console.log(`Selected largest video file: ${videoFile.path} (${videoFile.bytes} bytes)`);
                } else if (files.length > 0) {
                    // If no selected video files, try any file
                    videoFile = files[0];
                    console.log(`No selected video files, using first available file: ${videoFile.path}`);
                } else {
                    throw new Error('Brak dostępnych plików wideo');
                }
            }

            // Sprawdź czy mamy link do pobrania
            console.log('Selected video file:', videoFile);
            console.log('Torrent info:', torrentInfo);

            if (!videoFile) {
                throw new Error('Nie wybrano pliku wideo');
            }

            // Sprawdź, czy mamy link do pobrania w videoFile lub w torrentInfo
            let downloadLink = '';

            if (videoFile.download) {
                // Standardowa struktura - link jest w pliku
                downloadLink = videoFile.download;
                console.log(`Found download link in videoFile: ${downloadLink}`);
            } else if (torrentInfo.links && torrentInfo.links.length > 0) {
                // Alternatywna struktura - link jest w tablicy links na poziomie głównym
                downloadLink = torrentInfo.links[0];
                console.log(`Found download link in torrentInfo.links: ${downloadLink}`);
            } else {
                console.error('Brak linku do pobrania w pliku ani w torrencie:', videoFile, torrentInfo);
                throw new Error('Brak linku do pobrania dla wybranego pliku');
            }

            console.log(`Getting unrestricted link for file: ${videoFile.path}, using download link: ${downloadLink}`);

            // Pokaż powiadomienie o przygotowywaniu odtwarzacza
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Przygotowywanie odtwarzacza...`, 'info');
            }

            // Get the unrestricted link
            const unrestrictResponse = await fetch('/proxy/unrestrict/link', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `link=${encodeURIComponent(videoFile.download)}`
            });

            if (!unrestrictResponse.ok) {
                throw new Error(`Błąd pobierania linku: ${unrestrictResponse.status}`);
            }

            const unrestrictData = await unrestrictResponse.json();

            if (!unrestrictData.download) {
                throw new Error('Nie udało się pobrać linku do odtwarzania');
            }

            console.log(`Successfully got unrestricted link: ${unrestrictData.download.substring(0, 50)}...`);

            // Set the video URL and show the player
            videoUrl = unrestrictData.download;
            showVideoPlayer = true;

            // Pokaż powiadomienie o sukcesie
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Odtwarzanie rozpoczęte!`, 'success');
            }

            // Mark as watched
            if (!watchedItems.has(result.infoHash)) {
                watchedItems.add(result.infoHash);
                saveWatchedItems();
            }

        } catch (error) {
            console.error('Error handling watch:', error);

            // Show error notification
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd: ${error instanceof Error ? error.message : 'Nieznany błąd'}`, 'error');
            }
        } finally {
            isLoadingVideo = false;
            // Przywróć normalny stan
            document.body.style.overflow = '';
        }
    }

    // Function to check if a file is a video file
    function isVideoFile(filename: string): boolean {
        const videoExtensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg'];
        const lowerFilename = filename.toLowerCase();
        return videoExtensions.some(ext => lowerFilename.endsWith(ext));
    }

    // Function to close the video player
    function closeVideoPlayer() {
        showVideoPlayer = false;
        videoUrl = '';
        subtitlesUrl = '';
    }

    // Funkcja do włączania/wyłączania napisów
    function toggleSubtitles(enable: boolean): void {
        if (typeof window !== 'undefined') {
            const video = document.getElementById('videoPlayer') as HTMLVideoElement;
            if (video && video.textTracks && video.textTracks.length > 0) {
                for (let i = 0; i < video.textTracks.length; i++) {
                    video.textTracks[i].mode = enable ? 'showing' : 'hidden';
                }

                // Pokaż powiadomienie
                if (typeof window !== 'undefined' && (window as any).showNotification) {
                    (window as any).showNotification(
                        enable ? 'Napisy włączone' : 'Napisy wyłączone',
                        enable ? 'success' : 'info'
                    );
                }
            }
        }
    }

    // Funkcja do aktualizacji ścieżki napisów w odtwarzaczu
    function updateSubtitlesTrack(url: string): void {
        if (typeof window !== 'undefined') {
            const video = document.getElementById('videoPlayer') as HTMLVideoElement;
            if (video) {
                // Funkcja do dodawania nowej ścieżki napisów
                const addTrackElement = (trackSrc: string) => {
                    // Usuń istniejące ścieżki napisów
                    while (video.textTracks.length > 0) {
                        const track = video.querySelector('track');
                        if (track) {
                            video.removeChild(track);
                        } else {
                            break;
                        }
                    }

                    if (trackSrc) {
                        // Dodaj nową ścieżkę napisów
                        const track = document.createElement('track');
                        track.kind = 'captions';
                        track.label = 'Polski';
                        track.srclang = 'pl';
                        track.src = trackSrc;
                        track.default = true;

                        video.appendChild(track);

                        // Włącz napisy
                        setTimeout(() => {
                            if (video.textTracks && video.textTracks.length > 0) {
                                video.textTracks[0].mode = 'showing';
                            }
                        }, 500);

                        // Pokaż powiadomienie
                        if (typeof window !== 'undefined' && (window as any).showNotification) {
                            (window as any).showNotification('Napisy zostały załadowane', 'success');
                        }
                    }
                };

                // Sprawdź, czy URL zaczyna się od /api/subtitles/proxy
                // Jeśli tak, przekieruj do publicznego endpointu
                if (url.startsWith('/api/subtitles/proxy')) {
                    console.log('Converting proxy URL to public endpoint');

                    // Pobierz oryginalny URL z parametru zapytania
                    const originalUrl = new URL(url, window.location.origin).searchParams.get('url');
                    if (originalUrl) {
                        // Użyj publicznego endpointu do pobrania napisów
                        const downloadUrl = `/api/subtitles/download?url=${encodeURIComponent(originalUrl)}`;

                        // Pobierz napisy asynchronicznie
                        (async () => {
                            try {
                                const response = await fetch(downloadUrl);
                                if (response.ok) {
                                    const data = await response.json();
                                    if (data && data.url) {
                                        // Pobierz nazwę pliku z URL
                                        const fileName = data.url.split('/').pop();

                                        // Ustaw URL do publicznego endpointu
                                        const publicUrl = `/publicapi/subtitles/${fileName}`;
                                        console.log(`Switching to public subtitle endpoint: ${publicUrl}`);

                                        // Aktualizuj ścieżkę napisów
                                        addTrackElement(publicUrl);
                                    }
                                }
                            } catch (error) {
                                console.error('Error fetching subtitles for track:', error);
                            }
                        })();
                    }
                } else if (url) {
                    // Użyj bezpośrednio podanego URL
                    addTrackElement(url);
                }
            }
        }
    }

    // Function to search for subtitles using OpenSubtitles API
    async function searchSubtitlesWithOpenSubtitles(filename: string, imdbId?: string): Promise<boolean> {
        try {
            // Wyczyść rozszerzenie pliku i usuń ukośnik na początku
            let filenameWithoutExt = filename.replace(/\.(mp4|mkv|avi)$/i, '');

            // Usuń ukośnik na początku, jeśli istnieje
            if (filenameWithoutExt.startsWith('/')) {
                filenameWithoutExt = filenameWithoutExt.substring(1);
            }

            // Przygotuj zapytanie do API napisów
            const query = encodeURIComponent(filenameWithoutExt);

            // Przygotuj URL zapytania
            let apiUrl = `/api/subtitles/opensubtitles?query=${query}&language=pl`;

            // Dodaj IMDB ID jeśli dostępne
            if (imdbId) {
                apiUrl += `&imdbid=${imdbId}`;
            }

            console.log(`Searching subtitles with OpenSubtitles API, filename: ${filename}, imdbId: ${imdbId || 'none'}`);

            // Wykonaj zapytanie do API OpenSubtitles
            const response = await getWithAuth(apiUrl);

            if (!response.ok) {
                console.error(`OpenSubtitles API error: ${response.status}`);
                return false;
            }

            const data = await response.json();

            if (data && data.subtitles && data.subtitles.length > 0) {
                // Pobierz pierwszy wynik
                const subtitle = data.subtitles[0];

                console.log(`Found subtitle with OpenSubtitles API: ${subtitle.filename}, file_id: ${subtitle.file_id}`);

                // Pobierz napisy
                const downloadResponse = await getWithAuth(`/api/subtitles/opensubtitles/download?file_id=${subtitle.file_id}`);

                if (!downloadResponse.ok) {
                    console.error(`Failed to download subtitle from OpenSubtitles: ${downloadResponse.status}`);
                    return false;
                }

                const downloadData = await downloadResponse.json();

                if (downloadData && downloadData.url) {
                    // Pobierz nazwę pliku z URL
                    const fileName = downloadData.url.split('/').pop();

                    // Ustaw URL do publicznego endpointu
                    subtitlesUrl = `/publicapi/subtitles/${fileName}`;
                    console.log(`Using public subtitle endpoint from OpenSubtitles: ${subtitlesUrl}`);
                    return true;
                }
            }

            console.log('No subtitles found with OpenSubtitles API');
            return false;
        } catch (error) {
            console.error('Error searching subtitles with OpenSubtitles API:', error);
            return false;
        }
    }

    // Function to search for subtitles for player
    async function searchSubtitlesForPlayer(filename: string, retryCount: number = 0): Promise<boolean> {
        try {
            // Wyczyść rozszerzenie pliku i usuń ukośnik na początku
            let filenameWithoutExt = filename.replace(/\.(mp4|mkv|avi)$/i, '');

            // Usuń ukośnik na początku, jeśli istnieje
            if (filenameWithoutExt.startsWith('/')) {
                filenameWithoutExt = filenameWithoutExt.substring(1);
            }

            // Przygotuj zapytanie do API napisów
            const query = encodeURIComponent(filenameWithoutExt);

            console.log(`Searching subtitles for player, filename: ${filename}, attempt: ${retryCount + 1}`);

            // Najpierw spróbuj użyć OpenSubtitles API
            try {
                console.log('Trying OpenSubtitles API first...');
                const openSubtitlesResult = await searchSubtitlesWithOpenSubtitles(filename);

                if (openSubtitlesResult) {
                    console.log('Successfully found subtitles with OpenSubtitles API');
                    return true;
                }

                console.log('OpenSubtitles API did not find subtitles, falling back to Python/subliminal');
            } catch (openSubtitlesError) {
                console.error('Error with OpenSubtitles API, falling back to Python/subliminal:', openSubtitlesError);
            }

            // Wyszukaj napisy przez API z autoryzacją (używając bezpośrednio Pythona i subliminal)
            try {
                const response = await getWithAuth(`/api/subtitles/search-python?query=${query}&lang=pl`);

                if (response.ok) {
                    const data = await response.json();

                    if (data && data.subtitles && data.subtitles.length > 0) {
                        // Pobierz pierwszy wynik
                        const subtitle = data.subtitles[0];

                        // Wybierz najlepszy dostępny link i dodaj domenę jeśli potrzeba
                        let subtitleUrl = '';

                        if (subtitle.download_link) {
                            subtitleUrl = subtitle.download_link;
                        } else if (subtitle.page_link) {
                            // Sprawdź, czy link zawiera pełny URL
                            if (subtitle.provider === 'podnapisi') {
                                if (!subtitle.page_link.startsWith('http')) {
                                    // Dodaj domenę dla Podnapisi i /download na końcu
                                    subtitleUrl = 'https://www.podnapisi.net' + subtitle.page_link + '/download';
                                } else {
                                    // Jeśli już ma pełny URL, dodaj tylko /download
                                    subtitleUrl = subtitle.page_link + '/download';
                                }
                            } else {
                                subtitleUrl = subtitle.page_link;
                            }
                        }

                        if (subtitleUrl) {
                            console.log(`Found subtitle for player: ${subtitle.provider}, URL: ${subtitleUrl}`);

                            try {
                                // Funkcja do pobierania z ponownymi próbami
                                const fetchWithRetry = async (url: string, maxRetries = 3, delay = 1000) => {
                                    let lastError;

                                    for (let attempt = 1; attempt <= maxRetries; attempt++) {
                                        try {
                                            console.log(`Subtitle download attempt ${attempt}/${maxRetries}`);
                                            const response = await getWithAuth(url);

                                            if (response.ok) {
                                                return response;
                                            }

                                            lastError = new Error(`HTTP error: ${response.status}`);
                                            console.error(`Subtitle download attempt ${attempt} failed: ${response.status}`);

                                            // Jeśli to nie jest ostatnia próba, poczekaj przed kolejną
                                            if (attempt < maxRetries) {
                                                await new Promise(resolve => setTimeout(resolve, delay * attempt));
                                            }
                                        } catch (error) {
                                            lastError = error;
                                            console.error(`Subtitle download attempt ${attempt} failed with error:`, error);

                                            // Jeśli to nie jest ostatnia próba, poczekaj przed kolejną
                                            if (attempt < maxRetries) {
                                                await new Promise(resolve => setTimeout(resolve, delay * attempt));
                                            }
                                        }
                                    }

                                    throw lastError;
                                };

                                // Pobierz napisy na serwer i zapisz lokalnie z ponownymi próbami
                                let downloadResponse;
                                try {
                                    downloadResponse = await fetchWithRetry(`/api/subtitles/download?url=${encodeURIComponent(subtitleUrl)}`);
                                } catch (retryError) {
                                    console.error('All subtitle download attempts failed:', retryError);
                                    // Fallback do starego sposobu przez proxy
                                    subtitlesUrl = `/api/subtitles/proxy?url=${encodeURIComponent(subtitleUrl)}`;
                                    console.log(`Fallback to proxy URL for subtitles after retry failures: ${subtitlesUrl}`);
                                    return true;
                                }

                                if (downloadResponse.ok) {
                                    const downloadData = await downloadResponse.json();

                                    if (downloadData && downloadData.url) {
                                        // Pobierz nazwę pliku z URL
                                        const fileName = downloadData.url.split('/').pop();

                                        // Ustaw URL do publicznego endpointu
                                        subtitlesUrl = `/publicapi/subtitles/${fileName}`;
                                        console.log(`Using public subtitle endpoint: ${subtitlesUrl}`);
                                        return true;
                                    }
                                } else {
                                    console.error(`Failed to download subtitles: ${downloadResponse.status}`);
                                    // Fallback do starego sposobu przez proxy
                                    subtitlesUrl = `/api/subtitles/proxy?url=${encodeURIComponent(subtitleUrl)}`;
                                    console.log(`Fallback to proxy URL for subtitles: ${subtitlesUrl}`);
                                    return true;
                                }
                            } catch (downloadError) {
                                console.error('Error downloading subtitles:', downloadError);
                                // Fallback do starego sposobu przez proxy
                                subtitlesUrl = `/api/subtitles/proxy?url=${encodeURIComponent(subtitleUrl)}`;
                                console.log(`Fallback to proxy URL for subtitles: ${subtitlesUrl}`);
                                return true;
                            }
                        }
                    }
                }

                // Jeśli nie znaleziono napisów i to pierwsza próba, spróbuj ponownie
                if (retryCount < 1) {
                    console.log('No subtitles found on first attempt, retrying...');

                    // Poczekaj chwilę przed ponowną próbą
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    // Spróbuj ponownie z lekko zmodyfikowanym zapytaniem
                    // Usuń niektóre znaki specjalne i spróbuj ponownie
                    const cleanedFilename = filename
                        .replace(/\.(mp4|mkv|avi)$/i, '')
                        .replace(/[._-]/g, ' ')
                        .replace(/\s+/g, ' ')
                        .trim();

                    return searchSubtitlesForPlayer(cleanedFilename, retryCount + 1);
                }

                return false;
            } catch (error) {
                console.error('Error searching subtitles for player:', error);

                // Jeśli to pierwsza próba, spróbuj ponownie
                if (retryCount < 1) {
                    console.log('Error occurred, retrying subtitle search');

                    // Poczekaj chwilę przed ponowną próbą
                    await new Promise(resolve => setTimeout(resolve, 1500));

                    return searchSubtitlesForPlayer(filename, retryCount + 1);
                }

                return false;
            }
        } catch (error) {
            console.error('Error in searchSubtitlesForPlayer:', error);

            // Jeśli to pierwsza próba, spróbuj ponownie
            if (retryCount < 1) {
                console.log('Error occurred, retrying subtitle search');

                // Poczekaj chwilę przed ponowną próbą
                await new Promise(resolve => setTimeout(resolve, 1500));

                return searchSubtitlesForPlayer(filename, retryCount + 1);
            }

            return false;
        }
    }

    // Function to copy text to clipboard
    async function copyToClipboard(text: string): Promise<boolean> {
        // Najpierw spróbuj użyć nowoczesnego API Clipboard
        if (navigator && navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
            try {
                await navigator.clipboard.writeText(text);
                return true;
            } catch (clipboardError) {
                console.warn('Clipboard API nie zadziałało, próbuję fallback', clipboardError);
            }
        } else {
            console.warn('Clipboard API niedostępne, używam fallbacku');
        }

        // Fallback dla starszych przeglądarek
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // Ukryj element, ale upewnij się, że jest w DOM
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        // Zaznacz i skopiuj tekst
        textArea.focus();
        textArea.select();

        let success = false;
        try {
            // Używamy document.execCommand mimo że jest przestarzałe, jako fallback
            success = document.execCommand('copy');
        } catch (err) {
            console.error('Fallback: Nie udało się skopiować tekstu', err);
        }

        // Usuń element
        document.body.removeChild(textArea);
        return success;
    }

    // Function to search for subtitles
    async function searchSubtitles(filename: string) {
        try {
            isSearchingSubtitles = true;

            // Pokaż powiadomienie
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Wyszukiwanie napisów...`, 'info');
            }

            console.log(`Searching subtitles for: ${filename}`);

            // Wyczyść rozszerzenie pliku i usuń ukośnik na początku
            let filenameWithoutExt = filename.replace(/\.(mp4|mkv|avi)$/i, '');

            // Usuń ukośnik na początku, jeśli istnieje
            if (filenameWithoutExt.startsWith('/')) {
                filenameWithoutExt = filenameWithoutExt.substring(1);
            }

            // Przygotuj zapytanie do API napisów
            const query = encodeURIComponent(filenameWithoutExt);

            // Najpierw spróbuj użyć OpenSubtitles API
            try {
                console.log('Trying OpenSubtitles API for subtitle search...');
                const openSubtitlesResponse = await getWithAuth(`/api/subtitles/opensubtitles?query=${query}&language=pl`);

                if (openSubtitlesResponse.ok) {
                    const openSubtitlesData = await openSubtitlesResponse.json();

                    if (openSubtitlesData && openSubtitlesData.subtitles && openSubtitlesData.subtitles.length > 0) {
                        console.log(`Found ${openSubtitlesData.subtitles.length} subtitles with OpenSubtitles API`);

                        // Pobierz pierwszy wynik
                        const subtitle = openSubtitlesData.subtitles[0];

                        // Ustaw URL do pobrania
                        subtitlesUrl = `/api/subtitles/opensubtitles/download?file_id=${subtitle.file_id}`;

                        console.log(`Using OpenSubtitles API download URL: ${subtitlesUrl}`);

                        // Skopiuj link do schowka
                        const copySuccess = await copyToClipboard(subtitlesUrl);

                        // Pokaż odpowiednie powiadomienie
                        if (typeof window !== 'undefined') {
                            if (copySuccess) {
                                (window as any).showNotification(`Link do napisów skopiowany do schowka! (OpenSubtitles)`, 'success');
                            } else {
                                (window as any).showAlert(`Nie udało się skopiować linku do schowka. Link: ${subtitlesUrl}`, 'warning');
                            }
                        }

                        return true;
                    }

                    console.log('OpenSubtitles API did not find subtitles, trying fallback methods');
                }
            } catch (openSubtitlesError) {
                console.warn('OpenSubtitles API search failed, trying fallback methods...', openSubtitlesError);
            }

            // Wyszukaj napisy przez API z autoryzacją (używając bezpośrednio Pythona i subliminal)
            try {
                console.log('Trying primary subtitle search method (Python/subliminal)...');
                const response = await getWithAuth(`/api/subtitles/search-python?query=${query}&lang=pl`);

                if (response.ok) {
                    return await handleSubtitleResponse(response);
                } else {
                    console.warn(`Primary subtitle search failed with status: ${response.status}`);
                    throw new Error(`Błąd wyszukiwania napisów: ${response.status}`);
                }
            } catch (primaryError) {
                console.warn('Primary subtitle search method failed, trying alternative method...', primaryError);

                // Spróbuj alternatywnej metody
                const altResponse = await getWithAuth(`/api/subtitles/search-alt?query=${query}&lang=pl`);

                if (!altResponse.ok) {
                    throw new Error(`Błąd wyszukiwania napisów (alternatywna metoda): ${altResponse.status}`);
                }

                return await handleSubtitleResponse(altResponse);
            }

            // Funkcja pomocnicza do obsługi odpowiedzi
            async function handleSubtitleResponse(response: Response) {
                const data = await response.json();

                if (data && data.subtitles && data.subtitles.length > 0) {
                    // Pobierz pierwszy wynik
                    const subtitle = data.subtitles[0];

                    // Wybierz najlepszy dostępny link i dodaj domenę jeśli potrzeba
                    if (subtitle.download_link) {
                        subtitlesUrl = subtitle.download_link;
                    } else if (subtitle.page_link) {
                        // Sprawdź, czy link zawiera pełny URL
                        if (subtitle.provider === 'podnapisi') {
                            if (!subtitle.page_link.startsWith('http')) {
                                // Dodaj domenę dla Podnapisi i /download na końcu
                                subtitlesUrl = 'https://www.podnapisi.net' + subtitle.page_link + '/download';
                            } else {
                                // Jeśli już ma pełny URL, dodaj tylko /download
                                subtitlesUrl = subtitle.page_link + '/download';
                            }
                        } else {
                            subtitlesUrl = subtitle.page_link;
                        }
                    } else {
                        subtitlesUrl = '';
                    }

                    if (!subtitlesUrl) {
                        console.error('No subtitle URL found in result:', subtitle);
                        throw new Error('Nie znaleziono linku do pobrania napisów');
                    }

                    console.log(`Found subtitle: ${subtitle.provider}, score: ${subtitle.score}`);
                    console.log(`Subtitle URL: ${subtitlesUrl}`);

                    // Skopiuj link do schowka
                    // Funkcja pomocnicza do kopiowania przez fallback
                    const copyTextToClipboardFallback = (text: string) => {
                        const textArea = document.createElement('textarea');
                        textArea.value = text;

                        // Ukryj element, ale upewnij się, że jest w DOM
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);

                        // Zaznacz i skopiuj tekst
                        textArea.focus();
                        textArea.select();

                        let success = false;
                        try {
                            success = document.execCommand('copy');
                        } catch (err) {
                            console.error('Fallback: Nie udało się skopiować tekstu', err);
                        }

                        // Usuń element
                        document.body.removeChild(textArea);
                        return success;
                    };

                    let copySuccess = false;

                    // Najpierw spróbuj użyć nowoczesnego API Clipboard
                    if (navigator && navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
                        try {
                            await navigator.clipboard.writeText(subtitlesUrl);
                            copySuccess = true;
                        } catch (clipboardError) {
                            console.warn('Clipboard API nie zadziałało, próbuję fallback', clipboardError);
                        }
                    } else {
                        console.warn('Clipboard API niedostępne, używam fallbacku');
                    }

                    // Jeśli nowoczesne API nie zadziałało, użyj fallbacku
                    if (!copySuccess) {
                        copySuccess = copyTextToClipboardFallback(subtitlesUrl);
                    }

                    // Pokaż odpowiednie powiadomienie
                    if (typeof window !== 'undefined') {
                        if (copySuccess) {
                            (window as any).showNotification(`Link do napisów skopiowany do schowka! (${subtitle.provider})`, 'success');
                        } else {
                            (window as any).showAlert(`Nie udało się skopiować linku do schowka. Link: ${subtitlesUrl}`, 'warning');
                        }
                    }

                    return true;
                } else {
                    // Pokaż powiadomienie o braku napisów
                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert(`Nie znaleziono napisów dla tego pliku`, 'warning');
                    }
                    return false;
                }
            }
        } catch (error: unknown) {
            console.error('Error searching subtitles:', error);

            // Pokaż powiadomienie o błędzie
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd wyszukiwania napisów: ${error instanceof Error ? error.message : 'Nieznany błąd'}`, 'error');
            }
            return false;
        } finally {
            isSearchingSubtitles = false;
        }
    }

    // Function to format file size
    function formatFileSize(size: string): string {
        if (!size || size === 'Unknown') return 'Nieznany rozmiar';
        return size;
    }

    // Function to get total pages for pagination
    function getTotalPages(): number {
        // If we're in a specific view mode, handle accordingly
        if (viewMode === 'releases' && selectedEpisode !== null) {
            return Math.ceil(episodeReleases.length / itemsPerPage);
        }

        if (viewMode === 'episodes') {
            // In episodes view, we paginate the episodes
            return Math.ceil(visibleResults.length / itemsPerPage);
        }

        // Otherwise, we're in search view - filter by category
        let filteredResults = searchResults;

        if (activeCategory === 'movies') {
            // For movies, count unique movie groups (not individual movies)
            const movies = searchResults.filter(result => result.type === 'movie');

            // Create a set of unique movie groups
            const uniqueMovieGroups = new Set();

            movies.forEach(movie => {
                const groupKey = `${movie.title} (${movie.year})`;
                uniqueMovieGroups.add(groupKey);
            });

            // If a group is expanded, we need to add its items to the count
            if (selectedMovieGroup && groupedMovies.has(selectedMovieGroup)) {
                // Add the number of movies in the selected group (minus 1 for the group itself)
                const groupSize = groupedMovies.get(selectedMovieGroup)?.length || 0;

                // Calculate total items: unique groups + items in expanded group
                const totalItems = uniqueMovieGroups.size + (groupSize > 0 ? groupSize : 0);
                return Math.ceil(totalItems / itemsPerPage);
            }

            return Math.ceil(uniqueMovieGroups.size / itemsPerPage);
        } else if (activeCategory === 'tvshows') {
            // For TV shows, count unique TV shows
            const tvShows = searchResults.filter(result => result.type === 'tvshow');
            const uniqueTVShowIds = new Set();

            tvShows.forEach(result => {
                uniqueTVShowIds.add(result.imdbID);
            });

            return Math.ceil(uniqueTVShowIds.size / itemsPerPage);
        }

        return Math.ceil(filteredResults.length / itemsPerPage);
    }

    // Function to get pagination range
    function getPaginationRange(): number[] {
        const totalPages = getTotalPages();
        const range: number[] = [];

        // Show at most 5 pages
        const maxPagesToShow = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
        let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

        // Adjust start page if end page is at max
        if (endPage === totalPages) {
            startPage = Math.max(1, endPage - maxPagesToShow + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            range.push(i);
        }

        return range;
    }
</script>

<div class="card">
    <div class="flex items-center justify-between mb-4">
        <div>
            <h2>{m["nav.cinema"]()}</h2>
            <p class="text-[var(--text-secondary)]">Wyszukaj i pobierz filmy oraz seriale</p>
        </div>
    </div>

    <!-- Content Type Selection -->
    {#if contentType === 'selection'}
        <div class="content-type-selection">
            <div class="content-type-header">
                <h3>Co chcesz wyszukać?</h3>
                <p>Wybierz rodzaj treści, której szukasz</p>
            </div>

            <div class="content-type-options">
                <button class="content-type-card" on:click={() => setContentType('movies')}>
                    <div class="content-type-icon">
                        <Film size={48} />
                    </div>
                    <h4>Filmy</h4>
                    <p>Wyszukaj filmy z różnych źródeł</p>
                </button>

                <button class="content-type-card" on:click={() => setContentType('tvshows')}>
                    <div class="content-type-icon">
                        <Tv size={48} />
                    </div>
                    <h4>Seriale</h4>
                    <p>Wyszukaj seriale i odcinki</p>
                </button>
            </div>
        </div>

        <!-- Trakt.tv Watchlist Grid -->
        {#if showTraktGrid}
            <!-- TV Shows Section -->
            <div class="trakt-section">
                <div class="trakt-header">
                    <div class="trakt-title">
                        <h3>Popularne seriale</h3>
                        <p>Najnowsze i popularne seriale z Trakt.tv</p>
                    </div>
                    <button class="refresh-button" on:click={loadTraktShows} disabled={isTraktShowsLoading}>
                        <RefreshCw size={18} class={isTraktShowsLoading ? 'animate-spin' : ''} />
                        <span>Odśwież</span>
                    </button>
                </div>

                {#if isTraktShowsLoading}
                    <div class="trakt-loading">
                        <RefreshCw size={32} class="animate-spin" />
                        <p>Ładowanie popularnych seriali...</p>
                    </div>
                {:else if traktShowsError}
                    <div class="trakt-error">
                        <p>Wystąpił błąd podczas ładowania seriali: {traktShowsError}</p>
                        <button class="btn btn-outline" on:click={loadTraktShows}>Spróbuj ponownie</button>
                    </div>
                {:else if traktShowItems.length === 0}
                    <div class="trakt-empty">
                        <p>Nie znaleziono seriali</p>
                    </div>
                {:else}
                    <div class="trakt-grid">
                        {#each traktShowItems as item}
                            <div
                                class="trakt-card"
                                on:click={() => searchTraktShow(item)}
                                on:keydown={(e) => e.key === 'Enter' && searchTraktShow(item)}
                                role="button"
                                tabindex="0"
                            >
                                <div class="trakt-card-inner">
                                    <div class="trakt-poster">
                                        {#if item.poster}
                                            <img src={item.poster} alt={item.title} loading="lazy"
                                                 on:error={() => {
                                                     // Jeśli obraz nie załaduje się, usuń URL okładki
                                                     item.poster = null;
                                                 }}>
                                        {:else}
                                            <div class="trakt-poster-placeholder">
                                                <Tv size={32} />
                                            </div>
                                        {/if}
                                    </div>
                                    <div class="trakt-info">
                                        <h4 class="trakt-title">{item.title}</h4>
                                        {#if item.year}
                                            <span class="trakt-year">{item.year}</span>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        {/each}
                    </div>
                {/if}
            </div>

            <!-- Movies Section -->
            <div class="trakt-section">
                <div class="trakt-header">
                    <div class="trakt-title">
                        <h3>Najlepsze filmy tygodnia</h3>
                        <p>Popularne filmy z Trakt.tv</p>
                    </div>
                    <button class="refresh-button" on:click={loadTraktMovies} disabled={isTraktMoviesLoading}>
                        <RefreshCw size={18} class={isTraktMoviesLoading ? 'animate-spin' : ''} />
                        <span>Odśwież</span>
                    </button>
                </div>

                {#if isTraktMoviesLoading}
                    <div class="trakt-loading">
                        <RefreshCw size={32} class="animate-spin" />
                        <p>Ładowanie popularnych filmów...</p>
                    </div>
                {:else if traktMoviesError}
                    <div class="trakt-error">
                        <p>Wystąpił błąd podczas ładowania filmów: {traktMoviesError}</p>
                        <button class="btn btn-outline" on:click={loadTraktMovies}>Spróbuj ponownie</button>
                    </div>
                {:else if traktMovieItems.length === 0}
                    <div class="trakt-empty">
                        <p>Nie znaleziono filmów</p>
                    </div>
                {:else}
                    <div class="trakt-grid">
                        {#each traktMovieItems as item}
                            <div
                                class="trakt-card"
                                on:click={() => searchTraktShow(item)}
                                on:keydown={(e) => e.key === 'Enter' && searchTraktShow(item)}
                                role="button"
                                tabindex="0"
                            >
                                <div class="trakt-card-inner">
                                    <div class="trakt-poster">
                                        {#if item.poster}
                                            <img src={item.poster} alt={item.title} loading="lazy"
                                                 on:error={() => {
                                                     // Jeśli obraz nie załaduje się, usuń URL okładki
                                                     item.poster = null;
                                                 }}>
                                        {:else}
                                            <div class="trakt-poster-placeholder">
                                                <Film size={32} />
                                            </div>
                                        {/if}
                                    </div>
                                    <div class="trakt-info">
                                        <h4 class="trakt-title">{item.title}</h4>
                                        {#if item.year}
                                            <span class="trakt-year">{item.year}</span>
                                        {/if}
                                    </div>
                                </div>
                            </div>
                        {/each}
                    </div>
                {/if}
            </div>
        {/if}
    {:else}
        <!-- Search input -->
        <div class="search-header">
            <button class="back-button" on:click={() => setContentType('selection')}>
                <span>← Powrót do wyboru</span>
            </button>
            <h3>{contentType === 'movies' ? 'Wyszukiwanie filmów' : 'Wyszukiwanie seriali'}</h3>
        </div>

        <div style="display: flex; gap: 10px; margin-top: 20px; position: relative;">
            <input type="text" id="cinemaSearchQuery" class="magnet-input"
                   style="flex: 1; padding-right: 40px;"
                   placeholder={contentType === 'movies'
                       ? "Wpisz tytuł filmu (min. 3 znaki) lub IMDB ID (np. tt0111161)..."
                       : "Wpisz tytuł serialu (min. 3 znaki) lub IMDB ID (np. tt0903747)..."}
                   bind:value={cinemaSearchQuery}
                   on:input={handleSearchInput}
                   on:keypress={handleKeyPress}
                   disabled={isSearching}>

            {#if isSearching}
                <div class="search-loading-indicator">
                    <Loader2 size={20} class="animate-spin" />
                </div>
            {:else if cinemaSearchQuery.trim().length > 0}
                <button class="search-clear-button" on:click={() => { cinemaSearchQuery = ''; searchResults = []; visibleResults = []; }}>
                    &times;
                </button>
            {/if}

            <button class="btn btn-primary"
                    on:click={searchMedia}
                    disabled={isSearching || cinemaSearchQuery.trim().length < 3}>
                <Search size={18} strokeWidth={2} />
                <span>Szukaj</span>
            </button>
        </div>

        <!-- Category tabs are no longer needed with content type selection -->
    {/if}

    <!-- TV Show Navigation - Episodes View -->
    {#if viewMode === 'episodes' && selectedTVShow}
        <div class="mt-4 navigation-header">
            <button class="back-button" on:click={() => { resetTVShowNavigation(); updateVisibleResults(); }}>
                <span>← Powrót do wyszukiwania</span>
            </button>
            <h3 class="tv-show-title">{selectedTVShow.title} {selectedTVShow.year ? `(${selectedTVShow.year})` : ''}</h3>
        </div>

        <!-- Season filter -->
        {#if availableSeasons.length > 1}
            <div class="mt-4 season-filter">
                <button
                    class="season-btn {selectedSeason === null ? 'active' : ''}"
                    on:click={() => filterBySeason(null)}
                >
                    Wszystkie sezony
                </button>

                {#each availableSeasons as season}
                    <button
                        class="season-btn {selectedSeason === season ? 'active' : ''}"
                        on:click={() => filterBySeason(season)}
                    >
                        Sezon {season}
                    </button>
                {/each}
            </div>
        {/if}

        {#if visibleResults.length === 0}
            <div class="empty-state">
                <p>Nie znaleziono odcinków dla tego serialu</p>
            </div>
        {:else}
            <div class="mt-4 episodes-count">
                <p>Znaleziono {visibleResults.length} odcinków</p>
            </div>
        {/if}
    {/if}

    <!-- TV Show Navigation - Releases View -->
    {#if viewMode === 'releases' && selectedEpisode !== null}
        <div class="mt-4 navigation-header">
            <button class="back-button" on:click={backToEpisodes}>
                <span>← Powrót do odcinków</span>
            </button>
            <h3 class="tv-show-title">
                {selectedTVShow.title} - S{selectedEpisode.episodeInfo.season}E{selectedEpisode.episodeInfo.episode}
            </h3>
        </div>

        {#if isSearching}
            <div class="loading-container">
                <Loader2 size={40} class="animate-spin" />
                <p>Pobieranie wydań...</p>
            </div>
        {:else if episodeReleases.length === 0}
            <div class="empty-state">
                <p>Nie znaleziono wydań dla tego odcinka</p>
            </div>
        {:else}
            <div class="releases-count">
                <p>Znaleziono {episodeReleases.length} wydań</p>
            </div>
        {/if}
    {/if}

    <!-- Search results -->
    {#if isSearching}
        <div class="empty-state">
            <div class="loading" style="width: 24px; height: 24px; margin: 0 auto;"></div>
            <p style="margin-top: 10px;">Wyszukiwanie...</p>
        </div>
    {:else if searchError}
        <div class="empty-state">
            <p>Wystąpił błąd podczas wyszukiwania.<br/>{searchError}</p>
            <button class="btn btn-outline" style="margin-top: 10px;" on:click={searchMedia}>Spróbuj ponownie</button>
        </div>
    {:else if visibleResults.length > 0}
        <div class="mt-4 results-container">
        {#each visibleResults as result}
            <div class="media-item"
                 class:clickable={
                    (viewMode === 'search' && result.type === 'tvshow') ||
                    (viewMode === 'episodes' && result.episodeInfo) ||
                    (result.isMovieGroup)
                 }
                 class:movie-group={result.isMovieGroup}
                 class:movie-group-item={result.isGroupItem}
                 class:expanded={result.isMovieGroup && result.expanded}
                 role="button"
                 tabindex="0"
                 on:click={() => {
                    if (viewMode === 'search' && result.type === 'tvshow') {
                        selectTVShow(result);
                    } else if (viewMode === 'episodes' && result.episodeInfo) {
                        selectEpisode(result);
                    } else if (result.isMovieGroup) {
                        toggleMovieGroup(result.groupKey);
                    }
                 }}
                 on:keydown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        if (viewMode === 'search' && result.type === 'tvshow') {
                            selectTVShow(result);
                        } else if (viewMode === 'episodes' && result.episodeInfo) {
                            selectEpisode(result);
                        } else if (result.isMovieGroup) {
                            toggleMovieGroup(result.groupKey);
                        }
                    }
                 }}
            >
                <!-- Media info -->
                <div class="media-info">
                    {#if result.isMovieGroup}
                        <!-- Movie Group Display - only show if not expanded -->
                        {#if !result.expanded}
                            {#if result.poster}
                                <div class="media-poster">
                                    <img src={result.poster} alt={result.title} />
                                </div>
                            {:else}
                                <div class="media-icon">
                                    <Film size={24} />
                                </div>
                            {/if}
                            <div class="media-details">
                                <div class="flex items-center gap-2">
                                    <h4 class="media-title">
                                        {result.title}
                                        {#if result.year}
                                            <span class="media-year">({result.year})</span>
                                        {/if}
                                        <span class="group-count-badge">{result.groupCount} {result.groupCount === 1 ? 'wersja' : 'wersje'}</span>
                                    </h4>
                                </div>
                                <div class="media-meta">
                                    <span class="media-type">Film</span>
                                    <span class="media-group-hint">Kliknij, aby zobaczyć wszystkie wersje</span>
                                </div>
                            </div>
                        {/if}
                    {:else if result.isGroupItem}
                        <!-- Movie Group Item Display -->
                        <div class="group-item-indicator"></div>
                        <div class="media-details">
                            <div class="flex items-center gap-2">
                                <h4 class="media-title">
                                    {result.quality} {result.size ? `(${formatFileSize(result.size)})` : ''}
                                    {#if watchedItems.has(result.infoHash)}
                                        <span class="found-badge">ZNALEZIONO!</span>
                                    {/if}
                                </h4>
                            </div>
                            <div class="media-meta">
                                {#if result.seeders !== null && result.seeders !== undefined}
                                    <span class="media-seeders">Seeders: {result.seeders}</span>
                                {/if}

                                {#if result.languages && result.languages.length > 0}
                                    <span class="media-languages">{result.languages.join(', ')}</span>
                                {/if}
                            </div>
                            {#if result.filename}
                                <div class="media-filename" title={result.filename}>
                                    {result.filename}
                                </div>
                            {:else if result.originalTitle}
                                <div class="media-filename" title={result.originalTitle}>
                                    {result.originalTitle}
                                </div>
                            {/if}
                        </div>
                    {:else}
                        <!-- Regular Item Display -->
                        {#if result.poster}
                            <div class="media-poster">
                                <img src={result.poster} alt={result.title} />
                            </div>
                        {:else}
                            <div class="media-icon">
                                {#if result.type === 'movie'}
                                    <Film size={24} />
                                {:else}
                                    <Tv size={24} />
                                {/if}
                            </div>
                        {/if}
                        <div class="media-details">
                            <div class="flex items-center gap-2">
                                <h4 class="media-title">
                                    {result.title}
                                    {#if result.year}
                                        <span class="media-year">({result.year})</span>
                                    {/if}
                                    {#if result.episodeInfo && (viewMode === 'episodes' || viewMode === 'releases')}
                                        <span class="media-episode">S{result.episodeInfo.season}E{result.episodeInfo.episode}</span>
                                    {/if}
                                    {#if result.isCompleteSeason}
                                        <span class="complete-badge">PEŁNY SEZON</span>
                                    {/if}
                                </h4>
                                <div class="badges">
                                    {#if watchedItems.has(result.infoHash)}
                                        <span class="found-badge">ZNALEZIONO!</span>
                                    {/if}
                                </div>
                            </div>
                            <div class="media-meta">
                                <!-- Only show quality, size, seeders for movies and episode releases -->
                                {#if (result.type === 'movie' || viewMode === 'releases') && result.quality}
                                    <span class="media-quality">{result.quality}</span>
                                {/if}

                                {#if (result.type === 'movie' || viewMode === 'releases') && result.size}
                                    <span class="media-size">{formatFileSize(result.size)}</span>
                                {/if}

                                {#if (result.type === 'movie' || viewMode === 'releases') && result.seeders !== null && result.seeders !== undefined}
                                    <span class="media-seeders">Seeders: {result.seeders}</span>
                                {/if}

                                <span class="media-type">{result.type === 'movie' ? 'Film' : 'Serial'}</span>

                                {#if result.languages && result.languages.length > 0}
                                    <span class="media-languages">{result.languages.join(', ')}</span>
                                {/if}
                            </div>
                            {#if (viewMode === 'releases' || result.type === 'movie')}
                                {#if result.filename}
                                    <div class="media-filename" title={result.filename}>
                                        {result.filename}
                                    </div>
                                {:else if result.originalTitle}
                                    <div class="media-filename" title={result.originalTitle}>
                                        {result.originalTitle}
                                    </div>
                                {/if}
                            {/if}
                        </div>
                    {/if}
                </div>

                <!-- Movie Details Panel (shown when a movie group is selected) -->
                {#if result.isMovieGroup && result.expanded && result.groupKey === selectedMovieGroup && selectedMovieDetails}
                    <div class="movie-details-panel">
                        <div class="movie-details-content">
                            <div class="movie-details-poster">
                                {#if selectedMovieDetails.poster}
                                    <img src={selectedMovieDetails.poster} alt={selectedMovieDetails.title} />
                                {:else}
                                    <div class="movie-details-poster-placeholder">
                                        <Film size={48} />
                                    </div>
                                {/if}
                            </div>
                            <div class="movie-details-info">
                                <h3 class="movie-details-title">
                                    {selectedMovieDetails.title}
                                    {#if selectedMovieDetails.year}
                                        <span class="movie-details-year">({selectedMovieDetails.year})</span>
                                    {/if}
                                </h3>

                                <div class="movie-details-meta">
                                    {#if selectedMovieDetails.runtime}
                                        <span class="movie-details-runtime">{selectedMovieDetails.runtime}</span>
                                    {/if}

                                    {#if selectedMovieDetails.rating}
                                        <span class="movie-details-rating">⭐ {selectedMovieDetails.rating}</span>
                                    {/if}

                                    {#if selectedMovieDetails.genres && selectedMovieDetails.genres.length > 0}
                                        <div class="movie-details-genres">
                                            {#each selectedMovieDetails.genres as genre}
                                                <span class="movie-details-genre">{genre}</span>
                                            {/each}
                                        </div>
                                    {/if}
                                </div>

                                {#if selectedMovieDetails.overview}
                                    <div class="movie-details-overview">
                                        <p>{selectedMovieDetails.overview}</p>
                                    </div>
                                {/if}

                                {#if selectedMovieDetails.director}
                                    <div class="movie-details-director">
                                        <strong>Reżyser:</strong> {selectedMovieDetails.director}
                                    </div>
                                {/if}

                                {#if selectedMovieDetails.cast && selectedMovieDetails.cast.length > 0}
                                    <div class="movie-details-cast">
                                        <strong>Obsada:</strong> {selectedMovieDetails.cast.join(', ')}
                                    </div>
                                {/if}

                                <div class="movie-details-links">
                                    {#if selectedMovieDetails.imdbUrl}
                                        <a href={selectedMovieDetails.imdbUrl} target="_blank" rel="noopener noreferrer" class="movie-details-link">
                                            IMDB
                                        </a>
                                    {/if}

                                    {#if selectedMovieDetails.traktUrl}
                                        <a href={selectedMovieDetails.traktUrl} target="_blank" rel="noopener noreferrer" class="movie-details-link">
                                            Trakt.tv
                                        </a>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>
                {/if}

                <!-- Action buttons - only show for individual items, not for movie groups -->
                <div class="media-actions">
                    {#if result.infoHash && !result.isMovieGroup}
                        <button class="action-btn download-btn" on:click={(e) => { e.stopPropagation(); handleDownload(result); }} title="Dodaj do zakładki Dodaj torrent">
                            <Download size={20} />
                        </button>
                        <!-- VLC Stream button (hidden on screens 1024px and larger) -->
                        <button class="action-btn vlc-btn hide-on-desktop" on:click={(e) => { e.stopPropagation(); handleVlcStream(result); }} title="Otwórz w VLC" aria-label="Otwórz w VLC">
                            <ExternalLink size={20} />
                        </button>
                        <!-- Stream Link button (visible only on desktop) -->
                        <button class="action-btn link-btn show-on-desktop" on:click={(e) => { e.stopPropagation(); handleCopyStreamLink(result); }} title="Kopiuj link do streamowania" aria-label="Kopiuj link do streamowania">
                            <Copy size={20} />
                        </button>
                        <!-- Play in Browser button (visible only on desktop) -->
                        <button class="action-btn play-browser-btn show-on-desktop" on:click={(e) => { e.stopPropagation(); handlePlayInBrowser(result); }} title="Odtwórz w przeglądarce" aria-label="Odtwórz w przeglądarce">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                        </button>
                        <!-- Tymczasowo ukryte, ponieważ nie działa
                        <button class="action-btn copy-btn" on:click={(e) => { e.stopPropagation(); copyMagnetLink(result); }} title="Kopiuj link magnet">
                            <Copy size={20} />
                        </button>
                        -->
                        <!-- Tymczasowo ukryte, ponieważ nie działa
                        <button class="action-btn play-btn" on:click={(e) => { e.stopPropagation(); handleWatch(result); }} title="Oglądaj" aria-label="Oglądaj">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polygon points="5 3 19 12 5 21 5 3"></polygon>
                            </svg>
                        </button>
                        -->
                        {#if result.filename && (result.filename.toLowerCase().endsWith('.mp4') || result.filename.toLowerCase().endsWith('.mkv'))}
                            <button class="action-btn subtitles-btn" on:click={(e) => { e.stopPropagation(); searchSubtitles(result.filename); }} title="Wyszukaj napisy" aria-label="Wyszukaj napisy">
                                <Subtitles size={20} />
                            </button>
                        {/if}
                    {/if}
                </div>
            </div>
        {/each}

        <!-- Pagination -->
        {#if getTotalPages() > 1}
            <div class="pagination">
                <button class="pagination-btn" disabled={currentPage === 1} on:click={() => goToPage(1)}>
                    &laquo;
                </button>
                <button class="pagination-btn" disabled={currentPage === 1} on:click={() => goToPage(currentPage - 1)}>
                    &lsaquo;
                </button>

                {#each getPaginationRange() as page}
                    <button class="pagination-btn {currentPage === page ? 'active' : ''}" on:click={() => goToPage(page)}>
                        {page}
                    </button>
                {/each}

                <button class="pagination-btn" disabled={currentPage === getTotalPages()} on:click={() => goToPage(currentPage + 1)}>
                    &rsaquo;
                </button>
                <button class="pagination-btn" disabled={currentPage === getTotalPages()} on:click={() => goToPage(getTotalPages())}>
                    &raquo;
                </button>
            </div>
        {/if}
        </div>
    {:else if cinemaSearchQuery.trim().length >= 3}
        <div class="empty-state">
            <p>Nie znaleziono wyników dla "{cinemaSearchQuery}"</p>
        </div>
    {:else}
        <!-- Wyświetl platformy streamingowe tylko gdy jesteśmy w sekcji seriali i pole wyszukiwania jest puste -->
        {#if contentType === 'tvshows' && showStreamingPlatforms}
            <div class="streaming-platforms">
                <!-- Amazon Prime Shows -->
                {#if isStreamingPlatformsLoading}
                    <div class="loading-container">
                        <Loader2 size={40} class="animate-spin" />
                        <p>Ładowanie list platform streamingowych...</p>
                    </div>
                {:else}
                    <!-- Netflix Shows -->
                    <div class="platform-section">
                        <h3 class="platform-title">
                            <img src="/images/streamingplatforms/netflix.svg" alt="Netflix" class="platform-logo" />
                        </h3>

                        {#if netflixShows.length > 0}
                            <div class="trakt-grid">
                                {#each netflixShows as item}
                                    <button class="trakt-item"
                                            on:click={() => searchTraktShow(item)}
                                            aria-label="Wyszukaj {item.title}">
                                        <div class="trakt-poster">
                                            {#if item.poster}
                                                <img src={item.poster} alt={item.title} />
                                            {:else}
                                                <div class="trakt-poster-placeholder">
                                                    <Tv size={32} />
                                                </div>
                                            {/if}
                                        </div>
                                        <div class="trakt-title">{item.title}</div>
                                        {#if item.year}
                                            <div class="trakt-year">{item.year}</div>
                                        {/if}
                                    </button>
                                {/each}
                            </div>
                        {:else}
                            <div class="empty-state">
                                <p>Nie udało się załadować listy seriali Netflix</p>
                            </div>
                        {/if}
                    </div>

                    <!-- HBO/Max Shows -->
                    <div class="platform-section">
                        <h3 class="platform-title">
                            <img src="/images/streamingplatforms/max.png" alt="HBO/Max" class="platform-logo" />
                        </h3>

                        {#if hboShows.length > 0}
                            <div class="trakt-grid">
                                {#each hboShows as item}
                                    <button class="trakt-item"
                                            on:click={() => searchTraktShow(item)}
                                            aria-label="Wyszukaj {item.title}">
                                        <div class="trakt-poster">
                                            {#if item.poster}
                                                <img src={item.poster} alt={item.title} />
                                            {:else}
                                                <div class="trakt-poster-placeholder">
                                                    <Tv size={32} />
                                                </div>
                                            {/if}
                                        </div>
                                        <div class="trakt-title">{item.title}</div>
                                        {#if item.year}
                                            <div class="trakt-year">{item.year}</div>
                                        {/if}
                                    </button>
                                {/each}
                            </div>
                        {:else}
                            <div class="empty-state">
                                <p>Nie udało się załadować listy seriali HBO/Max</p>
                            </div>
                        {/if}
                    </div>

                    <!-- Disney+ Shows -->
                    <div class="platform-section">
                        <h3 class="platform-title">
                            <img src="/images/streamingplatforms/disney.webp" alt="Disney+" class="platform-logo" />
                        </h3>

                        {#if disneyShows.length > 0}
                            <div class="trakt-grid">
                                {#each disneyShows as item}
                                    <button class="trakt-item"
                                            on:click={() => searchTraktShow(item)}
                                            aria-label="Wyszukaj {item.title}">
                                        <div class="trakt-poster">
                                            {#if item.poster}
                                                <img src={item.poster} alt={item.title} />
                                            {:else}
                                                <div class="trakt-poster-placeholder">
                                                    <Tv size={32} />
                                                </div>
                                            {/if}
                                        </div>
                                        <div class="trakt-title">{item.title}</div>
                                        {#if item.year}
                                            <div class="trakt-year">{item.year}</div>
                                        {/if}
                                    </button>
                                {/each}
                            </div>
                        {:else}
                            <div class="empty-state">
                                <p>Nie udało się załadować listy seriali Disney+</p>
                            </div>
                        {/if}
                    </div>

                    <!-- Hulu Shows -->
                    <div class="platform-section">
                        <h3 class="platform-title">§
                            <img src="/images/streamingplatforms/hulu.svg" alt="Hulu" class="platform-logo" />
                        </h3>

                        {#if huluShows.length > 0}
                            <div class="trakt-grid">
                                {#each huluShows as item}
                                    <button class="trakt-item"
                                            on:click={() => searchTraktShow(item)}
                                            aria-label="Wyszukaj {item.title}">
                                        <div class="trakt-poster">
                                            {#if item.poster}
                                                <img src={item.poster} alt={item.title} />
                                            {:else}
                                                <div class="trakt-poster-placeholder">
                                                    <Tv size={32} />
                                                </div>
                                            {/if}
                                        </div>
                                        <div class="trakt-title">{item.title}</div>
                                        {#if item.year}
                                            <div class="trakt-year">{item.year}</div>
                                        {/if}
                                    </button>
                                {/each}
                            </div>
                        {:else}
                            <div class="empty-state">
                                <p>Nie udało się załadować listy seriali Hulu</p>
                            </div>
                        {/if}
                    </div>

                    <!-- Amazon Prime Shows -->
                    <div class="platform-section">
                        <h3 class="platform-title">
                            <img src="/images/streamingplatforms/prime.svg" alt="Amazon Prime" class="platform-logo" />
                        </h3>

                        {#if amazonPrimeShows.length > 0}
                            <div class="trakt-grid">
                                {#each amazonPrimeShows as item}
                                    <button class="trakt-item"
                                            on:click={() => searchTraktShow(item)}
                                            aria-label="Wyszukaj {item.title}">
                                        <div class="trakt-poster">
                                            {#if item.poster}
                                                <img src={item.poster} alt={item.title} />
                                            {:else}
                                                <div class="trakt-poster-placeholder">
                                                    <Tv size={32} />
                                                </div>
                                            {/if}
                                        </div>
                                        <div class="trakt-title">{item.title}</div>
                                        {#if item.year}
                                            <div class="trakt-year">{item.year}</div>
                                        {/if}
                                    </button>
                                {/each}
                            </div>
                        {:else}
                            <div class="empty-state">
                                <p>Nie udało się załadować listy seriali Amazon Prime</p>
                            </div>
                        {/if}
                    </div>
                {/if}
            </div>
        {:else}
            <div class="empty-state">
            </div>
        {/if}
    {/if}

    <!-- Loading Indicator -->
    {#if isLoadingVideo}
        <div class="loading-overlay">
            <div class="loading-spinner">
                <Loader2 size={40} class="animate-spin" />
                <p>Przygotowywanie odtwarzacza...</p>
            </div>
        </div>
    {/if}

    <!-- Video Player Modal -->
    {#if showVideoPlayer && videoUrl}
        <div
            class="video-modal-overlay"
            on:keydown={(e) => e.key === 'Escape' && closeVideoPlayer()}
            role="dialog"
            aria-modal="true"
            aria-labelledby="video-modal-title"
            tabindex="-1"
        >
            <div
                class="video-modal"
                role="document"
            >
                <div class="video-modal-header">
                    <h3 id="video-modal-title">
                        {selectedEpisode ?
                            `${selectedTVShow.title} - S${selectedEpisode.episodeInfo.season}E${selectedEpisode.episodeInfo.episode}` :
                            (selectedTVShow ? selectedTVShow.title : 'Odtwarzacz wideo')
                        }
                    </h3>
                    <button class="close-btn" on:click={closeVideoPlayer} aria-label="Zamknij">×</button>
                </div>
                <div class="video-container">
                    <video controls autoplay class="video-player" id="videoPlayer">
                        <source src={videoUrl} type="video/mp4">
                        <track kind="captions" src={subtitlesUrl || ''} label="Polski" srclang="pl" default>
                        Twoja przeglądarka nie obsługuje odtwarzania wideo.
                    </video>

                    {#if subtitlesUrl}
                    <div class="video-controls">
                        <div class="subtitle-controls">
                            <button on:click={() => toggleSubtitles(true)} class="control-button">
                                <Subtitles size={16} /> Włącz napisy
                            </button>
                            <button on:click={() => toggleSubtitles(false)} class="control-button">
                                <Subtitles size={16} /> Wyłącz napisy
                            </button>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    {/if}
</div>

<style>
    .media-item {
        background-color: var(--item-bg);
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 15px;
    }

    .media-info {
        display: flex;
        gap: 15px;
        flex: 1;
    }

    .media-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background-color: var(--card-bg);
        border-radius: 50%;
        color: var(--primary);
    }

    .media-poster {
        width: 60px;
        height: 90px;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .media-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .media-year {
        font-weight: normal;
        color: var(--text-secondary);
        font-size: 0.9em;
    }

    .media-episode {
        font-weight: bold;
        color: #3b82f6;
        font-size: 0.9em;
        margin-left: 8px;
    }

    .media-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .media-title {
        font-weight: bold;
        margin: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .media-meta {
        display: flex;
        gap: 15px;
        font-size: 12px;
        color: var(--text-secondary);
        flex-wrap: wrap;
    }

    .media-languages {
        color: #3b82f6;
    }

    .media-filename {
        font-size: 11px;
        color: var(--text-secondary);
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid var(--border);
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        line-height: 1.4;
        max-height: 4.2em; /* 3 lines */
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
    }

    .media-actions {
        display: flex;
        gap: 8px;
    }

    .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: var(--card-bg);
        border: 1px solid var(--border);
        color: var(--text);
        transition: all 0.2s ease;
    }

    .action-btn:hover {
        background-color: var(--primary);
        color: white;
    }

    .download-btn:hover {
        background-color: #3dd68c;
    }

    .subtitles-btn {
        background-color: #6366f1;
    }

    .subtitles-btn:hover {
        background-color: #4f46e5;
    }

    .vlc-btn {
        background-color: #ff8800;
        color: white;
    }

    .vlc-btn:hover {
        background-color: #e67a00;
    }

    .link-btn {
        background-color: #3b82f6;
        color: white;
    }

    .link-btn:hover {
        background-color: #2563eb;
    }

    .play-browser-btn {
        background-color: #ec4899;
        color: white;
    }

    .play-browser-btn:hover {
        background-color: #db2777;
    }

    /* Movie Group Styles */
    .movie-group {
        border-left: 4px solid #ec4899;
        transition: all 0.3s ease;
    }

    .movie-group:hover {
        background-color: rgba(236, 72, 153, 0.1);
    }

    .movie-group.expanded {
        background-color: rgba(236, 72, 153, 0.1);
    }

    .movie-group-item {
        padding-left: 1.5rem;
        border-left: 4px solid #ec4899;
        background-color: rgba(236, 72, 153, 0.05);
        margin-top: 2px;
    }

    .group-item-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #ec4899;
        margin-right: 8px;
        margin-left: 8px;
    }

    .group-count-badge {
        background-color: #ec4899;
        color: white;
        font-size: 0.75rem;
        padding: 0.125rem 0.5rem;
        border-radius: 9999px;
        margin-left: 0.5rem;
    }

    .media-group-hint {
        color: #ec4899;
        font-size: 0.75rem;
        font-style: italic;
    }

    /* Movie Details Panel */
    .movie-details-panel {
        width: 100%;
        background-color: rgba(17, 24, 39, 0.7);
        border-radius: 0.5rem;
        margin-top: 0.5rem;
        margin-bottom: 0.5rem;
        padding: 1rem;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .movie-details-content {
        display: flex;
        gap: 1rem;
    }

    .movie-details-poster {
        flex-shrink: 0;
        width: 200px;
        height: 300px;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .movie-details-poster img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .movie-details-poster-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .movie-details-info {
        flex: 1;
    }

    .movie-details-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .movie-details-year {
        font-weight: 400;
        opacity: 0.8;
        margin-left: 0.25rem;
    }

    .movie-details-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .movie-details-runtime, .movie-details-rating {
        font-size: 0.875rem;
        background-color: rgba(255, 255, 255, 0.1);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }

    .movie-details-genres {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .movie-details-genre {
        font-size: 0.75rem;
        background-color: #ec4899;
        color: white;
        padding: 0.125rem 0.5rem;
        border-radius: 9999px;
    }

    .movie-details-overview {
        margin-bottom: 0.75rem;
        font-size: 0.875rem;
        line-height: 1.5;
    }

    .movie-details-director, .movie-details-cast {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .movie-details-links {
        display: flex;
        gap: 0.75rem;
        margin-top: 1rem;
    }

    .movie-details-link {
        display: inline-block;
        padding: 0.375rem 0.75rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        text-decoration: none;
        transition: background-color 0.2s;
        font-weight: 600;
    }

    .movie-details-link[href*="imdb.com"] {
        background-color: #f5c518; /* IMDB yellow */
        color: #000000;
    }

    .movie-details-link[href*="imdb.com"]:hover {
        background-color: #e6b800;
    }

    .movie-details-link[href*="trakt.tv"] {
        background-color: #ed1c24; /* Trakt red */
        color: white;
    }

    .movie-details-link[href*="trakt.tv"]:hover {
        background-color: #d01018;
    }

    /* Streaming Platforms Styles */
    .streaming-platforms {
        margin-top: 20px;
    }

    .platform-section {
        margin-bottom: 30px;
    }

    .platform-title {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        font-size: 20px;
        font-weight: 600;
    }

    .platform-logo {
        height: autox;
        width: 120px;
        object-fit: contain;
        margin-top: 30px;
    }

    .trakt-item {
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;
        text-align: left;
        width: 100%;
        transition: transform 0.2s ease;
        color: inherit;
    }

    .trakt-item:hover {
        transform: translateY(-5px);
    }

    .trakt-item:focus {
        outline: 2px solid #ec4899;
        border-radius: 8px;
    }

    @media (max-width: 640px) {
        .movie-details-content {
            flex-direction: column;
        }

        .movie-details-poster {
            width: 120px;
            height: 180px;
            margin: 0 auto 1rem;
        }
    }

    .search-loading-indicator {
        position: absolute;
        right: 120px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
    }

    .search-clear-button {
        position: absolute;
        right: 120px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--text-secondary);
        font-size: 20px;
        cursor: pointer;
        padding: 0 10px;
    }

    /* Content type selection styles */
    .content-type-selection {
        margin: 20px 0;
    }

    .content-type-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .content-type-header h3 {
        font-size: 24px;
        margin-bottom: 8px;
    }

    .content-type-header p {
        color: var(--text-secondary);
    }

    .content-type-options {
        display: flex;
        gap: 20px;
        justify-content: center;
        width: 100%;
    }

    .content-type-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 30px;
        background-color: var(--card-bg);
        border: 1px solid var(--border);
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        width: 100%;
        text-align: center;
    }

    .content-type-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        border-color: #ec4899;
    }

    .content-type-icon {
        margin-bottom: 15px;
        color: #ec4899;
    }

    .content-type-card h4 {
        font-size: 18px;
        margin-bottom: 8px;
    }

    .content-type-card p {
        color: var(--text-secondary);
        font-size: 14px;
    }

    .search-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    /* TV Show Navigation Styles */
    .navigation-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        gap: 15px;
    }

    .back-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background-color: var(--card-bg);
        border: 1px solid var(--border);
        border-radius: 20px;
        color: var(--text-secondary);
        transition: all 0.2s ease;
    }

    .back-button:hover {
        background-color: var(--item-bg);
        color: var(--text);
    }

    .tv-show-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }

    /* Season filter styles */
    .season-filter {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
    }

    .season-btn {
        padding: 8px 16px;
        background-color: var(--card-bg);
        border: 1px solid var(--border);
        border-radius: 20px;
        color: var(--text-secondary);
        transition: all 0.2s ease;
        font-size: 14px;
    }

    .season-btn:hover {
        background-color: var(--item-bg);
        color: var(--text);
    }

    .season-btn.active {
        background-color: #ec4899;
        color: white;
        border-color: #ec4899;
    }

    .episodes-count {
        margin-bottom: 15px;
        color: var(--text-secondary);
        font-size: 14px;
    }

    /* Clickable item style */
    .clickable {
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .clickable:hover {
        background-color: var(--item-bg-hover, #2a2a2a);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .empty-state, .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;
        color: var(--text-secondary);
    }

    .loading-container {
        gap: 15px;
    }

    .releases-count {
        margin: 10px 0;
        color: var(--text-secondary);
        font-size: 14px;
    }

    .pagination {
        display: flex;
        justify-content: center;
        gap: 5px;
        margin-top: 20px;
    }

    .pagination-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 36px;
        height: 36px;
        border-radius: 6px;
        background-color: var(--card-bg);
        border: 1px solid var(--border);
        color: var(--text);
        transition: all 0.2s ease;
    }

    .pagination-btn:hover:not(:disabled) {
        background-color: var(--item-bg);
    }

    .pagination-btn.active {
        background-color: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .badges {
        display: flex;
        gap: 5px;
    }

    .found-badge {
        background-color: #10b981;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        font-weight: bold;
    }

    .complete-badge {
        background-color: #ec4899;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        font-weight: bold;
        margin-left: 8px;
    }

    /* Removed unused checkbox styles */

    /* Loading overlay styles */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .loading-spinner {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: var(--card-bg);
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    }

    .loading-spinner p {
        margin-top: 15px;
        color: var(--text);
    }

    /* Video player modal styles */
    .video-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        animation: none !important;
        transform: none !important;
    }

    .video-modal {
        background-color: var(--card-bg);
        border-radius: 8px;
        width: 90%;
        max-width: 1600px;
        max-height: 90vh;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
        animation: none !important;
        transform: none !important;
    }

    .video-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid var(--border);
    }

    .video-modal-header h3 {
        margin: 0;
        font-size: 18px;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-secondary);
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .close-btn:hover {
        background-color: var(--item-bg);
        color: var(--text);
    }

    .video-container {
        width: 100%;
        padding-top: 56.25%; /* 16:9 aspect ratio */
        position: relative;
    }

    .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: black;
        animation: none !important;
        transform: none !important;
    }

    .video-controls {
        position: absolute;
        bottom: 10px;
        left: 0;
        width: 100%;
        padding: 0 20px;
        z-index: 10;
        display: flex;
        justify-content: center;
        pointer-events: none;
    }

    .subtitle-controls {
        display: flex;
        gap: 10px;
        pointer-events: auto;
    }

    .control-button {
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 5px;
        transition: background-color 0.2s;
    }

    .control-button:hover {
        background-color: rgba(0, 0, 0, 0.9);
    }

    /* Trakt.tv watchlist styles */
    .trakt-section {
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--border);
    }

    .trakt-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .trakt-title h3 {
        font-size: 1.2rem;
        font-weight: bold;
        margin: 0;
        color: var(--text);
    }

    .trakt-title p {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin: 0.25rem 0 0 0;
    }

    .refresh-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background-color: var(--item-bg);
        border: 1px solid var(--border);
        color: var(--text);
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .refresh-button:hover {
        background-color: var(--primary);
        color: #000;
    }

    .refresh-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .trakt-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .trakt-card {
        background-color: var(--card-bg);
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .trakt-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .trakt-card-inner {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .trakt-poster {
        position: relative;
        padding-top: 150%; /* 2:3 aspect ratio */
        background-color: var(--item-bg);
    }

    .trakt-poster img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .trakt-poster-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-secondary);
        background-color: var(--item-bg);
    }

    .trakt-info {
        padding: 0.75rem;
    }

    .trakt-title {
        font-size: 0.9rem;
        font-weight: 500;
        margin: 0;
        color: var(--text);
        line-height: 1.2;
    }

    .trakt-year {
        display: block;
        font-size: 0.8rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }

    .trakt-loading, .trakt-error, .trakt-empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
        color: var(--text-secondary);
    }

    .trakt-error {
        color: var(--error);
    }

    /* Animacja obrotu jest już zdefiniowana w Tailwind jako animate-spin */

    /* Hide/show elements based on screen size */
    .hide-on-desktop {
        display: flex;
    }

    .show-on-desktop {
        display: none;
    }

    @media (min-width: 1024px) {
        .hide-on-desktop {
            display: none;
        }

        .show-on-desktop {
            display: flex;
        }
    }

    @media (max-width: 768px) {
        .media-item {
            flex-direction: column;
            align-items: flex-start;
        }

        .media-actions {
            width: 100%;
            justify-content: flex-end;
            margin-top: 10px;
        }

        /* Content type cards for mobile */
        .content-type-options {
            flex-direction: column;
            align-items: center;
        }

        .content-type-card {
            width: 100%;
            max-width: 280px;
        }

        .video-modal {
            width: 100%;
            height: 100%;
            max-width: none;
            max-height: none;
            border-radius: 0;
        }

        /* Trakt grid for mobile */
        .trakt-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 0.75rem;
        }

        .trakt-info {
            padding: 0.5rem;
        }

        .trakt-title {
            font-size: 0.8rem;
        }
    }
</style>
