<script lang="ts">
    import { addMagnet, addTorrentFile, selectFiles } from '$lib/services/debridService';

    // Zmienne dla formularza
    export let magnetUrl = '';
    export let droppedFile: File | null = null;
    let torrentFile: File | null = null;
    let fileName = 'Nie wybrano pliku';
    let isSubmitting = false;
    let isDragging = false;

    // Obsługa pliku przekazanego z komponentu nadrzędnego
    $: if (droppedFile) {
        torrentFile = droppedFile;
        fileName = droppedFile.name;
        // Automatycznie dodaj torrent po otrzymaniu pliku
        handleAddTorrent();
        // Resetuj droppedFile, aby nie dodawać go ponownie
        droppedFile = null;
    }

    // Obsługa zmiany pliku
    function handleFileChange(event: Event) {
        const input = event.target as HTMLInputElement;
        if (input.files && input.files.length > 0) {
            torrentFile = input.files[0];
            fileName = torrentFile.name;
        } else {
            torrentFile = null;
            fileName = 'Nie wybrano pliku';
        }
    }

    // Obsługa przeciągania plików
    function handleDragOver(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
        isDragging = true;
    }

    function handleDragEnter(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
        isDragging = true;
    }

    function handleDragLeave(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();

        // Sprawdzamy, czy kursor opuszcza komponent, a nie tylko przechodzi między elementami
        const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
        const x = event.clientX;
        const y = event.clientY;

        // Jeśli kursor jest poza granicami komponentu, ustawiamy isDragging na false
        if (x <= rect.left || x >= rect.right || y <= rect.top || y >= rect.bottom) {
            isDragging = false;
        }
    }

    function handleDrop(event: DragEvent) {
        event.preventDefault();
        event.stopPropagation();
        isDragging = false;

        if (isSubmitting) return;

        const files = event.dataTransfer?.files;
        if (files && files.length > 0) {
            // Sprawdź czy plik ma rozszerzenie .torrent
            const file = files[0];
            if (file.name.endsWith('.torrent')) {
                torrentFile = file;
                fileName = file.name;

                // Dodaj małe opóźnienie, aby użytkownik zobaczył wizualne potwierdzenie upuszczenia pliku
                setTimeout(() => {
                    // Automatycznie dodaj torrent po upuszczeniu pliku
                    handleAddTorrent();
                }, 300);
            } else {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert('Akceptowane są tylko pliki .torrent', 'error');
                }
            }
        }
    }

    // Funkcja do dodawania torrenta
    async function handleAddTorrent() {
        if (!magnetUrl && !torrentFile) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wprowadź link magnet lub wgraj plik torrent', 'error');
            }
            return;
        }

        isSubmitting = true;

        try {
            let torrentId: string;
            console.log('[AddTorrent] Starting torrent addition process');

            if (magnetUrl) {
                // Dodaj magnet link
                console.log('[AddTorrent] Adding magnet link');
                const result = await addMagnet(magnetUrl);
                console.log('[AddTorrent] Magnet link added successfully, ID:', result.id);
                torrentId = result.id;
            } else if (torrentFile) {
                // Dodaj plik torrent
                console.log('[AddTorrent] Adding torrent file:', torrentFile.name);
                const fileData = await readFileAsArrayBuffer(torrentFile);
                console.log('[AddTorrent] File read as ArrayBuffer, size:', fileData.byteLength);

                try {
                    const result = await addTorrentFile(fileData);
                    console.log('[AddTorrent] Torrent file added successfully, ID:', result.id);
                    torrentId = result.id;
                } catch (addError) {
                    console.error('[AddTorrent] Error adding torrent file:', addError);

                    // Provide more specific error message for torrent file issues
                    if (addError instanceof Error) {
                        if (addError.message.includes('JSON')) {
                            throw new Error(`Problem z przetwarzaniem odpowiedzi serwera: ${addError.message}`);
                        } else {
                            throw addError;
                        }
                    }
                    throw addError;
                }
            } else {
                throw new Error('Brak danych do dodania');
            }

            // Wybierz wszystkie pliki
            console.log('[AddTorrent] Selecting all files for torrent ID:', torrentId);
            await selectFiles(torrentId);
            console.log('[AddTorrent] Files selected successfully');

            // Pokaż krótki komunikat o sukcesie bez przycisku OK
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification('Torrent dodany pomyślnie!', 'success');
            }

            // Resetuj formularz
            magnetUrl = '';
            torrentFile = null;
            fileName = 'Nie wybrano pliku';
            console.log('[AddTorrent] Form reset successfully');

            // Przełącz na zakładkę pobierań
            if (typeof switchTab === 'function') {
                console.log('[AddTorrent] Switching to downloads tab');
                switchTab('downloads');
            }
        } catch (error) {
            console.error('[AddTorrent] Error adding torrent:', error);

            // Extract the most relevant part of the error message
            let errorMessage = 'Nieznany błąd';
            if (error instanceof Error) {
                // Try to extract the most useful part of the error message
                const message = error.message;
                if (message.includes('API Error:')) {
                    errorMessage = 'Błąd API: ' + message.split('API Error:')[1].trim();
                } else if (message.includes('Failed to add torrent:')) {
                    errorMessage = message.split('Failed to add torrent:')[1].trim();
                } else if (message.includes('Error 500:')) {
                    errorMessage = 'Błąd serwera: ' + message.split('Error 500:')[1].trim();
                } else if (message.includes('Error 404:')) {
                    errorMessage = 'Nie znaleziono zasobu: ' + message.split('Error 404:')[1].trim();
                } else {
                    errorMessage = message;
                }
            }

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Nie udało się dodać torrenta: ' + errorMessage,
                    'error'
                );
            }
        } finally {
            isSubmitting = false;
            console.log('[AddTorrent] Process completed, isSubmitting set to false');
        }
    }

    // Funkcja do odczytu pliku jako ArrayBuffer
    function readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                if (e.target && e.target.result) {
                    console.log(`[AddTorrent] File read successfully: ${file.name}, size: ${file.size} bytes`);
                    resolve(e.target.result as ArrayBuffer);
                } else {
                    console.error(`[AddTorrent] Error reading file: ${file.name}, no result available`);
                    reject(new Error('Błąd odczytu pliku - brak danych'));
                }
            };

            reader.onerror = (e) => {
                console.error(`[AddTorrent] Error reading file: ${file.name}`, e);
                reject(new Error(`Błąd odczytu pliku: ${e.target?.error?.message || 'Nieznany błąd'}`));
            };

            reader.onabort = () => {
                console.error(`[AddTorrent] File reading aborted: ${file.name}`);
                reject(new Error('Odczyt pliku został przerwany'));
            };

            try {
                console.log(`[AddTorrent] Starting to read file: ${file.name}, size: ${file.size} bytes`);
                reader.readAsArrayBuffer(file);
            } catch (error) {
                console.error(`[AddTorrent] Exception while reading file: ${file.name}`, error);
                reject(new Error(`Wyjątek podczas odczytu pliku: ${error instanceof Error ? error.message : 'Nieznany błąd'}`));
            }
        });
    }

    // Funkcja do przełączania zakładek (będzie przekazana z komponentu nadrzędnego)
    export let switchTab: (tab: string) => void;
</script>

<div class="card {isDragging ? 'dragging' : ''}"
     on:dragover={handleDragOver}
     on:dragenter={handleDragEnter}
     on:dragleave={handleDragLeave}
     on:drop={handleDrop}
     role="region"
     aria-label="Strefa dodawania pliku torrent">
     <div class="dropzone-message" class:hidden={!isDragging}>
        <p>Upuść plik .torrent tutaj</p>
     </div>
    <h2>Dodaj Torrent</h2>

    <div class="input-group" style="margin-top: 20px;">
        <label for="magnetUrl">Link Magnet</label>
        <input type="text" id="magnetUrl" class="magnet-input"
               placeholder="Wklej link magnet tutaj"
               bind:value={magnetUrl}
               disabled={isSubmitting}>
    </div>

    <div class="input-group">
        <label for="torrentFile">Plik Torrent</label>
        <div class="file-input-container">
            <div class="file-input-btn">Wybierz plik</div>
            <input type="file" id="torrentFile" accept=".torrent"
                   on:change={handleFileChange}
                   disabled={isSubmitting}>
        </div>
        <span class="file-name">{fileName}</span>
    </div>

    <button class="btn btn-primary"
            style="margin-top: 20px;"
            on:click={handleAddTorrent}
            disabled={isSubmitting}>
        {#if isSubmitting}
            Dodawanie...<div class="loading"></div>
        {:else}
            Dodaj do pobierania
        {/if}
    </button>
</div>
