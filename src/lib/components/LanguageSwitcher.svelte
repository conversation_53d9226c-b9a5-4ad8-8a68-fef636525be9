<script lang="ts">
    import { Globe } from 'lucide-svelte';
    import { getLocale, setLocale } from '$lib/paraglide/runtime';
    import { slide } from 'svelte/transition';
    import { onMount, onDestroy } from 'svelte';

    /**
     * Whether to show the language name
     */
    export let showName: boolean = true;

    /**
     * Whether to show the icon
     */
    export let showIcon: boolean = true;

    /**
     * Whether to use a dropdown menu
     */
    export let useDropdown: boolean = true;

    /**
     * Custom class for the language switcher
     */
    export let className: string = '';

    // Available locales
    const locales = ['en', 'pl'];

    // Language names
    const languageNames: Record<string, string> = {
        'en': 'English',
        'pl': '<PERSON>ski'
    };

    // State for dropdown
    let isOpen = false;

    // Toggle dropdown
    function toggleDropdown() {
        isOpen = !isOpen;
    }

    // Close dropdown when clicking outside
    function handleClickOutside(event: MouseEvent) {
        const target = event.target as HTMLElement;
        const dropdown = document.querySelector('.language-dropdown');

        if (dropdown && !dropdown.contains(target) && isOpen) {
            isOpen = false;
        }
    }

    // Switch language
    function switchLanguage(lang: string) {
        setLocale(lang as "en" | "pl");

        // Store the selected language in localStorage
        if (typeof window !== 'undefined') {
            localStorage.setItem('paraglide-locale', lang);

            // Update the HTML lang attribute
            document.documentElement.lang = lang;
        }

        isOpen = false;
    }

    // Add click outside listener
    onMount(() => {
        document.addEventListener('click', handleClickOutside);
    });

    onDestroy(() => {
        document.removeEventListener('click', handleClickOutside);
    });
</script>

{#if useDropdown}
    <div class="language-dropdown {className}">
        <button
            class="language-button"
            on:click={toggleDropdown}
            aria-haspopup="true"
            aria-expanded={isOpen}
        >
            {#if showIcon}
                <Globe size={18} />
            {/if}

            {#if showName}
                <span>{languageNames[getLocale()] || getLocale()}</span>
            {/if}

            <span class="dropdown-arrow" class:open={isOpen}>▼</span>
        </button>

        {#if isOpen}
            <div
                class="dropdown-menu"
                transition:slide={{ duration: 150 }}
                role="menu"
            >
                {#each locales as lang}
                    <button
                        class="dropdown-item {getLocale() === lang ? 'active' : ''}"
                        on:click={() => switchLanguage(lang)}
                        role="menuitem"
                    >
                        {languageNames[lang] || lang}
                    </button>
                {/each}
            </div>
        {/if}
    </div>
{:else}
    <div class="language-buttons {className}">
        {#each locales as lang}
            <button
                class="language-button {getLocale() === lang ? 'active' : ''}"
                on:click={() => switchLanguage(lang)}
                aria-pressed={getLocale() === lang}
            >
                {#if showIcon && getLocale() === lang}
                    <Globe size={18} />
                {/if}

                {#if showName}
                    <span>{languageNames[lang] || lang}</span>
                {/if}
            </button>
        {/each}
    </div>
{/if}

<style>
    .language-dropdown {
        position: relative;
        display: inline-block;
    }

    .language-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .language-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background-color: var(--card-bg, #1e1e2e);
        border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        border-radius: 0.25rem;
        color: var(--text-secondary, #a6adc8);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .language-button:hover {
        background-color: var(--card-bg-hover, #313244);
        color: var(--text, #cdd6f4);
    }

    .language-button.active {
        background-color: var(--primary-color-light, rgba(74, 108, 247, 0.1));
        color: var(--primary-color, #4a6cf7);
        border-color: var(--primary-color, #4a6cf7);
    }

    .dropdown-arrow {
        font-size: 0.6rem;
        margin-left: 0.25rem;
        transition: transform 0.2s ease;
    }

    .dropdown-arrow.open {
        transform: rotate(180deg);
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        margin-top: 0.25rem;
        background-color: var(--card-bg, #1e1e2e);
        border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        border-radius: 0.25rem;
        min-width: 8rem;
        z-index: 50;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .dropdown-item {
        display: block;
        width: 100%;
        padding: 0.5rem 1rem;
        text-align: left;
        background: none;
        border: none;
        color: var(--text-secondary, #a6adc8);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .dropdown-item:hover {
        background-color: var(--card-bg-hover, #313244);
        color: var(--text, #cdd6f4);
    }

    .dropdown-item.active {
        background-color: var(--primary-color-light, rgba(74, 108, 247, 0.1));
        color: var(--primary-color, #4a6cf7);
    }
</style>
