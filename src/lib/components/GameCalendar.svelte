<script lang="ts">
    import { onMount } from 'svelte';
    import { getRecentGames, getUpcomingGames, getGameById, getImageUrl, type IGDBGame } from '$lib/services/igdbService';
    import Modal from '$lib/components/Modal.svelte';
    import { Search } from 'lucide-svelte';

    // State variables
    let isLoading = true;
    let recentGames: IGDBGame[] = [];
    let upcomingGames: IGDBGame[] = [];
    let errorMessage = '';
    let activeTab: 'recent' | 'upcoming' = 'recent';

    // Modal state
    let showGameModal = false;
    let selectedGame: IGDBGame | null = null;
    let isLoadingGameDetails = false;

    // Load data on mount
    onMount(async () => {
        console.log('GameCalendar: Component mounted');
        await loadGames();
    });

    // Function to load games
    async function loadGames() {
        console.log('GameCalendar: loadGames() called');
        isLoading = true;
        errorMessage = '';

        try {
            // Try to load recent games first
            console.log('GameCalendar: Loading recent games...');
            try {
                recentGames = await getRecentGames(50);
                console.log('GameCalendar: Recent games loaded:', recentGames);
                console.log('GameCalendar: Recent games count:', recentGames.length);
            } catch (recentError) {
                console.error('GameCalendar: Error loading recent games:', recentError);
                recentGames = [];
            }

            // Then try to load upcoming games
            console.log('GameCalendar: Loading upcoming games...');
            try {
                upcomingGames = await getUpcomingGames(50);
                console.log('GameCalendar: Upcoming games loaded:', upcomingGames);
                console.log('GameCalendar: Upcoming games count:', upcomingGames.length);
            } catch (upcomingError) {
                console.error('GameCalendar: Error loading upcoming games:', upcomingError);
                upcomingGames = [];
            }

            // Log grouped games for debugging
            console.log('GameCalendar: Recent games by date:', recentGamesByDate);
            console.log('GameCalendar: Recent dates count:', recentDates.length);
            console.log('GameCalendar: Recent dates:', recentDates);
            console.log('GameCalendar: Upcoming games by date:', upcomingGamesByDate);
            console.log('GameCalendar: Upcoming dates count:', upcomingDates.length);
            console.log('GameCalendar: Upcoming dates:', upcomingDates);

            // Check if we have any data
            if (recentGames.length === 0 && upcomingGames.length === 0) {
                errorMessage = 'Nie udało się załadować danych o grach. Spróbuj ponownie później.';
            }
        } catch (error) {
            console.error('GameCalendar: Error loading games:', error);
            errorMessage = error instanceof Error ? error.message : 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Error loading games: ' + errorMessage,
                    'error'
                );
            }
        } finally {
            isLoading = false;
        }
    }

    // Function to open game details modal
    async function openGameDetails(game: IGDBGame) {
        isLoadingGameDetails = true;
        showGameModal = true;

        try {
            // Get full game details
            const gameDetails = await getGameById(game.id);
            if (gameDetails) {
                selectedGame = gameDetails;
            } else {
                // If we can't get full details, use the search result
                selectedGame = game;
            }
        } catch (error) {
            console.error('Error loading game details:', error);
            // If we can't get full details, use the search result
            selectedGame = game;
        } finally {
            isLoadingGameDetails = false;
        }
    }

    // Function to close game details modal
    function closeGameModal() {
        showGameModal = false;
        selectedGame = null;
    }

    // Function to redirect to ZOO search
    function searchInZoo() {
        if (selectedGame && typeof window !== 'undefined') {
            // Get the setActiveTab function from the parent component
            const setActiveTab = (window as any).setActiveTab;
            if (typeof setActiveTab === 'function') {
                // Zapisz nazwę gry przed przełączeniem zakładki
                const gameName = selectedGame.name;
                console.log(`GameCalendar: Searching for game "${gameName}" in ZOO`);

                // Spróbuj użyć właściwości zooSearchQuery na obiekcie window
                if ('zooSearchQuery' in window) {
                    console.log('GameCalendar: Using window.zooSearchQuery property');
                    // Ustaw wartość właściwości zooSearchQuery
                    (window as any).zooSearchQuery = gameName;
                } else {
                    console.log('GameCalendar: window.zooSearchQuery property not found, using sessionStorage');
                    // Alternatywnie, zapisz zapytanie w sessionStorage
                    sessionStorage.setItem('zooSearchQuery', gameName);
                }

                // Przełącz na zakładkę ZOO
                setActiveTab('zoo');

                // Dodaj większe opóźnienie, aby upewnić się, że komponent ZooSearch jest już zamontowany
                setTimeout(() => {
                    try {
                        // Znajdź pole wyszukiwania i ustaw jego wartość
                        const searchInput = document.getElementById('zooSearchQuery') as HTMLInputElement;
                        if (searchInput) {
                            console.log('GameCalendar: Found #zooSearchQuery input, setting value');
                            // Ustaw wartość pola wyszukiwania
                            searchInput.value = gameName;

                            // Wywołaj zdarzenie input, aby uruchomić wyszukiwanie
                            const inputEvent = new Event('input', { bubbles: true });
                            searchInput.dispatchEvent(inputEvent);

                            // Opcjonalnie: wywołaj zdarzenie keypress z klawiszem Enter, aby natychmiast rozpocząć wyszukiwanie
                            const keypressEvent = new KeyboardEvent('keypress', { key: 'Enter', bubbles: true });
                            searchInput.dispatchEvent(keypressEvent);
                        } else {
                            console.log('GameCalendar: #zooSearchQuery input not found, trying again in 200ms');
                            // Spróbuj ponownie z większym opóźnieniem
                            setTimeout(() => {
                                const searchInput = document.getElementById('zooSearchQuery') as HTMLInputElement;
                                if (searchInput) {
                                    console.log('GameCalendar: Found #zooSearchQuery input on second attempt');
                                    searchInput.value = gameName;
                                    const inputEvent = new Event('input', { bubbles: true });
                                    searchInput.dispatchEvent(inputEvent);
                                    const keypressEvent = new KeyboardEvent('keypress', { key: 'Enter', bubbles: true });
                                    searchInput.dispatchEvent(keypressEvent);
                                } else {
                                    console.error('GameCalendar: Nie znaleziono pola wyszukiwania #zooSearchQuery po dwóch próbach');
                                }
                            }, 200);
                        }
                    } catch (error) {
                        console.error('GameCalendar: Error setting search query:', error);
                    }
                }, 300);
            } else {
                console.error('GameCalendar: setActiveTab function not found');
            }
        }
    }

    // Function to format date
    function formatDate(timestamp?: number): string {
        if (!timestamp) return 'Nieznana data';

        try {
            const date = new Date(timestamp * 1000); // Convert Unix timestamp to milliseconds
            return date.toLocaleDateString('pl-PL', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (e) {
            return 'Nieznana data';
        }
    }

    // Function to get day of week
    function getDayOfWeek(timestamp?: number): string {
        if (!timestamp) return '';

        try {
            const date = new Date(timestamp * 1000); // Convert Unix timestamp to milliseconds
            return date.toLocaleDateString('pl-PL', {
                weekday: 'long'
            });
        } catch (e) {
            return '';
        }
    }

    // Function to group games by date
    function groupGamesByDate(games: IGDBGame[]): Map<string, IGDBGame[]> {
        const groupedGames = new Map<string, IGDBGame[]>();

        games.forEach(game => {
            if (game.first_release_date) {
                // Convert Unix timestamp to YYYY-MM-DD format
                const date = new Date(game.first_release_date * 1000).toISOString().split('T')[0];
                if (!groupedGames.has(date)) {
                    groupedGames.set(date, []);
                }
                groupedGames.get(date)?.push(game);
            }
        });

        return groupedGames;
    }

    // Get grouped games
    $: recentGamesByDate = groupGamesByDate(recentGames);
    $: upcomingGamesByDate = groupGamesByDate(upcomingGames);

    // Sort dates
    $: recentDates = [...recentGamesByDate.keys()].sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
    $: upcomingDates = [...upcomingGamesByDate.keys()].sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

    // Function to get website category name
    function getWebsiteCategory(category: number): string {
        const categories = {
            1: 'Oficjalna',
            2: 'Wikia',
            3: 'Wikipedia',
            4: 'Facebook',
            5: 'Twitter',
            6: 'Twitch',
            8: 'Instagram',
            9: 'YouTube',
            10: 'iPhone',
            11: 'iPad',
            12: 'Android',
            13: 'Steam',
            14: 'Reddit',
            15: 'Itch',
            16: 'Epic Games',
            17: 'GOG',
            18: 'Discord'
        };

        return categories[category as keyof typeof categories] || 'Strona';
    }
</script>

<div class="w-full m-0 p-6g game-calendar-container card">
    <h1 class="card-title">Kalendarz premier gier</h1>

    <!-- Loading indicator -->
    {#if isLoading}
        <div class="loading-container">
            <div class="loading"></div>
        </div>
    {:else if errorMessage}
        <div class="error-message">
            {errorMessage}
        </div>
    {:else}
        <!-- Tabs navigation -->
        <div class="tabs-container">
            <button
                class="tab-button {activeTab === 'recent' ? 'active' : ''}"
                on:click={() => activeTab = 'recent'}
            >
                Ostatnio wydane
            </button>
            <button
                class="tab-button {activeTab === 'upcoming' ? 'active' : ''}"
                on:click={() => activeTab = 'upcoming'}
            >
                Nadchodzące premiery
            </button>
        </div>

        <div class="calendar-sections">
            <!-- Recent games section -->
            {#if activeTab === 'recent'}
                {#if recentDates.length === 0}
                    <div class="empty-message">
                        Nie znaleziono najnowszych gier PC.
                    </div>
                {:else}
                    <div class="calendar-list">
                        {#each recentDates as date}
                            <div class="calendar-date">
                                <div class="date-header">
                                    <span class="date-day">{formatDate(new Date(date).getTime() / 1000)}</span>
                                    <span class="date-weekday">{getDayOfWeek(new Date(date).getTime() / 1000)}</span>
                                </div>
                                <div class="date-games">
                                    {#each recentGamesByDate.get(date) || [] as game}
                                        <div
                                            class="game-item"
                                            on:click={() => openGameDetails(game)}
                                            on:keydown={(e) => e.key === 'Enter' && openGameDetails(game)}
                                            role="button"
                                            tabindex="0"
                                        >
                                            <div class="game-image-container">
                                                {#if game.cover && game.cover.image_id}
                                                    <img src={getImageUrl(game.cover.image_id, 'cover_small')} alt={game.name} class="game-image" />
                                                {:else}
                                                    <div class="no-image">
                                                        <span>No image</span>
                                                    </div>
                                                {/if}
                                            </div>
                                            <div class="game-info">
                                                <h3 class="game-title">{game.name}</h3>
                                                {#if game.summary}
                                                    <p class="game-description">{game.summary}</p>
                                                {/if}
                                            </div>
                                        </div>
                                    {/each}
                                </div>
                            </div>
                        {/each}
                    </div>
                {/if}
            {/if}

            <!-- Upcoming games section -->
            {#if activeTab === 'upcoming'}
                {#if upcomingDates.length === 0}
                    <div class="empty-message">
                        Nie znaleziono nadchodzących gier PC.
                    </div>
                {:else}
                    <div class="calendar-list">
                        {#each upcomingDates as date}
                            <div class="calendar-date">
                                <div class="date-header">
                                    <span class="date-day">{formatDate(new Date(date).getTime() / 1000)}</span>
                                    <span class="date-weekday">{getDayOfWeek(new Date(date).getTime() / 1000)}</span>
                                </div>
                                <div class="date-games">
                                    {#each upcomingGamesByDate.get(date) || [] as game}
                                        <div
                                            class="game-item"
                                            on:click={() => openGameDetails(game)}
                                            on:keydown={(e) => e.key === 'Enter' && openGameDetails(game)}
                                            role="button"
                                            tabindex="0"
                                        >
                                            <div class="game-image-container">
                                                {#if game.cover && game.cover.image_id}
                                                    <img src={getImageUrl(game.cover.image_id, 'cover_small')} alt={game.name} class="game-image" />
                                                {:else}
                                                    <div class="no-image">
                                                        <span>No image</span>
                                                    </div>
                                                {/if}
                                            </div>
                                            <div class="game-info">
                                                <h3 class="game-title">{game.name}</h3>
                                                {#if game.summary}
                                                    <p class="game-description">{game.summary}</p>
                                                {/if}
                                            </div>
                                        </div>
                                    {/each}
                                </div>
                            </div>
                        {/each}
                    </div>
                {/if}
            {/if}
        </div>
    {/if}

    <!-- Game details modal -->
    <Modal
        open={showGameModal}
        title={selectedGame?.name || 'Szczegóły gry'}
        showClose={true}
        size="lg"
        on:close={closeGameModal}
    >
        {#if isLoadingGameDetails}
            <div class="loading-container">
                <div class="loading"></div>
            </div>
        {:else if selectedGame}
        <div class="w-full p-0 m-0 mb-8 game-details-actions">
            <button class="w-full text-center btn btn-primary" on:click={searchInZoo}>
                <Search size={18} />
                Szukaj w ZOO
            </button>
        </div>
            <div class="game-details">
                <div class="game-details-image">
                    {#if selectedGame.cover && selectedGame.cover.image_id}
                        <img src={getImageUrl(selectedGame.cover.image_id, 'cover_big')} alt={selectedGame.name} />
                    {:else}
                        <div class="no-image large">
                            <span>No image</span>
                        </div>
                    {/if}
                </div>

                <div class="game-details-info">
                    <p class="game-release-date">
                        <strong>Data wydania:</strong> {formatDate(selectedGame.first_release_date)}
                    </p>

                    {#if selectedGame.rating || selectedGame.total_rating}
                        <div class="game-rating">
                            <strong>Ocena:</strong>
                            {#if selectedGame.total_rating}
                                {Math.round(selectedGame.total_rating)}% (łącznie)
                            {:else if selectedGame.rating}
                                {Math.round(selectedGame.rating)}% (krytycy)
                            {/if}
                            {#if selectedGame.rating_count || selectedGame.total_rating_count}
                                <span class="rating-count">
                                    ({selectedGame.total_rating_count || selectedGame.rating_count} ocen)
                                </span>
                            {/if}
                        </div>
                    {/if}

                    {#if selectedGame.summary}
                        <p class="game-deck">{selectedGame.summary}</p>
                    {/if}

                    {#if selectedGame.storyline}
                        <div class="game-description">
                            <p>{selectedGame.storyline}</p>
                        </div>
                    {/if}

                    <div class="game-details-grid">
                        {#if selectedGame.genres && selectedGame.genres.length > 0}
                            <div class="game-detail-item">
                                <strong>Gatunki:</strong> {selectedGame.genres.map(g => g.name).join(', ')}
                            </div>
                        {/if}

                        {#if selectedGame.themes && selectedGame.themes.length > 0}
                            <div class="game-detail-item">
                                <strong>Motywy:</strong> {selectedGame.themes.map(t => t.name).join(', ')}
                            </div>
                        {/if}

                        {#if selectedGame.game_modes && selectedGame.game_modes.length > 0}
                            <div class="game-detail-item">
                                <strong>Tryby gry:</strong> {selectedGame.game_modes.map(m => m.name).join(', ')}
                            </div>
                        {/if}

                        {#if selectedGame.involved_companies && selectedGame.involved_companies.length > 0}
                            <div class="game-detail-item">
                                <strong>Twórcy:</strong>
                                {selectedGame.involved_companies
                                    .filter(ic => ic.developer)
                                    .map(ic => ic.company.name)
                                    .join(', ')}
                            </div>
                            <div class="game-detail-item">
                                <strong>Wydawca:</strong>
                                {selectedGame.involved_companies
                                    .filter(ic => ic.publisher)
                                    .map(ic => ic.company.name)
                                    .join(', ')}
                            </div>
                        {/if}
                    </div>

                    {#if selectedGame.videos && selectedGame.videos.length > 0}
                        <div class="game-videos">
                            <h3>Zwiastuny</h3>
                            <div class="video-container">
                                <iframe
                                    width="100%"
                                    height="315"
                                    src="https://www.youtube.com/embed/{selectedGame.videos[0].video_id}"
                                    title="{selectedGame.videos[0].name}"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen>
                                </iframe>
                            </div>
                        </div>
                    {/if}

                    {#if selectedGame.websites && selectedGame.websites.length > 0}
                        <div class="game-websites">
                            <h3>Strony internetowe</h3>
                            <div class="website-links">
                                {#each selectedGame.websites as website}
                                    <a href={website.url} target="_blank" rel="noopener noreferrer" class="website-link">
                                        {getWebsiteCategory(website.category)}
                                    </a>
                                {/each}
                            </div>
                        </div>
                    {/if}
                </div>
            </div>
        {/if}
    </Modal>
</div>

<style>
    .game-calendar-container {
        width: 100%;
        //max-width: 1200px;
        margin: 0 auto;
    }

    .card-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        color: #f8f8f8;
    }

    .calendar-sections {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .tabs-container {
        display: flex;
        margin-bottom: 1.5rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .tab-button {
        padding: 0.75rem 1.5rem;
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
    }

    .tab-button.active {
        color: #ec4899;
    }

    .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #ec4899;
    }

    .tab-button:hover {
        color: white;
    }

    .calendar-list {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        /* Usunięto ograniczenie wysokości i przewijanie */
    }

    .calendar-date {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .date-header {
        display: flex;
        flex-direction: column;
        background-color: rgba(20, 20, 20, 0.5);
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        border-left: 3px solid #ec4899;
    }

    .date-day {
        font-weight: 600;
        font-size: 1rem;
        color: #f8f8f8;
    }

    .date-weekday {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.7);
        text-transform: capitalize;
    }

    .date-games {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding-left: 1rem;
        /* Usunięto ograniczenie wysokości i przewijanie */
    }

    .game-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem;
        border-radius: 0.375rem;
        background-color: rgba(40, 40, 40, 0.5);
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .game-item:hover {
        background-color: rgba(60, 60, 60, 0.7);
    }

    .game-image-container {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        border-radius: 0.25rem;
        overflow: hidden;
        background-color: #333;
    }

    .game-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #333;
        color: #999;
        font-size: 0.75rem;
    }

    .no-image.large {
        height: 200px;
        font-size: 1rem;
    }

    .game-info {
        flex: 1;
        min-width: 0;
    }

    .game-title {
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        color: #f8f8f8;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .game-description {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }

    .loading {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #ec4899;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    .error-message {
        padding: 1rem;
        background-color: rgba(239, 68, 68, 0.2);
        border-left: 3px solid #ef4444;
        color: #f8f8f8;
        border-radius: 0.25rem;
    }

    .empty-message {
        padding: 1rem;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
    }

    /* Game details modal styles */
    .game-details {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    @media (min-width: 768px) {
        .game-details {
            flex-direction: row;
        }
    }

    .game-details-image {
        flex-shrink: 0;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .game-details-image img {
        width: 100%;
        border-radius: 0.375rem;
    }

    .game-details-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .game-release-date {
        font-size: 1rem;
        margin: 0;
    }

    .game-deck {
        font-size: 1.125rem;
        margin: 0;
        line-height: 1.5;
    }

    .game-description {
        font-size: 0.875rem;
        line-height: 1.6;
        color: rgba(255, 255, 255, 0.9);
        max-height: 300px;
        overflow-y: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    /* Ukryj pasek przewijania dla Chrome, Safari i Opera */
    .game-description::-webkit-scrollbar {
        display: none;
    }

    .game-rating {
        margin: 0.5rem 0;
        font-size: 1rem;
        color: #f8f8f8;
    }

    .rating-count {
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.7);
        margin-left: 0.25rem;
    }

    .game-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .game-detail-item {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .game-videos {
        margin: 1.5rem 0;
    }

    .game-videos h3 {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
        color: #f8f8f8;
    }

    .video-container {
        position: relative;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        height: 0;
        overflow: hidden;
        border-radius: 0.375rem;
    }

    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
    }

    .game-websites {
        margin: 1.5rem 0;
    }

    .game-websites h3 {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
        color: #f8f8f8;
    }

    .website-links {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .website-link {
        display: inline-block;
        padding: 0.5rem 0.75rem;
        background-color: rgba(40, 40, 40, 0.7);
        color: #f8f8f8;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        text-decoration: none;
        transition: background-color 0.2s ease;
    }

    .website-link:hover {
        background-color: rgba(60, 60, 60, 0.9);
        color: #ec4899;
    }

    .game-details-actions {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
    }

    .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-primary {
        background-color: #ec4899;
        color: white;
    }

    .btn-primary:hover {
        background-color: #db2777;
    }

    /* Usunięto nieużywane style btn-outline */
</style>
