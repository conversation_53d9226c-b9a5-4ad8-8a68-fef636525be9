<script lang="ts">
    import { onMount } from 'svelte';
    import { Eye, Plus, Trash2, RefreshCw, Download, AlertCircle } from 'lucide-svelte';
    import { getWithAuth, fetchWithAuth } from '$lib/utils/apiUtils';

    // State variables
    let watchedItems: WatchedItem[] = [];
    let newQuery = '';
    let isLoading = true;
    let isAdding = false;
    let isChecking = false;
    let error: string | null = null;
    let itemsWithNewResults: string[] = [];

    // Interface for watched items
    interface WatchedItem {
        id: string;
        query: string;
        dateAdded: string;
        lastChecked: string | null;
        lastNotified: string | null;
        hasResults: boolean;
    }

    // Interface for search results
    interface SearchResult {
        title: string;
        source: string;
        magnetLink?: string;
        fileSize?: string;
        uploadDate?: string;
        coverUrl?: string;
        description?: string;
        url?: string;
        links?: any[];
    }

    // Interface for check results
    interface CheckResult {
        item: WatchedItem;
        results: SearchResult[];
    }

    // Load watched items on component mount
    onMount(async () => {
        await loadWatchedItems();

        // Focus na pole wyszukiwania
        setTimeout(() => {
            const input = document.querySelector('.add-query-form input[type="text"]');
            if (input) {
                (input as HTMLInputElement).focus();
            }
        }, 100);
    });

    // Function to load watched items
    async function loadWatchedItems() {
        isLoading = true;
        error = null;

        try {
            const response = await getWithAuth('/api/watch');

            if (!response.ok) {
                throw new Error(`Error loading watched items: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                watchedItems = data.items || [];

                // Check for items with new results (items that have results but haven't been notified)
                itemsWithNewResults = watchedItems
                    .filter(item =>
                        item.hasResults &&
                        (!item.lastNotified ||
                         (item.lastChecked && item.lastNotified &&
                          new Date(item.lastNotified) < new Date(item.lastChecked)))
                    )
                    .map(item => item.id);

                // Update the exported count
                newResultsCount = itemsWithNewResults.length;
            } else {
                throw new Error(data.message || 'Failed to load watched items');
            }
        } catch (err) {
            console.error('Error loading watched items:', err);
            error = err instanceof Error ? err.message : 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Nie udało się załadować śledzonych zapytań: ' + error, 'error');
            }
        } finally {
            isLoading = false;
        }
    }

    // Function to add a new watched item
    async function addWatchedItem() {
        if (!newQuery || newQuery.trim().length < 3) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Zapytanie musi mieć co najmniej 3 znaki', 'error');
            }
            return;
        }

        isAdding = true;
        error = null;

        try {
            const response = await fetchWithAuth('/api/watch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query: newQuery.trim() })
            });

            if (!response.ok) {
                const data = await response.json();
                throw new Error(data.message || `Error adding watched item: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // Add the new item to the list
                if (data.item) {
                    watchedItems = [...watchedItems, data.item];
                }

                // Clear the input
                newQuery = '';

                if (typeof window !== 'undefined' && (window as any).showNotification) {
                    (window as any).showNotification('Dodano nowe zapytanie do śledzenia', 'success');
                }
            } else {
                throw new Error(data.message || 'Failed to add watched item');
            }
        } catch (err) {
            console.error('Error adding watched item:', err);
            error = err instanceof Error ? err.message : 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Nie udało się dodać zapytania: ' + error, 'error');
            }
        } finally {
            isAdding = false;
        }
    }

    // Function to remove a watched item
    async function removeWatchedItem(id: string) {
        if (typeof window !== 'undefined' && (window as any).showConfirm) {
            (window as any).showConfirm('Czy na pewno chcesz usunąć to zapytanie?', async () => {
                try {
                    const response = await fetchWithAuth(`/api/watch?id=${id}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        const data = await response.json();
                        throw new Error(data.message || `Error removing watched item: ${response.status}`);
                    }

                    const data = await response.json();

                    if (data.success) {
                        // Remove the item from the list
                        watchedItems = watchedItems.filter(item => item.id !== id);

                        if (typeof window !== 'undefined' && (window as any).showNotification) {
                            (window as any).showNotification('Usunięto zapytanie', 'success');
                        }
                    } else {
                        throw new Error(data.message || 'Failed to remove watched item');
                    }
                } catch (err) {
                    console.error('Error removing watched item:', err);
                    error = err instanceof Error ? err.message : 'Unknown error';

                    if (typeof window !== 'undefined' && (window as any).showAlert) {
                        (window as any).showAlert('Nie udało się usunąć zapytania: ' + error, 'error');
                    }
                }
            });
        }
    }

    // Function to check for new results
    async function checkForNewResults() {
        isChecking = true;
        error = null;

        try {
            const response = await getWithAuth('/api/watch/check?manual=true');

            if (!response.ok) {
                throw new Error(`Error checking for new results: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // Refresh the watched items list
                await loadWatchedItems();

                // Store items with new results
                itemsWithNewResults = data.newResults.map((result: CheckResult) => result.item.id);

                if (data.newResults.length > 0) {
                    if (typeof window !== 'undefined' && (window as any).showNotification) {
                        (window as any).showNotification(`Znaleziono nowe wyniki dla ${data.newResults.length} zapytań!`, 'success');
                    }
                } else {
                    if (typeof window !== 'undefined' && (window as any).showNotification) {
                        (window as any).showNotification('Nie znaleziono nowych wyników', 'info');
                    }
                }
            } else {
                throw new Error(data.message || 'Failed to check for new results');
            }
        } catch (err) {
            console.error('Error checking for new results:', err);
            error = err instanceof Error ? err.message : 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Nie udało się sprawdzić nowych wyników: ' + error, 'error');
            }
        } finally {
            isChecking = false;
        }
    }

    // Function to mark an item as notified
    async function markAsNotified(id: string) {
        try {
            const response = await fetchWithAuth('/api/watch/notify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id })
            });

            if (!response.ok) {
                throw new Error(`Error marking item as notified: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                // Update the item in the list
                watchedItems = watchedItems.map(item =>
                    item.id === id ? { ...item, lastNotified: new Date().toISOString() } : item
                );

                // Remove from items with new results
                itemsWithNewResults = itemsWithNewResults.filter(itemId => itemId !== id);
            } else {
                throw new Error(data.message || 'Failed to mark item as notified');
            }
        } catch (err) {
            console.error('Error marking item as notified:', err);
            // Don't show an alert for this error, just log it
        }
    }

    // Function to search for the query
    function searchQuery(query: string) {
        // Set the query in the parent component and switch to the search tab
        if (typeof window !== 'undefined') {
            // Store the query in sessionStorage
            sessionStorage.setItem('zooSearchQuery', query);

            // Switch to the zoo tab
            if (typeof switchTab === 'function') {
                switchTab('zoo');

                // Set a small timeout to ensure the ZooSearch component is mounted
                setTimeout(() => {
                    // Find the search input and set its value
                    const searchInput = document.getElementById('zooSearchQuery') as HTMLInputElement;
                    if (searchInput) {
                        searchInput.value = query;

                        // Trigger the search by creating and dispatching an input event
                        const inputEvent = new Event('input', { bubbles: true });
                        searchInput.dispatchEvent(inputEvent);
                    }
                }, 100);
            }
        }
    }

    // Function to format date
    function formatDate(dateString: string | null): string {
        if (!dateString) return 'Nigdy';

        try {
            const date = new Date(dateString);
            return date.toLocaleString('pl-PL', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (err) {
            return 'Nieprawidłowa data';
        }
    }

    // Function to handle key press in input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && newQuery.trim().length >= 3) {
            addWatchedItem();
        }
    }

    // Props
    export let switchTab: (tab: string) => void;

    // Export the count of items with new results
    export let newResultsCount = 0;

    // Update the count whenever itemsWithNewResults changes
    $: {
        newResultsCount = itemsWithNewResults.length;
    }
</script>

<div class="watch-list-container">
    <header>
        <h1>Śledzone zapytania</h1>
        <p class="description">
            Dodaj zapytania, które chcesz śledzić. System będzie codziennie sprawdzał, czy pojawiły się nowe wyniki.
        </p>
    </header>

    <div class="card">
        <div class="add-query-form">
            <div class="input-group">
                <input
                    type="text"
                    bind:value={newQuery}
                    placeholder="Wpisz zapytanie do śledzenia..."
                    on:keypress={handleKeyPress}
                    disabled={isAdding}
                />
                <button
                    class="btn btn-primary"
                    on:click={addWatchedItem}
                    disabled={isAdding || newQuery.trim().length < 3}
                >
                    {#if isAdding}
                        <RefreshCw size={18} class="spin" />
                    {:else}
                        <Plus size={18} />
                    {/if}
                    Dodaj
                </button>
            </div>
            <p class="hint">Wpisz co najmniej 3 znaki. System będzie sprawdzał nowe wyniki codziennie o 22:00.</p>
        </div>
    </div>

    <div class="card actions-card">
        <button
            class="btn btn-outline"
            on:click={checkForNewResults}
            disabled={isChecking || isLoading}
        >
            {#if isChecking}
                <RefreshCw size={18} class="spin" />
            {:else}
                <RefreshCw size={18} />
            {/if}
            Sprawdź teraz
        </button>
    </div>

    {#if isLoading}
        <div class="loading-container">
            <RefreshCw size={24} class="spin" />
            <p>Ładowanie śledzonych zapytań...</p>
        </div>
    {:else if error}
        <div class="error-container">
            <AlertCircle size={24} />
            <p>{error}</p>
            <button class="btn btn-outline" on:click={loadWatchedItems}>Spróbuj ponownie</button>
        </div>
    {:else if watchedItems.length === 0}
        <div class="empty-container">
            <Eye size={48} class="empty-icon" />
            <p>Nie masz jeszcze żadnych śledzonych zapytań</p>
            <p class="empty-hint">Dodaj zapytanie powyżej, aby rozpocząć śledzenie</p>
        </div>
    {:else}
        <div class="watched-items-list">
            {#each watchedItems.sort((a, b) => {
                // Sort items with new results to the top
                const aHasResults = itemsWithNewResults.includes(a.id);
                const bHasResults = itemsWithNewResults.includes(b.id);

                if (aHasResults && !bHasResults) return -1;
                if (!aHasResults && bHasResults) return 1;

                // If both have results or both don't have results, sort by date added (newest first)
                return new Date(b.dateAdded).getTime() - new Date(a.dateAdded).getTime();
            }) as item (item.id)}
                <div class="watched-item card {itemsWithNewResults.includes(item.id) ? 'has-results' : ''}">
                    <div class="watched-item-header">
                        <div class="watched-item-title">
                            <h3>{item.query}</h3>
                            {#if item.hasResults}
                                <span class="found-badge">ZNALEZIONO!</span>
                            {/if}
                        </div>
                        <div class="watched-item-actions">
                            <button
                                class="btn-icon btn-outline"
                                on:click={() => searchQuery(item.query)}
                                title="Wyszukaj teraz"
                            >
                                <Eye size={18} />
                            </button>
                            <button
                                class="btn-icon btn-error"
                                on:click={() => removeWatchedItem(item.id)}
                                title="Usuń"
                            >
                                <Trash2 size={18} />
                            </button>

                            {#if itemsWithNewResults.includes(item.id)}
                                <button
                                    class="btn-icon btn-primary"
                                    on:click={() => {
                                        searchQuery(item.query);
                                        markAsNotified(item.id);
                                    }}
                                    title="Pobierz"
                                >
                                    <Download size={20} />
                                </button>
                        {/if}

                        </div>
                    </div>

                    <div class="watched-item-details">
                        <div class="detail">
                            <span class="label">Dodano:</span>
                            <span class="value">{formatDate(item.dateAdded)}</span>
                        </div>
                        <div class="detail">
                            <span class="label">Ostatnie sprawdzenie:</span>
                            <span class="value">{formatDate(item.lastChecked)}</span>
                        </div>
                    </div>
                </div>
            {/each}
        </div>
    {/if}
</div>

<style>
    .watch-list-container {
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
    }

    header {
        margin-bottom: 1.5rem;
        text-align: center;
    }

    h1 {
        font-size: 1.8rem;
        margin-bottom: 0.5rem;
        color: var(--text-primary);
    }

    .description {
        color: var(--text-secondary);
        font-size: 0.95rem;
    }

    .card {
        background-color: var(--card-bg);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        height: 100%;
    }

    .actions-card {
        display: flex;
        justify-content: center;
        padding: 1rem;
    }

    .add-query-form {
        width: 100%;
    }

    .input-group {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    input {
        flex: 1;
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        border: 1px solid var(--border);
        background-color: var(--input-bg);
        color: var(--text-primary);
        font-size: 1rem;
    }

    input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
    }

    .hint {
        font-size: 0.85rem;
        color: var(--text-secondary);
        margin-top: 0.5rem;
    }

    .btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.25rem;
        border-radius: 0.375rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-primary {
        background-color: var(--primary);
        color: white;
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
    }

    .btn-outline {
        background-color: transparent;
        border: 1px solid var(--border);
        color: var(--text-primary);
    }

    .btn-outline:hover {
        background-color: var(--card-bg-hover);
    }

    .btn-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        color: white;
    }

    .btn-icon.btn-primary {
        background-color: var(--primary-color, #4a6cf7);
    }

    .btn-icon.btn-outline {
        background-color: transparent;
        border: 1px solid #4a4a4a;
        color: #ccc;
    }

    .btn-icon.btn-error {
        background-color: var(--error-color, #e53935);
    }

    .btn-icon:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .btn-icon:active {
        transform: translateY(0);
    }

    /* Additional button styles */

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .loading-container, .error-container, .empty-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem 1rem;
        text-align: center;
        color: var(--text-secondary);
    }

    .error-container {
        color: var(--error);
    }

    .empty-container {
        padding: 4rem 1rem;
    }

    /* Icon styles */

    .empty-hint {
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .spin {
        animation: spin 1.5s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .watched-items-list {
        display: grid;
        gap: 1rem;
    }

    .watched-item {
        position: relative;
        padding: 1.25rem;
        transition: all 0.2s ease;
    }

    .watched-item.has-results {
        border-left: 4px solid var(--success);
    }

    .watched-item-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .watched-item-title {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .watched-item-title h3 {
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0;
        color: var(--text-primary);
    }

    .found-badge {
        display: inline-block;
        background-color: var(--primary);
        color: #000;
        font-size: 0.7rem;
        font-weight: 600;
        padding: 0.2rem 0.5rem;
        border-radius: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .watched-item-actions {
        display: flex;
        gap: 0.5rem;
    }

    .watched-item-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .detail {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .label {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }

    .value {
        font-size: 0.9rem;
        color: var(--text-primary);
    }

    .new-results-alert {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background-color: rgba(var(--success-rgb), 0.1);
        border-radius: 0.375rem;
        color: var(--success);
        margin-top: 1rem;
    }

    .new-results-alert button {
        margin-left: auto;
    }

    @media (max-width: 640px) {
        .input-group {
            flex-direction: column;
        }

        .watched-item-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .watched-item-actions {
            width: 100%;
            justify-content: flex-end;
        }

        .new-results-alert {
            flex-direction: column;
            text-align: center;
        }

        .new-results-alert button {
            margin-left: 0;
            width: 100%;
            justify-content: center;
        }
    }
</style>
