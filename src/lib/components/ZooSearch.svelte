<script lang="ts">
    import { onMount } from 'svelte';
    import { getGameCoverByName } from '$lib/services/steamGridDbService';
    import { getStorageItem, setStorageItem, isLocalStorageAvailable } from '$lib/utils/storageUtils';
    import { PC_SEARCH_CONFIG, STORAGE_KEYS } from '$lib/config/apiConfig';
    import { getWithAuth, fetchWithAuth } from '$lib/utils/apiUtils';

    // Typy danych
    interface GameDownload {
        title: string;
        uris: string[];
        uploadDate: string;
        fileSize: string;
    }

    interface GameSource {
        name: string;
        downloads: GameDownload[];
    }

    interface GameDriveLink {
        name: string;
        url: string;
    }

    interface GameDriveResult {
        title: string;
        url: string;
        source: string;
        description?: string;
        links: GameDriveLink[];
    }

    interface SearchResult {
        title: string;
        source: string;
        magnetLink?: string;
        fileSize?: string;
        uploadDate?: string;
        coverUrl?: string;
        description?: string;
        url?: string;
        links?: GameDriveLink[];
    }

    // Typ dla danych w cache
    interface GameSearchCache {
        fitgirl: GameSource | null;
        dodi: GameSource | null;
        gog: GameSource | null;
        timestamp: number;
        reduced?: boolean;
    }

    // Zmienne dla wyszukiwania
    let zooSearchQuery = '';
    let isSearching = false;
    let searchResults: SearchResult[] = [];
    let searchError: string | null = null;
    let fitgirlData: GameSource | null = null;
    let dodiData: GameSource | null = null;
    let gogData: GameSource | null = null;
    let gameDriveResults: SearchResult[] = [];
    let isDataLoaded = false;
    let isSearchingGameDrive = false;
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 500; // czas w ms po którym rozpocznie się wyszukiwanie

    // Expose zooSearchQuery to window for use in other components
    if (typeof window !== 'undefined') {
        // Check if the property already exists
        if (!Object.getOwnPropertyDescriptor(window, 'zooSearchQuery')) {
            console.log('Defining zooSearchQuery property on window');
            Object.defineProperty(window, 'zooSearchQuery', {
                get: () => zooSearchQuery,
                set: (value) => {
                    zooSearchQuery = value;
                    // Trigger search if query is set from outside
                    if (value && value.length >= 3) {
                        clearTimeout(typingTimer);
                        typingTimer = setTimeout(() => searchGames(), doneTypingInterval);
                    }
                },
                configurable: true // Allow property to be redefined if needed
            });
        } else {
            console.log('zooSearchQuery property already exists on window');
        }
    }

    // Zmienne dla ustawień bota Discord
    let showDiscordConfig = false;
    let isSavingDiscordConfig = false;
    let isBotRunning = false;
    let discordConfig = {
        apiKey: '',
        clientId: '',
        clientSecret: '',
        enabled: false,
        autoAddToDebrid: true,
        returnLinks: true,
        channelId: ''
    };

    // Funkcja do ładowania konfiguracji Discord
    async function loadDiscordConfig() {
        try {
            const response = await getWithAuth('/api/discord/config');

            if (!response.ok) {
                throw new Error(`Błąd wczytywania konfiguracji Discord: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.config) {
                discordConfig = data.config;
                console.log('Załadowano konfigurację Discord:', discordConfig);

                // Sprawdź status bota
                await updateBotStatus();
            }
        } catch (error) {
            console.error('Błąd wczytywania konfiguracji Discord:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd wczytywania konfiguracji Discord: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        }
    }

    // Funkcja do aktualizacji statusu bota
    async function updateBotStatus() {
        isBotRunning = await checkDiscordBotStatus();

        // Ustaw interwał do sprawdzania statusu bota co 10 sekund, gdy panel konfiguracyjny jest otwarty
        if (showDiscordConfig) {
            setTimeout(updateBotStatus, 10000);
        }
    }

    // Źródła do wyszukiwania
    let searchSources = {
        fitgirl: true,
        dodi: true,
        gog: true,
        gamedrive: false
    };

    // Zmienne dla paginacji
    let visibleResults: SearchResult[] = [];
    let currentPage = 1;
    const resultsPerPage = 5;
    let isLoadingMore = false;
    let loadedCoverIndices = new Set<number>(); // Indeksy wyników, dla których załadowano okładki

    // Źródła danych z centralnej konfiguracji
    const FITGIRL_SOURCE = PC_SEARCH_CONFIG.SOURCES.FITGIRL;
    const DODI_SOURCE = PC_SEARCH_CONFIG.SOURCES.DODI;
    const GOG_SOURCE = PC_SEARCH_CONFIG.SOURCES.GOG;

    // Czas wygaśnięcia cache
    const CACHE_EXPIRATION = PC_SEARCH_CONFIG.CACHE_EXPIRATION;

    // Zmienne dla śledzenia postępu ładowania
    let loadingProgress = {
        fitgirl: 0,
        dodi: 0,
        gog: 0
    };
    let totalProgress = 0;

    // Funkcja do ładowania danych ze wszystkich źródeł
    async function loadData() {
        isDataLoaded = false;
        searchError = null;

        // Resetuj postęp ładowania
        loadingProgress = { fitgirl: 0, dodi: 0, gog: 0 };
        totalProgress = 0;

        try {
            // Najpierw sprawdź, czy dane są w cache
            if (isLocalStorageAvailable()) {
                try {
                    // Pobierz dane z localStorage używając naszej nowej funkcji
                    const cachedData = getStorageItem<GameSearchCache>(STORAGE_KEYS.GAME_SEARCH_DATA);

                    if (cachedData) {
                        const cacheTimestamp = cachedData.timestamp || 0;
                        const currentTime = new Date().getTime();

                        // Sprawdź, czy cache nie jest starszy niż czas wygaśnięcia
                        if (currentTime - cacheTimestamp < CACHE_EXPIRATION) {
                            console.log('Wczytywanie danych z cache...');

                            // Pobierz dane z cache
                            fitgirlData = cachedData.fitgirl || null;
                            dodiData = cachedData.dodi || null;
                            gogData = cachedData.gog || null;

                            // Jeśli dane są zredukowane, załaduj pozostałe dane w tle
                            if (cachedData.reduced) {
                                console.log('Wykryto zredukowane dane, ładowanie pozostałych danych w tle...');
                                setTimeout(() => {
                                    loadData();
                                }, 2000);
                            }

                            isDataLoaded = true;

                            // Jeśli jest już wpisane zapytanie, wykonaj wyszukiwanie
                            if (zooSearchQuery.trim().length >= 3) {
                                searchGames();
                            }

                            console.log('Dane wczytane z cache');
                            return;
                        } else {
                            console.log('Cache jest nieaktualny, pobieranie nowych danych...');
                        }
                    }
                } catch (error) {
                    console.error('Błąd wczytywania danych z cache:', error);
                    // Kontynuuj z pobieraniem danych z sieci
                }
            }

            // Funkcja do śledzenia postępu pobierania
            function createProgressTracker(source: 'fitgirl' | 'dodi' | 'gog') {
                return (response: Response) => {
                    const contentLength = parseInt(response.headers.get('Content-Length') || '0', 10);
                    if (!contentLength) {
                        return response;
                    }

                    const reader = response.body!.getReader();
                    let receivedLength = 0;

                    const stream = new ReadableStream({
                        start(controller) {
                            function push() {
                                reader.read().then(({ done, value }) => {
                                    if (done) {
                                        controller.close();
                                        return;
                                    }

                                    receivedLength += value.length;
                                    loadingProgress[source] = Math.round((receivedLength / contentLength) * 100);
                                    totalProgress = Math.round(
                                        (loadingProgress.fitgirl + loadingProgress.dodi + loadingProgress.gog) / 3
                                    );

                                    controller.enqueue(value);
                                    push();
                                }).catch(error => {
                                    console.error(`Błąd podczas śledzenia postępu ${source}:`, error);
                                    controller.error(error);
                                });
                            }

                            push();
                        }
                    });

                    return new Response(stream, { headers: response.headers });
                };
            }

            // Pobieranie danych z poszczególnych źródeł sekwencyjnie, aby zmniejszyć obciążenie
            console.log('Pobieranie danych FitGirl...');
            const fitgirlResponse = await fetch(FITGIRL_SOURCE)
                .then(createProgressTracker('fitgirl'));

            if (!fitgirlResponse.ok) {
                throw new Error(`Błąd pobierania danych FitGirl: ${fitgirlResponse.status}`);
            }

            fitgirlData = await fitgirlResponse.json();
            console.log('Dane FitGirl pobrane');

            console.log('Pobieranie danych DODI...');
            const dodiResponse = await fetch(DODI_SOURCE)
                .then(createProgressTracker('dodi'));

            if (!dodiResponse.ok) {
                throw new Error(`Błąd pobierania danych DODI: ${dodiResponse.status}`);
            }

            dodiData = await dodiResponse.json();
            console.log('Dane DODI pobrane');

            console.log('Pobieranie danych GOG...');
            const gogResponse = await fetch(GOG_SOURCE)
                .then(createProgressTracker('gog'));

            if (!gogResponse.ok) {
                throw new Error(`Błąd pobierania danych GOG: ${gogResponse.status}`);
            }

            gogData = await gogResponse.json();
            console.log('Dane GOG pobrane');

            // Zapisz dane w cache z kompresją
            if (isLocalStorageAvailable()) {
                try {
                    // Przygotuj dane do zapisania
                    const cacheData = {
                        fitgirl: fitgirlData,
                        dodi: dodiData,
                        gog: gogData,
                        timestamp: new Date().getTime()
                    };

                    // Spróbuj zapisać dane z kompresją
                    const success = setStorageItem(STORAGE_KEYS.GAME_SEARCH_DATA, cacheData, true);

                    if (success) {
                        console.log('Dane zapisane w cache (skompresowane)');
                    } else {
                        console.warn('Nie udało się zapisać pełnych danych, próba zapisania tylko FitGirl...');

                        // Zapisz tylko podstawowe dane (np. tylko FitGirl, który jest najpopularniejszy)
                        const reducedCacheData = {
                            fitgirl: fitgirlData,
                            timestamp: new Date().getTime(),
                            reduced: true
                        };

                        const reducedSuccess = setStorageItem(STORAGE_KEYS.GAME_SEARCH_DATA, reducedCacheData, true);

                        if (reducedSuccess) {
                            console.log('Zapisano tylko dane FitGirl w cache');
                        } else {
                            console.error('Nie udało się zapisać nawet zredukowanych danych');
                        }
                    }
                } catch (error) {
                    console.error('Błąd zapisywania danych w cache:', error);
                }
            }

            isDataLoaded = true;

            // Jeśli jest już wpisane zapytanie, wykonaj wyszukiwanie
            if (zooSearchQuery.trim().length >= 3) {
                searchGames();
            }
        } catch (error) {
            console.error('Błąd ładowania danych:', error);
            searchError = error instanceof Error ? error.message : 'Nieznany błąd';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Nie udało się załadować danych: ' + searchError, 'error');
            }
        }
    }

    // Funkcja do wyszukiwania gier na GameDrive
    async function searchGameDrive(query: string): Promise<SearchResult[]> {
        try {
            isSearchingGameDrive = true;

            const response = await fetch(`/api/pc/gamedrive/search?query=${encodeURIComponent(query)}`);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Error ${response.status}`);
            }

            const data = await response.json();
            const results = data.results || [];

            // Konwertuj wyniki GameDrive do formatu SearchResult
            return results.map((game: any) => ({
                title: game.title,
                source: 'GameDrive',
                description: game.description || 'Brak opisu',
                url: game.url,
                links: game.links,
                // Domyślne wartości dla pól wymaganych przez interfejs
                fileSize: 'Nieznany rozmiar',
                uploadDate: new Date().toISOString() // Używamy bieżącej daty
            }));
        } catch (error: any) {
            console.error('Błąd wyszukiwania GameDrive:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Błąd wyszukiwania GameDrive: ' + error.message, 'error');
            }
            return [];
        } finally {
            isSearchingGameDrive = false;
        }
    }

    // Funkcja do wyszukiwania gier
    async function searchGames() {
        if (zooSearchQuery.trim().length < 3) {
            searchResults = [];
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wpisz co najmniej 3 znaki', 'error');
            }
            return;
        }

        // Sprawdź, czy mamy jakiekolwiek dane do przeszukania
        const hasAnyData = (searchSources.fitgirl && fitgirlData) ||
                          (searchSources.dodi && dodiData) ||
                          (searchSources.gog && gogData);

        if (!isDataLoaded && !hasAnyData) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Dane są jeszcze ładowane, wyniki mogą być niekompletne', 'info');
            }
            // Kontynuuj wyszukiwanie z dostępnymi danymi
        }

        isSearching = true;
        searchResults = [];
        searchError = null;

        try {
            const query = zooSearchQuery.toLowerCase();
            let results: SearchResult[] = [];

            // Wyszukiwanie w danych FitGirl
            if (searchSources.fitgirl && fitgirlData && fitgirlData.downloads) {
                console.log('Wyszukiwanie w FitGirl...');
                fitgirlData.downloads.forEach(download => {
                    if (download.title.toLowerCase().includes(query) && download.uris && download.uris.length > 0) {
                        results.push({
                            title: download.title,
                            source: 'FitGirl',
                            magnetLink: download.uris[0],
                            fileSize: download.fileSize || 'Nieznany rozmiar',
                            uploadDate: download.uploadDate || 'Nieznana data'
                        });
                    }
                });
            }

            // Wyszukiwanie w danych DODI
            if (searchSources.dodi && dodiData && dodiData.downloads) {
                console.log('Wyszukiwanie w DODI...');
                dodiData.downloads.forEach(download => {
                    if (download.title.toLowerCase().includes(query) && download.uris && download.uris.length > 0) {
                        results.push({
                            title: download.title,
                            source: 'DODI',
                            magnetLink: download.uris[0],
                            fileSize: download.fileSize || 'Nieznany rozmiar',
                            uploadDate: download.uploadDate || 'Nieznana data'
                        });
                    }
                });
            }

            // Wyszukiwanie w danych GOG
            if (searchSources.gog && gogData && gogData.downloads) {
                console.log('Wyszukiwanie w GOG...');
                gogData.downloads.forEach(download => {
                    if (download.title.toLowerCase().includes(query) && download.uris && download.uris.length > 0) {
                        results.push({
                            title: download.title,
                            source: 'GOG',
                            magnetLink: download.uris[0],
                            fileSize: download.fileSize || 'Nieznany rozmiar',
                            uploadDate: download.uploadDate || 'Nieznana data'
                        });
                    }
                });
            }

            // Wyszukiwanie w GameDrive
            let gameDriveResults: SearchResult[] = [];
            if (searchSources.gamedrive) {
                console.log('Wyszukiwanie w GameDrive...');
                gameDriveResults = await searchGameDrive(zooSearchQuery);
            }
            results = [...results, ...gameDriveResults];

            // Sortowanie wyników - najnowsze najpierw
            results.sort((a, b) => {
                const dateA = new Date(a.uploadDate || new Date()).getTime() || 0;
                const dateB = new Date(b.uploadDate || new Date()).getTime() || 0;
                return dateB - dateA;
            });

            // Przypisanie wyników do zmiennej
            searchResults = results;

            // Resetowanie paginacji
            currentPage = 1;
            loadedCoverIndices = new Set<number>();

            // Ustawienie widocznych wyników
            updateVisibleResults();

            // Pobieranie okładek dla pierwszej strony wyników
            fetchGameCovers();

            if (searchResults.length === 0) {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(`Nie znaleziono wyników dla "${zooSearchQuery}"`, 'error');
                }
            }
            // Alert "Znaleziono X wyników" został usunięty
        } catch (error) {
            console.error('Błąd wyszukiwania:', error);
            searchError = error instanceof Error ? error.message : 'Nieznany błąd';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Nie udało się wyszukać: ' + searchError, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Funkcja do obsługi zmiany tekstu w polu wyszukiwania (debounce)
    function handleSearchInput() {
        clearTimeout(typingTimer);
        if (zooSearchQuery.trim().length >= 3) {
            typingTimer = setTimeout(searchGames, doneTypingInterval);
        } else {
            searchResults = [];
        }
    }

    // Funkcja do dodawania magnet linku
    async function addMagnetLink(magnetLink?: string) {
        if (!magnetLink) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Brak linku magnetycznego dla tej pozycji', 'error');
            }
            return;
        }

        try {
            // Przekazanie magnet linku do komponentu nadrzędnego bez pokazywania modalu
            if (typeof setMagnetAndSwitchTab === 'function') {
                setMagnetAndSwitchTab(magnetLink);
            }

        } catch (error) {
            console.error('Błąd dodawania magnet linku:', error);

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Nie udało się dodać magnet linku: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        }
    }

    // Funkcja do otwierania linku GameDrive
    function openGameDriveLinks(result: SearchResult) {
        if (result.source !== 'GameDrive' || !result.links || result.links.length === 0) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Brak linków do pobrania', 'error');
            }
            return;
        }

        // Tworzymy tekst z linkami do wyświetlenia w modalu
        const linksText = result.links.map(link => `${link.name}: ${link.url}`).join('\n');

        // Wyświetlamy modal z linkami
        if (typeof window !== 'undefined' && (window as any).showAlert) {
            (window as any).showAlert(`Linki do pobrania dla ${result.title}:\n\n${linksText}`, 'info');
        }

        // Otwieramy pierwszy link w nowej karcie
        if (result.links.length > 0) {
            window.open(result.links[0].url, '_blank');
        }
    }

    // Funkcja do obsługi naciśnięcia klawisza Enter w polu wyszukiwania
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && zooSearchQuery.trim().length >= 3) {
            searchGames();
        }
    }

    // Funkcja do formatowania daty
    function formatDate(dateString?: string): string {
        if (!dateString) return 'Nieznana data';

        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('pl-PL', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (e) {
            return dateString;
        }
    }

    // Funkcja do aktualizacji widocznych wyników na podstawie bieżącej strony
    function updateVisibleResults() {
        const startIndex = 0;
        const endIndex = currentPage * resultsPerPage;
        visibleResults = searchResults.slice(startIndex, endIndex);
        console.log(`Zaktualizowano widoczne wyniki: ${startIndex}-${endIndex} z ${searchResults.length}`);
    }

    // Funkcja do ładowania kolejnej strony wyników
    function loadMoreResults() {
        if (currentPage * resultsPerPage < searchResults.length) {
            isLoadingMore = true;
            currentPage++;
            updateVisibleResults();
            fetchGameCovers();
        }
    }

    // Funkcja do pobierania okładek dla wyników wyszukiwania
    async function fetchGameCovers() {
        console.log('Rozpoczynam pobieranie okładek dla wyników wyszukiwania');
        console.log('Liczba widocznych wyników:', visibleResults.length);

        // Oblicz zakres indeksów dla bieżącej strony
        const startIndex = (currentPage - 1) * resultsPerPage;
        const endIndex = Math.min(currentPage * resultsPerPage, searchResults.length);

        console.log(`Pobieranie okładek dla wyników ${startIndex}-${endIndex}`);

        for (let i = startIndex; i < endIndex; i++) {
            // Pomijamy jeśli już załadowano okładkę dla tego wyniku
            if (loadedCoverIndices.has(i)) {
                console.log(`Pomijam indeks ${i} - okładka już załadowana`);
                continue;
            }

            const result = searchResults[i];
            // Wyciągnij nazwę gry z tytułu (usuń informacje o repacku, wersji itp.)
            const gameName = extractGameName(result.title);

            console.log(`[${i+1}/${endIndex}] Próba pobrania okładki dla: "${result.title}" -> "${gameName}"`);

            try {
                // Pobierz okładkę dla gry
                console.log(`Wywołuję getGameCoverByName dla: "${gameName}"`);
                const coverUrl = await getGameCoverByName(gameName);

                console.log(`Otrzymany URL okładki: ${coverUrl || 'null'}`);

                if (coverUrl) {
                    // Aktualizuj wynik wyszukiwania z URL okładki
                    searchResults[i] = { ...result, coverUrl };
                    console.log(`Pobrano okładkę dla: ${gameName}, URL: ${coverUrl}`);
                } else {
                    console.log(`Nie znaleziono okładki dla: ${gameName}`);
                }

                // Dodaj indeks do zbioru załadowanych okładek
                loadedCoverIndices.add(i);
            } catch (error) {
                console.error(`Błąd pobierania okładki dla ${gameName}:`, error);
            }
        }

        // Wymuszenie aktualizacji widoku
        console.log('Aktualizacja widoku z nowymi okładkami');
        searchResults = [...searchResults];
        updateVisibleResults();
        isLoadingMore = false;
        console.log('Zakończono pobieranie okładek');
    }

    // Funkcja do wyciągania nazwy gry z tytułu
    function extractGameName(title: string): string {
        // Najpierw usuń wszystko po znaku +
        let cleanTitle = title.split('+')[0];

        // Usuń wszystko w nawiasach
        cleanTitle = cleanTitle
            .replace(/\[.*?\]/g, '')
            .replace(/\(.*?\)/g, '')
            .replace(/\{.*?\}/g, '');

        // Usuń typowe frazy z tytułów repacków
        cleanTitle = cleanTitle
            .replace(/\bFitGirl\b/i, '')
            .replace(/\bDODI\b/i, '')
            .replace(/\bRepack\b/i, '')
            .replace(/\bMulti\d*\b/i, '')
            .replace(/\bv\d+\.\d+\b/i, '')
            .replace(/\bv\d+\b/i, '')
            .replace(/\bUpdate\b/i, '')
            .replace(/\bDLC\b/i, '')
            .replace(/\bAll\s*DLCs\b/i, '')
            .replace(/\bGOG\b/i, '')
            .replace(/\bEGS\b/i, '')
            .replace(/\bSteam\b/i, '')
            .replace(/\bEpic\b/i, '')
            .replace(/\bCrack\b/i, '')
            .replace(/\bCracked\b/i, '')
            .replace(/\bCODEX\b/i, '')
            .replace(/\bPLAZA\b/i, '')
            .replace(/\bELAMIGOS\b/i, '')
            .replace(/\bSKIDROW\b/i, '')
            .replace(/\bRELOADED\b/i, '')
            .replace(/\bCPY\b/i, '')
            .replace(/\bHI2U\b/i, '')
            .replace(/\bBALTMAN\b/i, '')
            .replace(/\bDARKSiDERS\b/i, '')
            .replace(/\bSelectivE\b/i, '')
            .replace(/\bRG\b/i, '')
            .replace(/\bMechanic\b/i, '')
            .replace(/\bGoldberg\b/i, '')
            .replace(/\bIncl\b/i, '')
            .replace(/\bBuild\s*\d+\b/i, '')
            .replace(/\bPatch\s*\d+\b/i, '')
            .replace(/\+\d+\s*Trainer/i, '')
            .replace(/\s{2,}/g, ' ') // Zamień wielokrotne spacje na pojedynczą
            .trim();

        // Jeśli tytuł zawiera myślnik lub dwukropek, weź tylko pierwszą część (zwykle nazwa gry)
        // Ale zachowaj dwukropek w nazwach jak "Control: Ultimate Edition"
        let parts = cleanTitle.split('-');
        cleanTitle = parts[0].trim();

        // Zachowaj dwukropek w nazwach jak "Control: Ultimate Edition"
        // ale usuń wszystko po drugim dwukropku jeśli istnieje
        const colonParts = cleanTitle.split(':');
        if (colonParts.length > 1) {
            // Zachowaj pierwszy dwukropek i tekst po nim
            cleanTitle = colonParts[0] + ': ' + colonParts[1].trim();
        }

        return cleanTitle.trim();
    }

    // Funkcja do przełączania panelu ustawień Discord
    async function toggleDiscordConfig() {
        showDiscordConfig = !showDiscordConfig;

        if (showDiscordConfig) {
            // Gdy panel jest otwierany, zaktualizuj status bota
            await updateBotStatus();
        }
    }

    // Funkcja do zapisywania ustawień Discord
    async function saveDiscordConfig() {
        if (isSavingDiscordConfig) return;

        isSavingDiscordConfig = true;

        try {
            const response = await fetch('/api/discord/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(discordConfig)
            });

            if (!response.ok) {
                throw new Error(`Błąd zapisywania konfiguracji Discord: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert('Ustawienia bota Discord zostały zapisane!', 'success');
                }

                // Jeśli bot jest włączony, zrestartuj go
                if (discordConfig.enabled) {
                    await restartDiscordBot();
                } else {
                    await stopDiscordBot();
                }

                showDiscordConfig = false;
            } else {
                throw new Error(data.message || 'Nieznany błąd podczas zapisywania konfiguracji Discord');
            }
        } catch (error) {
            console.error('Błąd zapisywania ustawień Discord:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd zapisywania ustawień: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        } finally {
            isSavingDiscordConfig = false;
        }
    }

    // Funkcja do uruchamiania bota Discord
    async function startDiscordBot() {
        try {
            const response = await fetchWithAuth('/api/discord/bot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action: 'start' })
            });

            if (!response.ok) {
                throw new Error(`Błąd uruchamiania bota Discord: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                isBotRunning = true;
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert('Bot Discord został uruchomiony!', 'success');
                }
            } else {
                throw new Error(data.message || 'Nieznany błąd podczas uruchamiania bota Discord');
            }
        } catch (error) {
            console.error('Błąd uruchamiania bota Discord:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd uruchamiania bota: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        }
    }

    // Funkcja do zatrzymywania bota Discord
    async function stopDiscordBot() {
        try {
            const response = await fetch('/api/discord/bot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action: 'stop' })
            });

            if (!response.ok) {
                throw new Error(`Błąd zatrzymywania bota Discord: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                isBotRunning = false;
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert('Bot Discord został zatrzymany!', 'success');
                }
            } else {
                throw new Error(data.message || 'Nieznany błąd podczas zatrzymywania bota Discord');
            }
        } catch (error) {
            console.error('Błąd zatrzymywania bota Discord:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd zatrzymywania bota: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        }
    }

    // Funkcja do restartowania bota Discord
    async function restartDiscordBot() {
        try {
            const response = await fetch('/api/discord/bot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action: 'restart' })
            });

            if (!response.ok) {
                throw new Error(`Błąd restartowania bota Discord: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert('Bot Discord został zrestartowany!', 'success');
                }
            } else {
                throw new Error(data.message || 'Nieznany błąd podczas restartowania bota Discord');
            }
        } catch (error) {
            console.error('Błąd restartowania bota Discord:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd restartowania bota: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        }
    }

    // Funkcja do sprawdzania statusu bota Discord
    async function checkDiscordBotStatus() {
        try {
            const response = await fetchWithAuth('/api/discord/bot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action: 'status' })
            });

            if (!response.ok) {
                throw new Error(`Błąd sprawdzania statusu bota Discord: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                return data.isRunning;
            } else {
                throw new Error(data.message || 'Nieznany błąd podczas sprawdzania statusu bota Discord');
            }
        } catch (error) {
            console.error('Błąd sprawdzania statusu bota Discord:', error);
            return false;
        }
    }

    // Funkcja do ładowania danych w tle
    function loadDataInBackground() {
        // Najpierw sprawdź, czy dane są w cache
        if (isLocalStorageAvailable()) {
            try {
                // Pobierz dane z localStorage używając naszej nowej funkcji
                const cachedData = getStorageItem<GameSearchCache>(STORAGE_KEYS.GAME_SEARCH_DATA);

                if (cachedData) {
                    const cacheTimestamp = cachedData.timestamp || 0;
                    const currentTime = new Date().getTime();

                    // Sprawdź, czy cache nie jest starszy niż czas wygaśnięcia
                    if (currentTime - cacheTimestamp < CACHE_EXPIRATION) {
                        console.log('Wczytywanie danych z cache...');

                        // Pobierz dane z cache
                        fitgirlData = cachedData.fitgirl || null;
                        dodiData = cachedData.dodi || null;
                        gogData = cachedData.gog || null;

                        isDataLoaded = true;

                        // Jeśli jest już wpisane zapytanie, wykonaj wyszukiwanie
                        if (zooSearchQuery.trim().length >= 3) {
                            searchGames();
                        }

                        console.log('Dane wczytane z cache');

                        // Mimo to, odśwież dane w tle
                        setTimeout(() => {
                            console.log('Odświeżanie danych w tle...');
                            loadData();
                        }, 2000);

                        return;
                    }
                }
            } catch (error) {
                console.error('Błąd wczytywania danych z cache:', error);
            }
        }

        // Jeśli nie ma danych w cache, załaduj je normalnie
        loadData();
    }

    // Ładowanie danych przy montowaniu komponentu
    onMount(() => {
        loadDataInBackground();
        loadDiscordConfig();

        // Focus na pole wyszukiwania
        setTimeout(() => {
            const searchInput = document.getElementById('zooSearchQuery');
            if (searchInput) {
                searchInput.focus();
            }
        }, 100);

        // Sprawdź, czy jest zapisane zapytanie w sessionStorage
        if (typeof sessionStorage !== 'undefined') {
            const savedQuery = sessionStorage.getItem('zooSearchQuery');
            if (savedQuery) {
                console.log(`ZooSearch: Found saved query in sessionStorage: "${savedQuery}"`);
                zooSearchQuery = savedQuery;
                // Usuń zapisane zapytanie, aby nie było używane ponownie
                sessionStorage.removeItem('zooSearchQuery');

                // Jeśli dane są już załadowane, wykonaj wyszukiwanie
                if (isDataLoaded && zooSearchQuery.trim().length >= 3) {
                    console.log('ZooSearch: Data already loaded, searching...');
                    setTimeout(() => searchGames(), 100);
                } else {
                    console.log('ZooSearch: Data not loaded yet, will search after loading');
                }
            }
        }
    });

    // Funkcja do ustawienia magnet linku i przełączenia zakładki (będzie przekazana z komponentu nadrzędnego)
    export let setMagnetAndSwitchTab: (magnetUrl: string) => void;
</script>

<div class="card">
    <div class="flex items-center justify-between mb-4">
        <div>
            <h2>Szukaj gry na PC</h2>
            <p class="text-[var(--text-secondary)]">Wyszukaj i pobierz gry na PC z FitGirl, DODI, GameDrive lub GOG</p>
        </div>
        <div class="flex gap-2">
            <button class="btn btn-discord" on:click={toggleDiscordConfig} title="Bot Discord" aria-label="Bot Discord">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 512 365.467" shape-rendering="geometricPrecision" text-rendering="geometricPrecision" image-rendering="optimizeQuality" fill-rule="evenodd" clip-rule="evenodd" aria-hidden="true">
                    <path fill="currentColor" d="M378.186 365.028s-15.794-18.865-28.956-35.099c57.473-16.232 79.41-51.77 79.41-51.77-17.989 11.846-35.099 20.182-50.454 25.885-21.938 9.213-42.997 14.917-63.617 18.866-42.118 7.898-80.726 5.703-113.631-.438-25.008-4.827-46.506-11.407-64.494-18.867-10.091-3.947-21.059-8.774-32.027-14.917-1.316-.877-2.633-1.316-3.948-2.193-.877-.438-1.316-.878-1.755-.878-7.898-4.388-12.285-7.458-12.285-7.458s21.06 34.659 76.779 51.331c-13.163 16.673-29.395 35.977-29.395 35.977C36.854 362.395 0 299.218 0 299.218 0 159.263 63.177 45.633 63.177 45.633 126.354-1.311 186.022.005 186.022.005l4.388 5.264C111.439 27.645 75.461 62.305 75.461 62.305s9.653-5.265 25.886-12.285c46.945-20.621 84.236-25.885 99.592-27.64 2.633-.439 4.827-.878 7.458-.878 26.763-3.51 57.036-4.387 88.624-.878 41.68 4.826 86.43 17.111 132.058 41.68 0 0-34.66-32.906-109.244-55.281l6.143-7.019s60.105-1.317 122.844 45.628c0 0 63.178 113.631 63.178 253.585 0-.438-36.854 62.739-133.813 65.81l-.001.001zm-43.874-203.133c-25.006 0-44.75 21.498-44.75 48.262 0 26.763 20.182 48.26 44.75 48.26 25.008 0 44.752-21.497 44.752-48.26 0-26.764-20.182-48.262-44.752-48.262zm-160.135 0c-25.008 0-44.751 21.498-44.751 48.262 0 26.763 20.182 48.26 44.751 48.26 25.007 0 44.75-21.497 44.75-48.26.439-26.763-19.742-48.262-44.75-48.262z"/>
                </svg>
                <span>Bot Discord</span>
            </button>
        </div>
    </div>

    {#if showDiscordConfig}
        <div class="config-panel" style="border: 1px solid #5865F2; background-color: #2a2d31;">
            <h3 class="mb-4" style="color: #5865F2;">Ustawienia bota Discord</h3>

            <div class="config-section">
                <h4>Podstawowe ustawienia</h4>

                <div class="form-group">
                    <label for="discord_api_key">Token bota Discord</label>
                    <input type="password" id="discord_api_key" bind:value={discordConfig.apiKey} placeholder="••••••••••••••••••••••••••">
                    <div class="input-help">
                        Znajdziesz go w zakładce "Bot" > "Reset Token" w Discord Developer Portal.
                        Format: XXXX.YYYY.ZZZZ
                    </div>
                </div>

                <div class="form-group">
                    <label for="discord_client_id">Client ID</label>
                    <input type="text" id="discord_client_id" bind:value={discordConfig.clientId} placeholder="ID klienta Discord">
                    <div class="input-help">
                        Znajdziesz go w zakładce "General Information" jako "Application ID".
                    </div>
                </div>

                <div class="form-group">
                    <label for="discord_client_secret">Client Secret</label>
                    <input type="password" id="discord_client_secret" bind:value={discordConfig.clientSecret} placeholder="••••••••••••••••••••••••••">
                    <div class="input-help">
                        Znajdziesz go w zakładce "OAuth2" > "Client Secret".
                    </div>
                </div>

                <div class="form-group">
                    <label for="discord_channel_id">ID kanału Discord</label>
                    <input type="text" id="discord_channel_id" bind:value={discordConfig.channelId} placeholder="ID kanału na którym bot będzie działać">
                    <div class="input-help">
                        Kliknij prawym przyciskiem myszy na kanał i wybierz "Kopiuj ID".
                        Wymaga włączonego trybu dewelopera w ustawieniach Discord.
                    </div>
                </div>

                <div class="form-group">
                    <div class="label-text">Opcje bota</div>
                    <div class="checkbox-group">
                        <label>
                            <input type="checkbox" bind:checked={discordConfig.enabled}>
                            Włącz bota Discord
                        </label>
                        <label>
                            <input type="checkbox" bind:checked={discordConfig.autoAddToDebrid}>
                            Automatycznie dodawaj do Real-Debrid
                        </label>
                        <label>
                            <input type="checkbox" bind:checked={discordConfig.returnLinks}>
                            Zwracaj linki do pobrania na Discord
                        </label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="label-text">Status bota</div>
                    <div class="bot-status">
                        <div class="status-indicator" class:active={isBotRunning}>
                            <span class="status-dot"></span>
                            <span class="status-text">{isBotRunning ? 'Bot aktywny' : 'Bot nieaktywny'}</span>
                        </div>
                        <div class="bot-controls">
                            <button class="btn btn-sm" on:click={startDiscordBot} disabled={isBotRunning}>
                                Uruchom bota
                            </button>
                            <button class="btn btn-sm" on:click={stopDiscordBot} disabled={!isBotRunning}>
                                Zatrzymaj bota
                            </button>
                            <button class="btn btn-sm" on:click={updateBotStatus} title="Odśwież status">
                                <span class="refresh-icon">↻</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="label-text">Instalacja bota</div>
                    <div class="debug-info">
                        <p>Aby dodać bota do swojego serwera Discord, kliknij poniższy link:</p>
                        <a href="https://discord.com/oauth2/authorize?client_id=1364855491905323008" target="_blank" class="discord-install-link">
                            Dodaj bota do serwera Discord
                        </a>
                    </div>
                </div>
            </div>

            <div class="config-buttons">
                <button class="btn btn-secondary" on:click={toggleDiscordConfig}>Anuluj</button>
                <button class="btn btn-primary" style="background-color: #5865F2;" on:click={saveDiscordConfig} disabled={isSavingDiscordConfig}>
                    {#if isSavingDiscordConfig}
                        Zapisywanie...<div class="loading"></div>
                    {:else}
                        Zapisz
                    {/if}
                </button>
            </div>
        </div>
    {:else}
        <div style="display: flex; gap: 10px; margin-top: 20px;">
            <input type="text" id="zooSearchQuery" class="magnet-input"
                   style="flex: 1;"
                   placeholder="Wpisz tytuł gry (min. 3 znaki)..."
                   bind:value={zooSearchQuery}
                   on:input={handleSearchInput}
                   on:keypress={handleKeyPress}
                   disabled={isSearching}>
            <button class="btn btn-primary"
                    on:click={searchGames}
                    disabled={isSearching || zooSearchQuery.trim().length < 3}>
                {#if isSearching}
                    Szukanie...<div class="loading"></div>
                {:else}
                    Szukaj
                {/if}
            </button>
        </div>
    {/if}

    <!-- Checkboxy do wyboru źródeł -->
    <div class="search-sources">
        <label class="source-checkbox">
            <input type="checkbox" bind:checked={searchSources.fitgirl}>
            <span class="source-name">FitGirl</span>
        </label>
        <label class="source-checkbox">
            <input type="checkbox" bind:checked={searchSources.dodi}>
            <span class="source-name">DODI</span>
        </label>
        <label class="source-checkbox">
            <input type="checkbox" bind:checked={searchSources.gog}>
            <span class="source-name">GOG</span>
        </label>
        <label class="source-checkbox">
            <input type="checkbox" bind:checked={searchSources.gamedrive}>
            <span class="source-name">GameDrive</span>
        </label>
    </div>

    <div style="margin-top: 20px;">
        {#if !isDataLoaded && !searchError}
            <div style="text-align: center; padding: 20px;">
                <div class="loading" style="width: 24px; height: 24px; margin: 0 auto;"></div>
                <p style="margin-top: 10px;">Ładowanie danych... {totalProgress}%</p>

                <!-- Pasek postępu -->
                <div class="progress-container">
                    <div class="progress-bar" style="width: {totalProgress}%"></div>
                </div>

                <!-- Szczegóły postępu -->
                <div class="progress-details">
                    <div class="progress-item">
                        <span>FitGirl: {loadingProgress.fitgirl}%</span>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {loadingProgress.fitgirl}%"></div>
                        </div>
                    </div>
                    <div class="progress-item">
                        <span>DODI: {loadingProgress.dodi}%</span>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {loadingProgress.dodi}%"></div>
                        </div>
                    </div>
                    <div class="progress-item">
                        <span>GOG: {loadingProgress.gog}%</span>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {loadingProgress.gog}%"></div>
                        </div>
                    </div>
                </div>
            </div>
        {:else if isSearching}
            <div style="text-align: center; padding: 20px;">
                <div class="loading" style="width: 24px; height: 24px; margin: 0 auto;"></div>
                <p style="margin-top: 10px;">Wyszukiwanie...</p>
            </div>
        {:else if searchError}
            <div class="empty-state">
                <p>Wystąpił błąd podczas wyszukiwania.<br/>{searchError}</p>
                <button class="btn btn-outline" style="margin-top: 10px;" on:click={loadData}>Spróbuj ponownie</button>
            </div>
        {:else if searchResults.length > 0}
            {#each visibleResults as result}
                <div style="background-color: var(--item-bg); border-radius: 6px; padding: 15px; margin-bottom: 10px; display: flex; gap: 15px;">
                    <!-- Okładka gry -->
                    <div class="game-cover-container">
                        {#if result.coverUrl}
                            <img src={result.coverUrl} alt="Okładka {result.title}" class="game-cover" />
                        {:else}
                            <div class="game-cover-placeholder">
                                <span>Brak okładki</span>
                            </div>
                        {/if}
                    </div>

                    <!-- Informacje o grze -->
                    <div style="flex: 1; display: flex; flex-direction: column; gap: 10px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: bold; overflow: hidden; text-overflow: ellipsis; white-space: wrap;" title={result.title}>
                                {result.title}
                            </span>
                            <span class="source-badge" style="background-color: {
                                result.source === 'FitGirl' ? '#6a329f' :
                                result.source === 'DODI' ? '#3a7ca5' :
                                result.source === 'GameDrive' ? '#e74c3c' :
                                '#4c9a2a'
                            }; padding: 3px 8px; border-radius: 4px; font-size: 12px;">
                                {result.source}
                            </span>
                        </div>

                        <div style="font-size: 12px; color: #aaa;">
                            <span>Rozmiar: {result.fileSize}</span>
                            <span style="margin-left: 15px;">Data: {formatDate(result.uploadDate)}</span>
                        </div>

                        <div style="display: flex; gap: 8px; justify-content: flex-end; margin-top: auto;">
                            <button class="btn btn-outline"
                                    style="font-size: 13px; padding: 6px 12px;"
                                    on:click={() => window.open(
                                        result.source === 'FitGirl' ? `https://fitgirl-repacks.site/?s=${encodeURIComponent(result.title)}` :
                                        result.source === 'DODI' ? `https://dodi-repacks.site/?s=${encodeURIComponent(result.title)}` :
                                        result.source === 'GameDrive' ? result.url :
                                        `https://www.gog.com/en/games?query=${encodeURIComponent(extractGameName(result.title))}`, '_blank')}>
                                Odwiedź stronę
                            </button>

                            {#if result.source === 'GameDrive'}
                                <button class="btn btn-primary"
                                        style="font-size: 13px; padding: 6px 12px;"
                                        on:click={() => openGameDriveLinks(result)}>
                                    Pobierz
                                </button>
                            {:else}
                                <button class="btn btn-primary"
                                        style="font-size: 13px; padding: 6px 12px;"
                                        on:click={() => addMagnetLink(result.magnetLink)}>
                                    Dodaj torrent
                                </button>
                            {/if}
                        </div>
                    </div>
                </div>
            {/each}

            <!-- Przycisk "Pokaż więcej" -->
            {#if visibleResults.length < searchResults.length}
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-outline" on:click={loadMoreResults} disabled={isLoadingMore}>
                        {#if isLoadingMore}
                            Ładowanie...<div class="loading"></div>
                        {:else}
                            Pokaż więcej wyników ({visibleResults.length} z {searchResults.length})
                        {/if}
                    </button>
                </div>
            {/if}
        {:else if zooSearchQuery.trim().length >= 3}
            <div class="empty-state">
                <p>Nie znaleziono wyników dla "{zooSearchQuery}".</p>
            </div>
        {:else if zooSearchQuery.trim().length > 0 && zooSearchQuery.trim().length < 3}
            <div class="empty-state">
                <p>Wpisz co najmniej 3 znaki, aby rozpocząć wyszukiwanie.</p>
            </div>
        {:else}
            <div class="empty-state">
                <p>Wpisz tytuł gry, aby rozpocząć wyszukiwanie.</p>
                <p style="margin-top: 10px; font-size: 14px; color: #aaa;">Wyszukiwanie rozpocznie się automatycznie po wpisaniu co najmniej 3 znaków.</p>
            </div>
        {/if}
    </div>
</div>

<style>
    .source-badge {
        display: inline-block;
        min-width: 60px;
        text-align: center;
    }

    .game-cover-container {
        width: 120px;
        height: 170px;
        flex-shrink: 0;
        overflow: hidden;
        border-radius: 4px;
        background-color: #1a1a1a;
    }

    .game-cover {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .game-cover:hover {
        transform: scale(1.05);
    }

    .game-cover-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #1a1a1a;
        color: #666;
        font-size: 12px;
        text-align: center;
    }

    .search-sources {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 10px;
    }

    .source-checkbox {
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: pointer;
        user-select: none;
    }

    .source-checkbox input[type="checkbox"] {
        width: 16px;
        height: 16px;
        cursor: pointer;
    }

    .source-name {
        font-size: 14px;
        color: var(--text-secondary, #aaa);
    }

    .progress-container {
        width: 100%;
        height: 8px;
        background-color: #2d2d2d;
        border-radius: 4px;
        overflow: hidden;
        margin: 8px 0;
    }

    .progress-bar {
        height: 100%;
        background-color: var(--primary-color, #4a6cf7);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .progress-details {
        margin-top: 15px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .progress-item {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .progress-item span {
        font-size: 12px;
        color: var(--text-secondary, #aaa);
    }

    .progress-item .progress-container {
        height: 6px;
    }

    /* Discord settings styles */
    .btn-discord {
        background-color: #5865F2;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
    }

    .btn-discord:hover {
        background-color: #4752c4;
    }

    .config-panel {
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .config-section {
        margin-bottom: 20px;
    }

    .config-section h4 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
        color: #fff;
    }

    .form-group {
        margin-bottom: 16px;
    }

    .form-group label {
        display: block;
        margin-bottom: 6px;
        color: var(--text-secondary);
    }

    .form-group input[type="text"],
    .form-group input[type="password"] {
        width: 100%;
        padding: 10px;
        background-color: #1e1e1e;
        border: 1px solid #444;
        border-radius: 4px;
        color: var(--text);
    }

    .input-help {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
        line-height: 1.4;
    }

    .label-text {
        display: block;
        margin-bottom: 6px;
        color: var(--text-secondary);
    }

    .checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .checkbox-group label {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
    }

    .config-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }



    /* Discord bot status styles */
    .bot-status {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-top: 5px;
    }

    .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #f04747;
        display: inline-block;
    }

    .status-indicator.active .status-dot {
        background-color: #43b581;
    }

    .status-text {
        font-size: 14px;
        color: var(--text-secondary);
    }

    .bot-controls {
        display: flex;
        gap: 10px;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 14px;
    }

    .refresh-icon {
        display: inline-block;
        font-size: 16px;
        font-weight: bold;
        transform: rotate(0deg);
        transition: transform 0.3s ease;
    }

    button:hover .refresh-icon {
        transform: rotate(180deg);
    }

    .debug-info {
        background-color: rgba(0, 0, 0, 0.2);
        padding: 10px;
        border-radius: 4px;
        font-size: 14px;
    }

    .debug-info p {
        margin: 5px 0;
    }

    .discord-install-link {
        display: inline-block;
        background-color: #5865F2;
        color: white;
        padding: 10px 16px;
        border-radius: 4px;
        text-decoration: none;
        font-weight: 500;
        margin-top: 10px;
        transition: background-color 0.2s;
    }

    .discord-install-link:hover {
        background-color: #4752c4;
        text-decoration: none;
    }
</style>
