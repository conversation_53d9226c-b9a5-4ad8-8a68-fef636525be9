<script lang="ts">
    import { onMount } from 'svelte';
    import { Book, Search, Loader2, Download, ExternalLink, Star, StarOff, History, ArrowLeft } from 'lucide-svelte';
    import { getWithAuth } from '$lib/utils/apiUtils';

    // State variables
    let bookSearchQuery = '';
    let searchResults: any[] = [];
    let isSearching = false;
    let searchError: string | null = null;
    let selectedBook: any = null;
    let isLoadingDetails = false;
    let downloadLinks: any[] = [];

    // Watchlist functionality
    let watchlist: any[] = [];
    let showWatchlist = false;

    // History functionality
    let recentlyViewed: any[] = [];
    let showHistory = false;
    const MAX_HISTORY_ITEMS = 8;

    // Function to handle search input changes with debounce
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 500; // ms

    function handleSearchInput() {
        // Clear any existing timer
        clearTimeout(typingTimer);

        // Reset results if search query is cleared
        if (bookSearchQuery.trim().length === 0) {
            searchResults = [];
            return;
        }

        // Start a new timer for search
        if (bookSearchQuery.trim().length >= 3) {
            // Set a timer to execute search after the debounce interval
            typingTimer = setTimeout(() => {
                searchBooks();
            }, doneTypingInterval);
        }
    }

    // Function to handle key press in search input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && bookSearchQuery.trim().length >= 3) {
            // Clear any existing timer to prevent duplicate searches
            clearTimeout(typingTimer);
            searchBooks();
        }
    }

    // Function to search for books
    async function searchBooks() {
        if (bookSearchQuery.trim().length < 3) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wpisz co najmniej 3 znaki', 'error');
            }
            return;
        }

        isSearching = true;
        searchResults = [];
        searchError = null;
        selectedBook = null;
        showHistory = false;
        showWatchlist = false;

        try {
            const response = await getWithAuth(`/api/necromicon/search?query=${encodeURIComponent(bookSearchQuery)}`);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Error ${response.status}`);
            }

            const data = await response.json();
            searchResults = data.results || [];

            if (searchResults.length === 0) {
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(`Nie znaleziono wyników dla "${bookSearchQuery}"`, 'error');
                }
            }
        } catch (error: any) {
            console.error('Error searching books:', error);
            searchError = error.message || 'Unknown error';

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd wyszukiwania: ${searchError}`, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Function to get book download links
    async function getBookDownloadLinks(book: any) {
        selectedBook = book;
        isLoadingDetails = true;
        downloadLinks = [];
        showHistory = false;
        showWatchlist = false;

        try {
            console.log(`Getting download links for book: ${book.title} (${book.md5})`);
            const response = await getWithAuth(`/api/necromicon/download?md5=${encodeURIComponent(book.md5)}`);

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `Error ${response.status}`);
            }

            downloadLinks = data.downloadLinks || [];
            console.log(`Found ${downloadLinks.length} download links`);

            // Add to recently viewed
            addToRecentlyViewed(book);

            // Show notification if no download links found
            if (downloadLinks.length === 0 && typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Nie znaleziono linków do pobrania dla "${book.title}"`, 'warning');
            }

        } catch (error: any) {
            console.error('Error getting book details:', error);
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(`Błąd pobierania szczegółów: ${error.message}`, 'error');
            }
        } finally {
            isLoadingDetails = false;
        }
    }

    // Function to go back to book list
    function backToBookList() {
        selectedBook = null;
    }

    // Function to toggle watchlist
    function toggleWatchlist(book: any, event?: Event) {
        if (event) {
            event.stopPropagation();
        }

        const index = watchlist.findIndex(item => item.md5 === book.md5);

        if (index === -1) {
            // Add to watchlist
            watchlist = [...watchlist, book];
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Dodano "${book.title}" do listy`, 'success');
            }
        } else {
            // Remove from watchlist
            watchlist = watchlist.filter(item => item.md5 !== book.md5);
            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Usunięto "${book.title}" z listy`, 'info');
            }
        }

        // Save to localStorage
        if (typeof window !== 'undefined') {
            localStorage.setItem('necromicon_watchlist', JSON.stringify(watchlist));
        }
    }

    // Function to check if a book is in the watchlist
    function isInWatchlist(md5: string) {
        return watchlist.some(item => item.md5 === md5);
    }

    // Function to toggle watchlist view
    function toggleWatchlistView() {
        showWatchlist = !showWatchlist;
        if (showWatchlist) {
            showHistory = false;
        }
    }

    // Function to add a book to recently viewed
    function addToRecentlyViewed(book: any) {
        // Remove if already exists
        recentlyViewed = recentlyViewed.filter(item => item.md5 !== book.md5);

        // Add to the beginning
        recentlyViewed = [book, ...recentlyViewed].slice(0, MAX_HISTORY_ITEMS);

        // Save to localStorage
        if (typeof window !== 'undefined') {
            localStorage.setItem('necromicon_history', JSON.stringify(recentlyViewed));
        }
    }

    // Function to toggle history view
    function toggleHistory() {
        showHistory = !showHistory;
        if (showHistory) {
            showWatchlist = false;
        }
    }

    // Function to load a book from watchlist or history
    function loadBookFromList(md5: string) {
        const book = [...watchlist, ...recentlyViewed].find(item => item.md5 === md5);
        if (book) {
            getBookDownloadLinks(book);
        }
    }

    // Load watchlist and history from localStorage on mount
    onMount(() => {
        if (typeof window !== 'undefined') {
            try {
                const savedWatchlist = localStorage.getItem('necromicon_watchlist');
                if (savedWatchlist) {
                    watchlist = JSON.parse(savedWatchlist);
                }

                const savedHistory = localStorage.getItem('necromicon_history');
                if (savedHistory) {
                    recentlyViewed = JSON.parse(savedHistory);
                }

                // Focus on search input
                setTimeout(() => {
                    const searchInput = document.getElementById('bookSearchQuery');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }, 100);
            } catch (error) {
                console.error('Error loading data from localStorage:', error);
            }
        }
    });
</script>

<div class="rounded-tl-none rounded-bl-none card">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="mb-1 text-2xl font-semibold">Necromicon (BETA)</h1>
            <p class="text-[var(--text-secondary)]">Wyszukaj i pobierz książki</p>
        </div>
        {#if selectedBook === null}
            <div class="header-actions">
                <button class="btn btn-icon" on:click={toggleWatchlistView} title="Lista do przeczytania" class:active={showWatchlist}>
                    <Star size={24} />
                </button>
                <button class="btn btn-icon" on:click={toggleHistory} title="Historia przeglądania" class:active={showHistory}>
                    <History size={24} />
                </button>
            </div>
        {/if}
    </div>

    <!-- Main content area -->
    {#if selectedBook === null}
        <!-- Search and results view -->

        <!-- Search input -->
        <div style="display: flex; gap: 10px; position: relative;">
            <input type="text" id="bookSearchQuery" class="magnet-input"
                   style="flex: 1; padding-right: 40px;"
                   placeholder="Wpisz tytuł książki lub autora (min. 3 znaki)..."
                   bind:value={bookSearchQuery}
                   on:input={handleSearchInput}
                   on:keypress={handleKeyPress}
                   disabled={isSearching}>

            {#if isSearching}
                <div class="search-loading-indicator">
                    <Loader2 size={20} class="loading-spinner" />
                </div>
            {:else if bookSearchQuery.trim().length > 0}
                <button class="search-clear-button" on:click={() => { bookSearchQuery = ''; searchResults = []; }}>
                    &times;
                </button>
            {/if}

            <button class="btn btn-primary" on:click={searchBooks} disabled={isSearching || bookSearchQuery.trim().length < 3}>
                <Search size={18} />
                <span>Szukaj</span>
            </button>
        </div>

        {#if showWatchlist && watchlist.length > 0}
            <!-- Watchlist -->
            <div class="section-title">
                <h2>Lista do przeczytania</h2>
            </div>
            <div class="book-grid">
                {#each watchlist as book}
                    <div class="book-card">
                        <div
                            class="book-card-inner"
                            on:click={() => loadBookFromList(book.md5)}
                            on:keydown={(e) => e.key === 'Enter' && loadBookFromList(book.md5)}
                            role="button"
                            tabindex="0"
                        >
                            <div class="book-cover">
                                {#if book.coverImg}
                                    <img src={book.coverImg} alt={book.title} loading="lazy">
                                {:else}
                                    <div class="book-cover-placeholder">
                                        <Book size={32} />
                                    </div>
                                {/if}
                                <div
                                    class="watchlist-button"
                                    on:click={(e) => toggleWatchlist(book, e)}
                                    on:keydown={(e) => e.key === 'Enter' && toggleWatchlist(book, e)}
                                    title="Usuń z listy"
                                    role="button"
                                    tabindex="0"
                                >
                                    <StarOff size={20} />
                                </div>
                                <div class="book-format">{book.format}</div>
                            </div>
                            <div class="book-info">
                                <h3 class="book-title">{book.title}</h3>
                                <p class="book-author">{book.author}</p>
                                <p class="book-year">{book.year}</p>
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
        {:else if showHistory && recentlyViewed.length > 0}
            <!-- Recently viewed -->
            <div class="section-title">
                <h2>Ostatnio przeglądane</h2>
            </div>
            <div class="book-grid">
                {#each recentlyViewed as book}
                    <div class="book-card">
                        <div
                            class="book-card-inner"
                            on:click={() => loadBookFromList(book.md5)}
                            on:keydown={(e) => e.key === 'Enter' && loadBookFromList(book.md5)}
                            role="button"
                            tabindex="0"
                        >
                            <div class="book-cover">
                                {#if book.coverImg}
                                    <img src={book.coverImg} alt={book.title} loading="lazy">
                                {:else}
                                    <div class="book-cover-placeholder">
                                        <Book size={32} />
                                    </div>
                                {/if}
                                <div
                                    class="watchlist-button"
                                    class:watchlist-active={isInWatchlist(book.md5)}
                                    on:click={(e) => toggleWatchlist(book, e)}
                                    on:keydown={(e) => e.key === 'Enter' && toggleWatchlist(book, e)}
                                    title={isInWatchlist(book.md5) ? "Usuń z listy" : "Dodaj do listy"}
                                    role="button"
                                    tabindex="0"
                                >
                                    {#if isInWatchlist(book.md5)}
                                        <Star size={20} />
                                    {:else}
                                        <Star size={20} />
                                    {/if}
                                </div>
                                <div class="book-format">{book.format}</div>
                            </div>
                            <div class="book-info">
                                <h3 class="book-title">{book.title}</h3>
                                <p class="book-author">{book.author}</p>
                                <p class="book-year">{book.year}</p>
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
        {:else if isSearching}
            <!-- Loading state -->
            <div class="empty-state">
                <Loader2 size={32} class="loading-spinner" />
                <p>Wyszukiwanie...</p>
            </div>
        {:else if searchError}
            <!-- Error state -->
            <div class="empty-state">
                <p>Wystąpił błąd podczas wyszukiwania.<br/>{searchError}</p>
                <button class="btn btn-outline" style="margin-top: 10px;" on:click={searchBooks}>Spróbuj ponownie</button>
            </div>
        {:else if searchResults.length > 0}
            <!-- Search results -->
            <div class="mt-6 section-title">
                <h2>Wyniki wyszukiwania</h2>
            </div>
            <div class="book-grid">
                {#each searchResults as book}
                    <div class="book-card">
                        <div
                            class="book-card-inner"
                            on:click={() => getBookDownloadLinks(book)}
                            on:keydown={(e) => e.key === 'Enter' && getBookDownloadLinks(book)}
                            role="button"
                            tabindex="0"
                            aria-label="Pokaż szczegóły książki {book.title}"
                        >
                            <div class="book-cover">
                                {#if book.coverImg}
                                    <img src={book.coverImg} alt={book.title} loading="lazy">
                                {:else}
                                    <div class="book-cover-placeholder">
                                        <Book size={32} />
                                    </div>
                                {/if}
                                <div
                                    class="watchlist-button"
                                    class:watchlist-active={isInWatchlist(book.md5)}
                                    on:click={(e) => toggleWatchlist(book, e)}
                                    on:keydown={(e) => e.key === 'Enter' && toggleWatchlist(book, e)}
                                    title={isInWatchlist(book.md5) ? "Usuń z listy" : "Dodaj do listy"}
                                    role="button"
                                    tabindex="0"
                                >
                                    {#if isInWatchlist(book.md5)}
                                        <Star size={20} />
                                    {:else}
                                        <Star size={20} />
                                    {/if}
                                </div>
                                <div class="book-format">{book.format}</div>
                            </div>
                            <div class="book-info">
                                <h3 class="book-title">{book.title}</h3>
                                <p class="book-author">{book.author}</p>
                                <p class="book-year">{book.year}</p>
                                <p class="book-size">{book.fileSize}</p>
                            </div>
                        </div>
                    </div>
                {/each}
            </div>
        {:else if bookSearchQuery.trim().length >= 3}
            <!-- No results -->
            <div class="empty-state">
                <p>Nie znaleziono wyników dla "{bookSearchQuery}"</p>
            </div>
        {:else}
            <!-- Initial state -->
            <div class="empty-state">
                <p>Wpisz tytuł książki lub autora, aby rozpocząć wyszukiwanie</p>
            </div>
        {/if}
    {:else}
        <!-- Book details and download links view -->
        <button
            class="back-button"
            on:click={backToBookList}
            aria-label="Powrót do listy książek"
        >
            <ArrowLeft size={20} />
            <span>Powrót do listy</span>
        </button>

        <div class="book-details">
            <div class="book-details-header">
                <div class="book-details-cover">
                    {#if selectedBook.coverImg}
                        <img src={selectedBook.coverImg} alt={selectedBook.title} loading="lazy">
                    {:else}
                        <div class="book-cover-placeholder large">
                            <Book size={64} />
                        </div>
                    {/if}
                    <div
                        class="watchlist-button large"
                        class:watchlist-active={isInWatchlist(selectedBook.md5)}
                        on:click={() => toggleWatchlist(selectedBook)}
                        on:keydown={(e) => e.key === 'Enter' && toggleWatchlist(selectedBook)}
                        title={isInWatchlist(selectedBook.md5) ? "Usuń z listy" : "Dodaj do listy"}
                        role="button"
                        tabindex="0"
                    >
                        {#if isInWatchlist(selectedBook.md5)}
                            <Star size={24} />
                        {:else}
                            <Star size={24} />
                        {/if}
                    </div>
                </div>
                <div class="book-details-info">
                    <h2 class="book-details-title">{selectedBook.title}</h2>
                    <p class="book-details-author">{selectedBook.author}</p>
                    <p class="book-details-year">{selectedBook.year}</p>
                    <div class="book-details-meta">
                        <span class="book-details-format">{selectedBook.format}</span>
                        <span class="book-details-size">{selectedBook.fileSize}</span>
                        <span class="book-details-language">{selectedBook.language}</span>
                    </div>
                </div>
            </div>

            <div class="book-details-section">
                <h3>Opcje pobierania</h3>

                {#if isLoadingDetails}
                    <div class="loading-container">
                        <Loader2 size={32} class="loading-spinner" />
                        <p>Ładowanie linków do pobrania...</p>
                    </div>
                {:else if downloadLinks.length === 0}
                    <div class="empty-state">
                        <p>Nie znaleziono linków do pobrania</p>
                        <p class="mt-4 text-sm">Spróbuj ponownie lub odwiedź <a href={`https://annas-archive.org/md5/${selectedBook.md5}`} target="_blank" rel="noopener noreferrer" class="text-pink-500 hover:underline">stronę książki</a> bezpośrednio w archiwum Ani.</p>
                        <button class="mt-4 btn btn-outline" on:click={() => getBookDownloadLinks(selectedBook)}>
                            <Loader2 size={18} />
                            <span>Spróbuj ponownie</span>
                        </button>
                    </div>
                {:else}
                    <div class="download-links">
                        <!-- Preferred slow download links first (with star) -->
                        {#if downloadLinks.some(link => link.type === 'slow' && link.isPreferred)}
                            <div class="download-section">
                                <h4>Zalecane pobieranie</h4>
                                {#each downloadLinks.filter(link => link.type === 'slow' && link.isPreferred) as link}
                                    <div class="download-link-item slow preferred">
                                        <div class="download-link-info">
                                            <span class="download-link-label">{link.label}</span>
                                            <span class="download-link-url">{link.url}</span>
                                        </div>
                                        <div class="download-link-actions">
                                            <a href={link.url} target="_blank" rel="noopener noreferrer" class="btn btn-primary">
                                                <Download size={18} />
                                                <span>Pobierz</span>
                                            </a>
                                            <button class="btn btn-outline" on:click={() => {
                                                if (typeof navigator !== 'undefined' && navigator.clipboard) {
                                                    navigator.clipboard.writeText(link.url);
                                                    if (typeof window !== 'undefined' && (window as any).showNotification) {
                                                        (window as any).showNotification('Link skopiowany do schowka', 'success');
                                                    }
                                                }
                                            }}>
                                                <ExternalLink size={18} />
                                                <span>Kopiuj link</span>
                                            </button>
                                        </div>
                                    </div>
                                {/each}
                            </div>
                        {/if}

                        <!-- Other slow download links -->
                        {#if downloadLinks.some(link => link.type === 'slow' && !link.isPreferred)}
                            <div class="download-section">
                                <h4>Wszystkie opcje pobierania</h4>
                                {#each downloadLinks.filter(link => link.type === 'slow' && !link.isPreferred) as link}
                                    <div class="download-link-item slow">
                                        <div class="download-link-info">
                                            <span class="download-link-label">{link.label}</span>
                                            <span class="download-link-url">{link.url}</span>
                                        </div>
                                        <div class="download-link-actions">
                                            <a href={link.url} target="_blank" rel="noopener noreferrer" class="btn btn-primary">
                                                <Download size={18} />
                                                <span>Pobierz</span>
                                            </a>
                                            <button class="btn btn-outline" on:click={() => {
                                                if (typeof navigator !== 'undefined' && navigator.clipboard) {
                                                    navigator.clipboard.writeText(link.url);
                                                    if (typeof window !== 'undefined' && (window as any).showNotification) {
                                                        (window as any).showNotification('Link skopiowany do schowka', 'success');
                                                    }
                                                }
                                            }}>
                                                <ExternalLink size={18} />
                                                <span>Kopiuj link</span>
                                            </button>
                                        </div>
                                    </div>
                                {/each}
                            </div>
                        {/if}

                        <!-- Direct download links -->
                        {#if downloadLinks.some(link => link.type === 'direct')}
                            <div class="download-section">
                                <h4>Bezpośrednie linki</h4>
                                {#each downloadLinks.filter(link => link.type === 'direct') as link}
                                    <div class="download-link-item direct">
                                        <div class="download-link-info">
                                            <span class="download-link-label">{link.label}</span>
                                            <span class="download-link-url">{link.url}</span>
                                        </div>
                                        <div class="download-link-actions">
                                            <a href={link.url} target="_blank" rel="noopener noreferrer" class="btn btn-primary">
                                                <Download size={18} />
                                                <span>Pobierz</span>
                                            </a>
                                            <button class="btn btn-outline" on:click={() => {
                                                if (typeof navigator !== 'undefined' && navigator.clipboard) {
                                                    navigator.clipboard.writeText(link.url);
                                                    if (typeof window !== 'undefined' && (window as any).showNotification) {
                                                        (window as any).showNotification('Link skopiowany do schowka', 'success');
                                                    }
                                                }
                                            }}>
                                                <ExternalLink size={18} />
                                                <span>Kopiuj link</span>
                                            </button>
                                        </div>
                                    </div>
                                {/each}
                            </div>
                        {/if}

                        <!-- External links section -->
                        {#if downloadLinks.some(link => link.type === 'external')}
                            <div class="external-links-section">
                                <h4>Zewnętrzne źródła</h4>
                                {#each downloadLinks.filter(link => link.type === 'external') as link}
                                    <div class="download-link-item external">
                                        <div class="download-link-info">
                                            <span class="download-link-label">{link.label}</span>
                                        </div>
                                        <div class="download-link-actions">
                                            <a href={link.url} target="_blank" rel="noopener noreferrer" class="btn btn-outline">
                                                <ExternalLink size={18} />
                                                <span>Otwórz</span>
                                            </a>
                                        </div>
                                    </div>
                                {/each}
                            </div>
                        {/if}

                        <div class="download-help">
                            <p>Jeśli masz problemy z pobieraniem:</p>
                            <ul>
                                <li>Spróbuj innego linku</li>
                                <li>Użyj menedżera pobierania jak <a href="https://jdownloader.org/" target="_blank" rel="noopener noreferrer" class="text-pink-500 hover:underline">JDownloader</a></li>
                                <li>Odwiedź <a href={`https://annas-archive.org/md5/${selectedBook.md5}`} target="_blank" rel="noopener noreferrer" class="text-pink-500 hover:underline">stronę książki</a> bezpośrednio w archiwum Ani</li>
                            </ul>
                        </div>
                    </div>
                {/if}
            </div>
        </div>
    {/if}
</div>

<style>
    .header-actions {
        display: flex;
        gap: 8px;
    }

    .btn-icon {
        padding: 8px;
        border-radius: 4px;
        background-color: var(--button-bg, #333);
    }

    .btn-icon:hover {
        background-color: var(--button-hover-bg, #444);
    }

    .btn-icon.active {
        background-color: rgba(236, 72, 153, 0.1);
        color: var(--primary);
    }

    .search-loading-indicator {
        position: absolute;
        right: 120px;
        top: 50%;
        transform: translateY(-50%);
    }

    .search-clear-button {
        position: absolute;
        right: 120px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 24px;
        line-height: 1;
        color: var(--text-secondary);
        background: none;
        border: none;
        cursor: pointer;
        padding: 0 8px;
    }

    .section-title {
        margin: 24px 0 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border);
    }

    .section-title h2 {
        font-size: 1.25rem;
        font-weight: 600;
    }

    .book-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }

    .book-card {
        background-color: var(--card-bg);
        border-radius: 8px;
        overflow: hidden;
        transition: transform 0.2s, box-shadow 0.2s;
        height: 100%;
    }

    .book-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .book-card-inner {
        display: flex;
        flex-direction: column;
        height: 100%;
        cursor: pointer;
    }

    .book-cover {
        position: relative;
        aspect-ratio: 2/3;
        background-color: var(--bg-color);
        overflow: hidden;
    }

    .book-cover img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .book-cover-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background-color: var(--bg-color);
        color: var(--text-secondary);
    }

    .book-cover-placeholder.large {
        min-height: 200px;
    }

    .watchlist-button {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .watchlist-button:hover {
        background-color: rgba(0, 0, 0, 0.7);
    }

    .watchlist-button.watchlist-active {
        color: #ffb6c1;
    }

    .watchlist-button.large {
        width: 40px;
        height: 40px;
    }

    .book-format {
        position: absolute;
        bottom: 8px;
        left: 8px;
        padding: 4px 8px;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        border-radius: 4px;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .book-info {
        padding: 12px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .book-title {
        font-weight: 600;
        margin-bottom: 4px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .book-author {
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-bottom: 4px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .book-year {
        color: var(--text-secondary);
        font-size: 0.75rem;
        margin-top: auto;
    }

    .book-size {
        color: var(--text-secondary);
        font-size: 0.75rem;
        margin-top: 4px;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;
        color: var(--text-secondary);
    }

    :global(.loading-spinner) {
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    .back-button {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
        padding: 8px 12px;
        background-color: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        border-radius: 4px;
        transition: background-color 0.2s;
    }

    .back-button:hover {
        background-color: var(--bg-color);
        color: var(--text);
    }

    .book-details {
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .book-details-header {
        display: flex;
        gap: 24px;
    }

    .book-details-cover {
        position: relative;
        flex-shrink: 0;
        width: 200px;
        max-width: 100%;
    }

    .book-details-cover img {
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .book-details-info {
        flex-grow: 1;
    }

    .book-details-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .book-details-author {
        font-size: 1.125rem;
        color: var(--text-secondary);
        margin-bottom: 8px;
    }

    .book-details-year {
        font-size: 1rem;
        color: var(--text-secondary);
        margin-bottom: 16px;
    }

    .book-details-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-top: 8px;
    }

    .book-details-format,
    .book-details-size,
    .book-details-language {
        padding: 4px 8px;
        background-color: var(--bg-color);
        border-radius: 4px;
        font-size: 0.875rem;
    }

    .book-details-section {
        margin-top: 16px;
    }

    .book-details-section h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border);
    }

    .download-links {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .download-link-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background-color: var(--bg-color);
        border-radius: 8px;
    }

    .download-link-info {
        flex-grow: 1;
    }

    .download-link-actions {
        display: flex;
        gap: 8px;
    }

    .download-link-label {
        font-weight: 500;
        display: block;
    }

    .download-link-url {
        font-size: 0.75rem;
        color: var(--text-secondary);
        display: block;
        word-break: break-all;
        margin-top: 4px;
    }

    .download-link-item.direct {
        background-color: rgba(236, 72, 153, 0.1);
        border: 1px solid rgba(236, 72, 153, 0.2);
    }

    .download-link-item.slow {
        background-color: rgba(59, 130, 246, 0.1);
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .download-section,
    .external-links-section {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid var(--border);
    }

    .download-section:first-child,
    .external-links-section:first-child {
        margin-top: 0;
        padding-top: 0;
        border-top: none;
    }

    .download-section h4,
    .external-links-section h4 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 12px;
    }

    .download-link-item.preferred {
        background-color: rgba(236, 72, 153, 0.2);
        border: 1px solid rgba(236, 72, 153, 0.3);
    }

    .download-link-item.slow.preferred {
        background-color: rgba(59, 130, 246, 0.2);
        border: 1px solid rgba(59, 130, 246, 0.3);
    }

    .download-help {
        margin-top: 24px;
        padding: 16px;
        background-color: var(--bg-color);
        border-radius: 8px;
        font-size: 0.875rem;
    }

    .download-help p {
        margin-bottom: 8px;
        font-weight: 500;
    }

    .download-help ul {
        list-style-type: disc;
        padding-left: 20px;
    }

    .download-help li {
        margin-bottom: 4px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .book-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }

        .book-details-header {
            flex-direction: column;
        }

        .book-details-cover {
            width: 100%;
            max-width: 200px;
            margin: 0 auto;
        }

        .download-link-item {
            flex-direction: column;
            gap: 12px;
        }

        .download-link-actions {
            width: 100%;
            justify-content: space-between;
        }
    }
</style>
