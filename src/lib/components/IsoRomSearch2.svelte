<script lang="ts">
    import { onMount } from 'svelte';
    import { Save, Download, Loader2, Settings, AlertTriangle, ChevronDown, ChevronUp, ExternalLink } from 'lucide-svelte';
    import { getWithAuth, fetchWithAuth } from '$lib/utils/apiUtils';

    // State variables
    let isoRomSearchQuery = '';
    let searchResults: any[] = [];
    let isSearching = false;
    let isDownloading = false;
    let selectedGame: any = null;
    let errorMessage = '';
    let showDebugInfo = false;
    let debugInfo: any = null;
    let downloadLinks: string[] = [];
    let hostingType = '';
    let showLinks = false;

    // Debounce variables
    let typingTimer: ReturnType<typeof setTimeout>;
    const doneTypingInterval = 1500; // 1.5 seconds debounce time

    // Function to handle search input changes with debounce
    function handleSearchInput() {
        // Clear any existing timer
        clearTimeout(typingTimer);

        // Reset results if search query is cleared
        if (isoRomSearchQuery.trim().length === 0) {
            searchResults = [];
            return;
        }

        // Start a new timer for search
        if (isoRomSearchQuery.trim().length >= 3) {
            // Set a timer to execute search after the debounce interval
            typingTimer = setTimeout(() => {
                searchGames();
            }, doneTypingInterval);
        }
    }

    // Function to handle key press in search input
    function handleKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && isoRomSearchQuery.trim().length >= 3) {
            // Clear any existing timer to prevent duplicate searches
            clearTimeout(typingTimer);
            searchGames();
        }
    }

    // Function to search for games
    async function searchGames() {
        if (isoRomSearchQuery.trim().length < 3) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Wpisz co najmniej 3 znaki', 'error');
            }
            return;
        }

        isSearching = true;
        searchResults = [];
        errorMessage = '';
        debugInfo = null;
        showDebugInfo = false;

        try {
            const response = await getWithAuth(`/api/isorom2/search?query=${encodeURIComponent(isoRomSearchQuery)}`);

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `Błąd wyszukiwania: ${response.status}`);
            }

            searchResults = data.results || [];

            if (searchResults.length === 0) {
                errorMessage = 'Nie znaleziono gier';
                if (typeof window !== 'undefined' && (window as any).showAlert) {
                    (window as any).showAlert(errorMessage, 'error');
                }
            }
        } catch (error) {
            console.error('Błąd wyszukiwania:', error);
            errorMessage = 'Błąd wyszukiwania: ' + (error instanceof Error ? error.message : 'Nieznany błąd');

            if (error instanceof Error) {
                debugInfo = {
                    error: error.toString(),
                    stack: error.stack,
                    message: error.message
                };
            } else {
                debugInfo = {
                    error: String(error),
                    message: 'Nieznany błąd'
                };
            }

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(errorMessage, 'error');
            }
        } finally {
            isSearching = false;
        }
    }

    // Function to get download links for a game
    async function getDownloadLinks(game: any) {
        console.log('Getting download links for game:', game);

        if (!game.url) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Brak URL gry', 'error');
            }
            return;
        }

        // Set the selected game and downloading state
        selectedGame = game;
        isDownloading = true;
        downloadLinks = [];
        hostingType = '';
        showLinks = false;

        try {
            const response = await fetchWithAuth(`/api/isorom2/download`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ gameUrl: game.url })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `Błąd pobierania: ${response.status}`);
            }

            if (!data.success || !data.links || data.links.length === 0) {
                throw new Error('Nie znaleziono linków do pobrania');
            }

            // We have the download links
            downloadLinks = data.links;
            hostingType = data.hostingType;
            showLinks = true;

            // Update the selected game with the detail image URL if available
            if (data.imageUrl) {
                selectedGame = {
                    ...selectedGame,
                    detailImageUrl: data.imageUrl
                };
            }

            if (typeof window !== 'undefined' && (window as any).showNotification) {
                (window as any).showNotification(`Znaleziono ${downloadLinks.length} linków do pobrania (${hostingType})`, 'success');
            }

        } catch (error) {
            console.error('Błąd pobierania:', error);

            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert(
                    'Błąd pobierania: ' + (error instanceof Error ? error.message : 'Nieznany błąd'),
                    'error'
                );
            }
        } finally {
            isDownloading = false;
        }
    }

    // Function to add a link to the "Add Hosting Link" tab
    function addToUnrestrictLinks(links: string[]) {
        if (links.length === 0) {
            if (typeof window !== 'undefined' && (window as any).showAlert) {
                (window as any).showAlert('Brak linków do dodania', 'error');
            }
            return;
        }

        // Join links with newlines
        const linksText = links.join('\n');

        // Switch to the "Add Hosting Link" tab with the links
        switchTabWithLinks('hoster', linksText);

        if (typeof window !== 'undefined' && (window as any).showNotification) {
            (window as any).showNotification('Przekierowano do zakładki "Dodaj link z hostingu"', 'info');
        }
    }

    // Function to switch to a tab with links
    export let switchTabWithLinks: (tab: string, links: string) => void = () => {};

    // Toggle debug info display
    function toggleDebugInfo() {
        showDebugInfo = !showDebugInfo;
    }

    onMount(() => {
        // Focus on search input when component mounts
        const searchInput = document.getElementById('isoRomSearchQuery2');
        if (searchInput) {
            searchInput.focus();
        }
    });
</script>

<div class="card">
    <div class="flex items-center justify-between mb-4">
        <div>
            <h2>Szukaj gry (ISO/ROM #2)</h2>
            <p class="text-[var(--text-secondary)]">Wyszukaj i pobierz obrazy ISO i pliki ROM gier na konsole</p>
        </div>
    </div>

    <div style="display: flex; gap: 10px; margin-top: 20px; position: relative;">
        <input type="text" id="isoRomSearchQuery2" class="magnet-input"
               style="flex: 1; padding-right: 40px;"
               placeholder="Wpisz tytuł gry (min. 3 znaki)..."
               bind:value={isoRomSearchQuery}
               on:input={handleSearchInput}
               on:keypress={handleKeyPress}
               disabled={isSearching}>

        {#if isSearching}
            <div class="search-indicator">
                <div class="loading"></div>
            </div>
        {/if}
    </div>

    {#if errorMessage}
        <div class="mt-6 error-container">
            <button
                class="w-full text-left error-header"
                on:click={toggleDebugInfo}
                on:keydown={(e) => e.key === 'Enter' && toggleDebugInfo()}
                type="button"
                aria-expanded={showDebugInfo}
                aria-controls="debug-info-panel">
                <AlertTriangle size={20} class="text-[var(--error)]" />
                <span>{errorMessage}</span>
                {#if debugInfo}
                    <div class="ml-auto btn-icon-small">
                        {#if showDebugInfo}
                            <ChevronUp size={16} />
                        {:else}
                            <ChevronDown size={16} />
                        {/if}
                    </div>
                {/if}
            </button>

            {#if showDebugInfo && debugInfo}
                <div id="debug-info-panel" class="debug-info">
                    <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
                </div>
            {/if}
        </div>
    {/if}

    {#if searchResults.length > 0}
        <div class="search-results">
            <h3 class="mt-6 mb-3">Wyniki wyszukiwania</h3>
            <div class="results-list">
                {#each searchResults as game}
                    <div class="game-item">
                        <div class="game-info">
                            {#if game.imageUrl}
                                <div class="game-cover">
                                    <img src={game.imageUrl} alt={game.title} loading="lazy">
                                </div>
                            {:else}
                                <div class="game-icon">
                                    <Save size={24} />
                                </div>
                            {/if}
                            <div class="game-details">
                                <h4 class="game-title">{game.title}</h4>
                            </div>
                        </div>
                        <button class="btn btn-secondary download-btn"
                                on:click={() => getDownloadLinks(game)}
                                disabled={isDownloading && selectedGame?.url === game.url}>
                            {#if isDownloading && selectedGame?.url === game.url}
                                <div class="flex items-center gap-2">
                                    <Loader2 size={18} class="animate-spin" />
                                    <span>Szukanie...</span>
                                </div>
                            {:else}
                                <Download size={18} />
                            {/if}
                        </button>
                    </div>

                    {#if showLinks && selectedGame?.url === game.url && downloadLinks.length > 0}
                        <div class="download-links-container">
                            <div class="download-links-header">
                                <h5>Linki do pobrania ({hostingType})</h5>
                                <button class="btn btn-primary" on:click={() => addToUnrestrictLinks(downloadLinks)}>
                                    Dodaj wszystkie do zakładki "Dodaj link z hostingu"
                                </button>
                            </div>
                            {#if selectedGame.detailImageUrl}
                                <div class="game-detail-image">
                                    <img src={selectedGame.detailImageUrl} alt={selectedGame.title} loading="lazy">
                                </div>
                            {/if}
                            <div class="download-links-list">
                                {#each downloadLinks as link, index}
                                    <div class="download-link-item">
                                        <span class="download-link-number">#{index + 1}</span>
                                        <a href={link} target="_blank" rel="noopener noreferrer" class="download-link">
                                            {link.length > 60 ? link.substring(0, 60) + '...' : link}
                                            <ExternalLink size={14} />
                                        </a>
                                    </div>
                                {/each}
                            </div>
                        </div>
                    {/if}
                {/each}
            </div>
        </div>
    {:else if isSearching}
        <div class="searching-indicator">
            <div class="loading-large"></div>
            <p>Wyszukiwanie gier...</p>
        </div>
    {/if}
</div>

<style>
    .search-indicator {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
    }

    .loading {
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top: 2px solid var(--primary);
        animation: spin 1s linear infinite;
    }

    .loading-large {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        border-top: 4px solid var(--primary);
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .search-results {
        margin-top: 20px;
    }

    .results-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: 10px;
    }

    .game-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background-color: var(--item-bg);
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .game-item:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .game-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .game-icon {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        background-color: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
    }

    .game-cover {
        width: 80px;
        height: 80px;
        border-radius: 6px;
        overflow: hidden;
        background-color: rgba(0, 0, 0, 0.2);
    }

    .game-cover img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .game-detail-image {
        margin: 15px 0;
        text-align: center;
    }

    .game-detail-image img {
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .game-details {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .game-title {
        font-weight: 600;
        margin: 0;
        font-size: 1rem;
    }

    .download-btn {
        min-width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 12px;
    }

    .download-btn:disabled {
        opacity: 0.8;
        min-width: 130px;
    }

    .searching-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 40px;
        gap: 16px;
    }

    .error-container {
        background-color: rgba(var(--error-rgb, 248, 113, 113), 0.1);
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 20px;
    }

    .error-header {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 16px;
        cursor: pointer;
        font-weight: 500;
    }

    .btn-icon-small {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .btn-icon-small:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: var(--text);
    }

    .debug-info {
        padding: 12px 16px;
        background-color: rgba(0, 0, 0, 0.2);
        border-top: 1px solid rgba(var(--error-rgb, 248, 113, 113), 0.2);
        overflow-x: auto;
    }

    .debug-info pre {
        font-family: monospace;
        font-size: 0.85rem;
        white-space: pre-wrap;
        color: var(--text-secondary);
    }

    .download-links-container {
        margin-top: 8px;
        margin-bottom: 16px;
        padding: 12px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
    }

    .download-links-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    .download-links-header h5 {
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
    }

    .download-links-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .download-link-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
    }

    .download-link-number {
        font-weight: 600;
        color: var(--primary);
        min-width: 30px;
    }

    .download-link {
        display: flex;
        align-items: center;
        gap: 6px;
        color: var(--text);
        text-decoration: none;
        word-break: break-all;
    }

    .download-link:hover {
        text-decoration: underline;
        color: var(--primary);
    }
</style>
