<script lang="ts">
    import { AlertTriangle, ChevronDown, ChevronUp, XCircle } from 'lucide-svelte';
    
    /**
     * Error message to display
     */
    export let message: string;
    
    /**
     * Error type: 'error' (red) or 'warning' (yellow)
     */
    export let type: 'error' | 'warning' = 'error';
    
    /**
     * Whether to show a retry button
     */
    export let showRetry: boolean = false;
    
    /**
     * Function to call when retry button is clicked
     */
    export let onRetry: () => void = () => {};
    
    /**
     * Whether to show details (if available)
     */
    export let showDetails: boolean = false;
    
    /**
     * Detailed error information (optional)
     */
    export let details: any = null;
    
    /**
     * Toggle showing details
     */
    function toggleDetails() {
        showDetails = !showDetails;
    }
</script>

<div class="error-container {type}">
    <div class="error-header">
        <div class="error-icon">
            {#if type === 'error'}
                <XCircle size={20} />
            {:else}
                <AlertTriangle size={20} />
            {/if}
        </div>
        
        <div class="error-message">
            {message}
        </div>
        
        {#if details}
            <button 
                class="details-toggle" 
                on:click={toggleDetails}
                aria-expanded={showDetails}
                aria-controls="error-details"
            >
                {#if showDetails}
                    <ChevronUp size={16} />
                {:else}
                    <ChevronDown size={16} />
                {/if}
            </button>
        {/if}
    </div>
    
    {#if showDetails && details}
        <div id="error-details" class="error-details">
            {#if typeof details === 'string'}
                <p>{details}</p>
            {:else if typeof details === 'object'}
                <pre>{JSON.stringify(details, null, 2)}</pre>
            {/if}
        </div>
    {/if}
    
    {#if showRetry}
        <div class="error-actions">
            <button class="retry-button" on:click={onRetry}>
                Try Again
            </button>
        </div>
    {/if}
</div>

<style>
    .error-container {
        border-radius: 0.375rem;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .error-container.error {
        background-color: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .error-container.warning {
        background-color: rgba(245, 158, 11, 0.1);
        border: 1px solid rgba(245, 158, 11, 0.2);
    }
    
    .error-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .error-icon {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .error-container.error .error-icon {
        color: rgb(239, 68, 68);
    }
    
    .error-container.warning .error-icon {
        color: rgb(245, 158, 11);
    }
    
    .error-message {
        flex: 1;
        font-weight: 500;
    }
    
    .details-toggle {
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 0.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.25rem;
    }
    
    .details-toggle:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .error-details {
        margin-top: 0.75rem;
        padding: 0.75rem;
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 0.25rem;
        font-family: monospace;
        font-size: 0.875rem;
        overflow-x: auto;
    }
    
    .error-actions {
        margin-top: 1rem;
        display: flex;
        justify-content: flex-end;
    }
    
    .retry-button {
        background-color: var(--card-bg);
        color: var(--text);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 0.25rem;
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    
    .retry-button:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
</style>
