/**
 * Authentication store
 * Manages user authentication state and JWT token
 */

import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';

// Authentication state interface
interface AuthState {
    isAuthenticated: boolean;
    token: string | null;
    expiresAt: number | null;
}

// Initial state
const initialState: AuthState = {
    isAuthenticated: false,
    token: null,
    expiresAt: null
};

// Load state from localStorage if available
function loadInitialState(): AuthState {
    if (browser) {
        try {
            const savedToken = localStorage.getItem('auth_token');
            const savedExpiry = localStorage.getItem('auth_expires');
            
            if (savedToken && savedExpiry) {
                const expiresAt = parseInt(savedExpiry, 10);
                
                // Check if token is still valid
                if (expiresAt > Date.now()) {
                    return {
                        isAuthenticated: true,
                        token: savedToken,
                        expiresAt
                    };
                }
            }
        } catch (error) {
            console.error('Error loading auth state from localStorage:', error);
        }
    }
    return initialState;
}

// Create the store
function createAuthStore() {
    const { subscribe, update, set } = writable<AuthState>(loadInitialState());
    
    return {
        subscribe,
        
        /**
         * Set authentication state
         * @param token JWT token
         * @param expiresIn Expiration time in seconds
         */
        login: (token: string, expiresIn: number = 86400) => {
            const expiresAt = Date.now() + expiresIn * 1000;
            
            update(state => ({
                isAuthenticated: true,
                token,
                expiresAt
            }));
            
            // Save to localStorage
            if (browser) {
                localStorage.setItem('auth_token', token);
                localStorage.setItem('auth_expires', expiresAt.toString());
            }
        },
        
        /**
         * Clear authentication state
         */
        logout: () => {
            set(initialState);
            
            // Remove from localStorage
            if (browser) {
                localStorage.removeItem('auth_token');
                localStorage.removeItem('auth_expires');
            }
        },
        
        /**
         * Check if token is expired
         */
        checkExpiration: () => {
            update(state => {
                if (state.expiresAt && state.expiresAt < Date.now()) {
                    // Token expired, clear state
                    if (browser) {
                        localStorage.removeItem('auth_token');
                        localStorage.removeItem('auth_expires');
                    }
                    return initialState;
                }
                return state;
            });
        }
    };
}

// Export the store
export const authStore = createAuthStore();

// Derived store for checking authentication status
export const isAuthenticated = derived(
    authStore,
    $authStore => $authStore.isAuthenticated
);

// Derived store for getting the token
export const authToken = derived(
    authStore,
    $authStore => $authStore.token
);
