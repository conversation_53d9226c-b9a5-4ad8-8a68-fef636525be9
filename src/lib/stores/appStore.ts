/**
 * Central application state store
 * Manages global application state like theme, user preferences, and loading states
 */

import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';

// Theme types
export type ThemeType = 'dark' | 'light' | 'system';

// App state interface
interface AppState {
    // Theme settings
    theme: ThemeType;
    
    // Global loading state
    isLoading: boolean;
    loadingMessage: string | null;
    
    // Global error state
    error: string | null;
    
    // User preferences
    preferences: {
        showCovers: boolean;
        autoAddToDebrid: boolean;
        preferredSources: string[];
    };
}

// Initial state
const initialState: AppState = {
    theme: 'dark',
    isLoading: false,
    loadingMessage: null,
    error: null,
    preferences: {
        showCovers: true,
        autoAddToDebrid: true,
        preferredSources: ['fitgirl', 'dodi', 'gog']
    }
};

// Load state from localStorage if available
function loadInitialState(): AppState {
    if (browser) {
        try {
            const savedState = localStorage.getItem('appState');
            if (savedState) {
                return { ...initialState, ...JSON.parse(savedState) };
            }
        } catch (error) {
            console.error('Error loading app state from localStorage:', error);
        }
    }
    return initialState;
}

// Create the store
const createAppStore = () => {
    const { subscribe, update, set } = writable<AppState>(loadInitialState());
    
    // Save state to localStorage when it changes
    if (browser) {
        subscribe(state => {
            try {
                localStorage.setItem('appState', JSON.stringify(state));
            } catch (error) {
                console.error('Error saving app state to localStorage:', error);
            }
        });
    }
    
    return {
        subscribe,
        
        /**
         * Set theme
         * @param theme Theme to set
         */
        setTheme: (theme: ThemeType) => {
            update(state => ({ ...state, theme }));
            
            // Apply theme to document
            if (browser) {
                if (theme === 'system') {
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    document.documentElement.classList.toggle('dark', prefersDark);
                } else {
                    document.documentElement.classList.toggle('dark', theme === 'dark');
                }
            }
        },
        
        /**
         * Set loading state
         * @param isLoading Whether the app is loading
         * @param message Optional loading message
         */
        setLoading: (isLoading: boolean, message: string | null = null) => {
            update(state => ({ ...state, isLoading, loadingMessage: message }));
        },
        
        /**
         * Set error state
         * @param error Error message or null to clear
         */
        setError: (error: string | null) => {
            update(state => ({ ...state, error }));
        },
        
        /**
         * Update user preferences
         * @param preferences Partial preferences to update
         */
        updatePreferences: (preferences: Partial<AppState['preferences']>) => {
            update(state => ({
                ...state,
                preferences: { ...state.preferences, ...preferences }
            }));
        },
        
        /**
         * Reset state to initial values
         */
        reset: () => {
            set(initialState);
        }
    };
};

// Export the store
export const appStore = createAppStore();

// Derived stores for specific parts of the state
export const theme = derived(appStore, $appStore => $appStore.theme);
export const isLoading = derived(appStore, $appStore => $appStore.isLoading);
export const loadingMessage = derived(appStore, $appStore => $appStore.loadingMessage);
export const error = derived(appStore, $appStore => $appStore.error);
export const preferences = derived(appStore, $appStore => $appStore.preferences);
