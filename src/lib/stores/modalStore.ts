import { writable } from 'svelte/store';

export type ModalType = 'alert' | 'confirm';

export interface ModalButton {
    text: string;
    action: () => void;
    style?: 'primary' | 'outline' | 'error';
}

export interface ModalOptions {
    title: string;
    message: string;
    type: ModalType;
    buttons: ModalButton[];
}

// Default empty modal state
const defaultModalState = {
    isOpen: false,
    title: '',
    message: '',
    type: 'alert' as ModalType,
    buttons: []
};

// Create the store
const createModalStore = () => {
    const { subscribe, set, update } = writable<{
        isOpen: boolean;
        title: string;
        message: string;
        type: ModalType;
        buttons: ModalButton[];
    }>(defaultModalState);

    return {
        subscribe,
        
        // Show an alert modal with a single OK button
        alert: (title: string, message: string, onClose?: () => void) => {
            update(() => ({
                isOpen: true,
                title,
                message,
                type: 'alert',
                buttons: [
                    {
                        text: 'OK',
                        action: () => {
                            if (onClose) onClose();
                            set({ ...defaultModalState });
                        },
                        style: 'primary'
                    }
                ]
            }));
        },
        
        // Show a confirmation modal with Yes/No buttons
        confirm: (title: string, message: string, onConfirm: () => void, onCancel?: () => void) => {
            update(() => ({
                isOpen: true,
                title,
                message,
                type: 'confirm',
                buttons: [
                    {
                        text: 'Tak',
                        action: () => {
                            onConfirm();
                            set({ ...defaultModalState });
                        },
                        style: 'primary'
                    },
                    {
                        text: 'Nie',
                        action: () => {
                            if (onCancel) onCancel();
                            set({ ...defaultModalState });
                        },
                        style: 'outline'
                    }
                ]
            }));
        },
        
        // Show a custom modal with custom buttons
        custom: (options: ModalOptions) => {
            update(() => ({
                isOpen: true,
                title: options.title,
                message: options.message,
                type: options.type,
                buttons: options.buttons
            }));
        },
        
        // Close the modal
        close: () => {
            set({ ...defaultModalState });
        }
    };
};

// Export the store
export const modalStore = createModalStore();
