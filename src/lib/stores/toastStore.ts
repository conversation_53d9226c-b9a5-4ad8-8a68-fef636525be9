/**
 * Toast notification store
 * Manages toast notifications with different types and auto-dismiss
 */

import { writable } from 'svelte/store';

// Toast types
export type ToastType = 'success' | 'error' | 'warning' | 'info';

// Toast interface for internal use (with ID)
export interface Toast {
    id: string;
    type: ToastType;
    message: string;
    title?: string;
    duration?: number;
    dismissible?: boolean;
}

// Toast input interface (without ID, which will be generated)
export interface ToastInput {
    type: ToastType;
    message: string;
    title?: string;
    duration?: number;
    dismissible?: boolean;
}

// Default duration in milliseconds
const DEFAULT_DURATION = 5000;

// Create the store
const createToastStore = () => {
    const { subscribe, update } = writable<Toast[]>([]);

    // Generate a unique ID
    const generateId = (): string => {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    };

    return {
        subscribe,

        /**
         * Add a toast notification
         * @param toast Toast object, input object, or message string
         * @param type Toast type (if message is a string)
         * @param duration Duration in milliseconds (if message is a string)
         */
        add: (
            toast: ToastInput | string,
            type: ToastType = 'info',
            duration: number = DEFAULT_DURATION
        ) => {
            const id = generateId();

            // If toast is a string, create a Toast object
            const toastObj: Toast = typeof toast === 'string'
                ? { id, type, message: toast, duration }
                : { ...toast, id };

            // Set default duration if not provided
            if (toastObj.duration === undefined) {
                toastObj.duration = DEFAULT_DURATION;
            }

            // Add the toast to the store
            update(toasts => [...toasts, toastObj]);

            // Auto-dismiss if duration is greater than 0
            if (toastObj.duration > 0) {
                const removeToast = () => {
                    update(toasts => toasts.filter(t => t.id !== id));
                };

                setTimeout(removeToast, toastObj.duration);
            }

            return id;
        },

        /**
         * Remove a toast by ID
         * @param id Toast ID
         */
        remove: (id: string) => {
            update(toasts => toasts.filter(t => t.id !== id));
        },

        /**
         * Clear all toasts
         */
        clear: () => {
            update(() => []);
        },

        /**
         * Show a success toast
         * @param message Toast message
         * @param title Optional toast title
         * @param duration Optional duration in milliseconds
         */
        success: (message: string, title?: string, duration?: number) => {
            return toastStore.add({ type: 'success', message, title, duration });
        },

        /**
         * Show an error toast
         * @param message Toast message
         * @param title Optional toast title
         * @param duration Optional duration in milliseconds
         */
        error: (message: string, title?: string, duration?: number) => {
            return toastStore.add({ type: 'error', message, title, duration });
        },

        /**
         * Show a warning toast
         * @param message Toast message
         * @param title Optional toast title
         * @param duration Optional duration in milliseconds
         */
        warning: (message: string, title?: string, duration?: number) => {
            return toastStore.add({ type: 'warning', message, title, duration });
        },

        /**
         * Show an info toast
         * @param message Toast message
         * @param title Optional toast title
         * @param duration Optional duration in milliseconds
         */
        info: (message: string, title?: string, duration?: number) => {
            return toastStore.add({ type: 'info', message, title, duration });
        }
    };
};

// Export the store
export const toastStore = createToastStore();
