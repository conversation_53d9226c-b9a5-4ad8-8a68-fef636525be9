/**
 * Service for Trakt.tv API communication
 * Handles fetching watchlist and trending data
 */

import { fetchWithAuth, getWithAuth } from '$lib/utils/apiUtils';
import { TMDB_CONFIG } from '$lib/config/apiConfig';

// Trakt.tv API base URL
const TRAKT_API_PROXY = '/api/trakt';

/**
 * Interface for Trakt.tv show/movie item
 */
export interface TraktItem {
    type: 'show' | 'movie';
    title: string;
    year: number;
    ids: {
        trakt: number;
        slug: string;
        tvdb?: number;
        imdb?: string;
        tmdb?: number;
    };
    poster?: string | null; // Added after fetching from TMDB, can be null if image fails to load
    overview?: string; // Added after fetching from TMDB
}

/**
 * Get items from a specific Trakt.tv list
 * @param username Username of the list owner
 * @param listId ID or slug of the list
 * @param limit Maximum number of items to return (default: no limit)
 * @returns Promise with list items
 */
export async function getTraktList(username: string, listId: string, limit?: number): Promise<TraktItem[]> {
    try {
        console.log(`[Trakt Service] Getting list: ${username}/${listId}${limit ? `, limit: ${limit}` : ''}`);

        // Build the URL with the correct endpoint format
        let url = `${TRAKT_API_PROXY}/users/${username}/lists/${listId}/items`;

        // Add limit parameter if specified
        if (limit && limit > 0) {
            url += `?limit=${limit}`;
        }

        const response = await fetchWithAuth(url);

        if (!response.ok) {
            console.error(`[Trakt Service] API Error: ${response.status}`);
            throw new Error(`API Error: ${response.status}`);
        }

        const data = await response.json();

        // Transform the data to a simpler format
        return data.map((item: any) => {
            // Each item has a type (show or movie) and the actual data is in a property with that name
            const type = item.type;
            const itemData = item[type];

            return {
                type,
                title: itemData.title,
                year: itemData.year,
                ids: itemData.ids
            };
        });
    } catch (error) {
        console.error('[Trakt Service] Error getting list:', error);

        // Log more detailed error information
        if (error.status) {
            console.error(`[Trakt Service] HTTP Status: ${error.status}`);
            console.error(`[Trakt Service] URL: ${error.url}`);
            console.error(`[Trakt Service] Method: ${error.method}`);
        }

        // For 405 errors specifically, provide a more helpful message
        if (error.status === 405) {
            console.error('[Trakt Service] Method Not Allowed (405) - The endpoint might have changed or the list might be private');
        }

        throw error;
    }
}

/**
 * Get trending shows from Trakt.tv
 * @param limit Number of items to fetch (default: 20)
 * @returns Promise with trending shows
 */
export async function getTrendingShows(limit: number = 20): Promise<TraktItem[]> {
    try {
        console.log(`[Trakt Service] Getting trending shows, limit: ${limit}`);

        const response = await fetchWithAuth(`${TRAKT_API_PROXY}/shows/trending?limit=${limit}`);

        if (!response.ok) {
            console.error(`[Trakt Service] API Error: ${response.status}`);
            throw new Error(`API Error: ${response.status}`);
        }

        const data = await response.json();

        // Transform the data to a simpler format
        return data.map((item: any) => ({
            type: 'show',
            title: item.show.title,
            year: item.show.year,
            ids: item.show.ids
        }));
    } catch (error) {
        console.error('[Trakt Service] Error getting trending shows:', error);
        throw error;
    }
}

/**
 * Get trending movies from Trakt.tv
 * @param limit Number of items to fetch (default: 20)
 * @returns Promise with trending movies
 */
export async function getTrendingMovies(limit: number = 20): Promise<TraktItem[]> {
    try {
        console.log(`[Trakt Service] Getting trending movies, limit: ${limit}`);

        const response = await fetchWithAuth(`${TRAKT_API_PROXY}/movies/trending?limit=${limit}`);

        if (!response.ok) {
            console.error(`[Trakt Service] API Error: ${response.status}`);
            throw new Error(`API Error: ${response.status}`);
        }

        const data = await response.json();

        // Transform the data to a simpler format
        return data.map((item: any) => ({
            type: 'movie',
            title: item.movie.title,
            year: item.movie.year,
            ids: item.movie.ids
        }));
    } catch (error) {
        console.error('[Trakt Service] Error getting trending movies:', error);
        throw error;
    }
}

/**
 * Enrich Trakt items with poster images from TMDB
 * @param items Trakt items to enrich
 * @returns Promise with enriched items
 */
export async function enrichWithTmdbData(items: TraktItem[]): Promise<TraktItem[]> {
    try {
        console.log(`[Trakt Service] Enriching ${items.length} items with TMDB data`);

        // Get TMDB configuration from centralized config
        const { API_KEY: TMDB_API_KEY, IMAGE_BASE_URL: TMDB_IMAGE_BASE_URL } = TMDB_CONFIG;

        const enrichedItems = await Promise.all(
            items.map(async (item) => {
                try {
                    // Jeśli mamy TMDB ID, użyjmy go bezpośrednio
                    if (item.ids.tmdb) {
                        const tmdbType = item.type === 'show' ? 'tv' : 'movie';
                        const tmdbUrl = `${TMDB_CONFIG.BASE_URL}/${tmdbType}/${item.ids.tmdb}?api_key=${TMDB_API_KEY}&language=pl-PL`;

                        console.log(`[Trakt Service] Fetching TMDB data for ${item.title} (${tmdbType}, ID: ${item.ids.tmdb})`);

                        try {
                            // Używamy zwykłego fetch, ponieważ to zewnętrzne API, nie nasze
                            const response = await fetch(tmdbUrl);

                            if (response.ok) {
                                const data = await response.json();

                                if (data.poster_path) {
                                    const originalPosterUrl = `${TMDB_IMAGE_BASE_URL}${data.poster_path}`;
                                    // Use proxy for poster to avoid CORS issues
                                    // We'll use getWithAuth to fetch the image later, just store the URL for now
                                    const posterUrl = `/api/image/proxy?url=${encodeURIComponent(originalPosterUrl)}`;
                                    console.log(`[Trakt Service] Found poster for ${item.title}: ${posterUrl}`);
                                    return {
                                        ...item,
                                        poster: posterUrl,
                                        overview: data.overview || ''
                                    };
                                } else {
                                    console.log(`[Trakt Service] No poster_path found for ${item.title} in TMDB data`);
                                }
                            } else {
                                console.error(`[Trakt Service] TMDB API error for ${item.title}: ${response.status}`);
                            }
                        } catch (tmdbError) {
                            console.error(`[Trakt Service] Error fetching TMDB data for ${item.title}:`, tmdbError);
                        }
                    }

                    // Jeśli nie mamy TMDB ID lub nie udało się pobrać danych, spróbujmy z OMDB jako fallback
                    console.log(`[Trakt Service] Trying OMDB fallback for ${item.title}`);

                    // OMDB endpoint obsługuje tylko parametr title, nie id
                    const url = `/api/omdb?title=${encodeURIComponent(item.title)}`;
                    console.log(`[Trakt Service] Using title for OMDB: ${item.title}`);

                    try {
                        // Use getWithAuth instead of fetch to include the JWT token
                        const response = await getWithAuth(url);

                        if (response.ok) {
                            const data = await response.json();
                            console.log(`[Trakt Service] OMDB response for ${item.title}:`, data.Response);

                            if (data.Poster && data.Poster !== 'N/A') {
                                // Use proxy for OMDB poster to avoid CORS issues
                                const posterUrl = `/api/image/proxy?url=${encodeURIComponent(data.Poster)}`;
                                console.log(`[Trakt Service] Found OMDB poster for ${item.title}: ${posterUrl}`);
                                return {
                                    ...item,
                                    poster: posterUrl,
                                    overview: data.Plot || ''
                                };
                            } else {
                                console.log(`[Trakt Service] No valid poster found in OMDB for ${item.title}`);
                            }
                        } else {
                            console.error(`[Trakt Service] OMDB API error for ${item.title}: ${response.status}`);
                        }
                    } catch (omdbError) {
                        console.error(`[Trakt Service] Error fetching OMDB data for ${item.title}:`, omdbError);
                    }
                } catch (error) {
                    console.error(`[Trakt Service] Error enriching item ${item.title}:`, error);
                }

                // Jeśli wszystko zawiedzie, zwróć element bez okładki
                return item;
            })
        );

        return enrichedItems;
    } catch (error) {
        console.error('[Trakt Service] Error enriching items:', error);
        return items; // Return original items if enrichment fails
    }
}
