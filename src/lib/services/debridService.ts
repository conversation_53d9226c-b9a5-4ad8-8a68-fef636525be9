/**
 * Service for Real-Debrid API communication
 * Handles torrent management, link unrestriction, and related functionality
 */

import { fetchWithErrorHandling, cleanMediaTitle } from '$lib/utils/apiUtils';
import { REAL_DEBRID_CONFIG } from '$lib/config/apiConfig';

// API configuration
const { API_KEY, BASE_URL: API_BASE_URL } = REAL_DEBRID_CONFIG;

// Typy danych
export type TorrentCategory = 'all' | 'movies' | 'tvshows' | 'other';

export interface Torrent {
    id: string;
    filename: string;
    hash: string;
    bytes: number;
    host: string;
    split: number;
    progress: number;
    status: string;
    added: string;
    links: string[];
    ended: string | null;
    speed: number | null;
    seeders: number | null;
    files: TorrentFile[];
    category?: TorrentCategory; // Dodane pole kategorii
}

export interface TorrentFile {
    id: number;
    path: string;
    bytes: number;
    selected: number;
}

export interface UnrestrictedLink {
    id: string;
    filename: string;
    mimeType: string;
    filesize: number;
    link: string;
    host: string;
    chunks: number;
    download: string;
    streamable: number;
}

/**
 * Get list of torrents from Real-Debrid
 * @returns Promise with array of torrents with added category
 */
export async function getTorrents(): Promise<Torrent[]> {
    try {
        console.log(`[Debrid Service] Fetching torrents from: ${API_BASE_URL}/torrents`);

        const response = await fetchWithErrorHandling(`${API_BASE_URL}/torrents`, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`
            }
        });

        const torrents = await response.json();
        console.log(`[Debrid Service] Received ${torrents.length} torrents`);

        // Add category to each torrent
        return torrents.map((torrent: Torrent) => ({
            ...torrent,
            category: determineTorrentCategory(torrent)
        }));
    } catch (error) {
        console.error('[Debrid Service] Error fetching torrents list:', error);

        // Provide more detailed error message
        if (error instanceof Error) {
            const enhancedError = new Error(`Failed to fetch torrents: ${error.message}`);
            enhancedError.stack = error.stack;
            throw enhancedError;
        }

        throw error;
    }
}

/**
 * Get information about a specific torrent
 * @param torrentId ID of the torrent
 * @returns Promise with torrent information
 */
export async function getTorrentInfo(torrentId: string): Promise<Torrent> {
    try {
        const response = await fetchWithErrorHandling(`${API_BASE_URL}/torrents/info/${torrentId}`, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`
            }
        });

        return await response.json();
    } catch (error) {
        console.error(`Error fetching torrent info for ID ${torrentId}:`, error);
        throw error;
    }
}

/**
 * Add a magnet link to Real-Debrid
 * @param magnetUrl Magnet URL to add
 * @returns Promise with torrent ID
 */
export async function addMagnet(magnetUrl: string): Promise<{ id: string }> {
    try {
        console.log(`[Debrid Service] Adding magnet link to ${API_BASE_URL}/torrents/addMagnet`);
        console.log(`[Debrid Service] Magnet URL: ${magnetUrl.substring(0, 50)}...`);

        const response = await fetchWithErrorHandling(`${API_BASE_URL}/torrents/addMagnet`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `magnet=${encodeURIComponent(magnetUrl)}`
        });

        // Get the response text first to check if it's empty or invalid JSON
        const responseText = await response.text();
        console.log(`[Debrid Service] Raw response from addMagnet: ${responseText}`);

        // Only parse as JSON if there's actual content
        if (responseText && responseText.trim()) {
            try {
                const result = JSON.parse(responseText);
                console.log(`[Debrid Service] Parsed response:`, result);

                if (!result.id) {
                    throw new Error(`Missing torrent ID in response: ${JSON.stringify(result)}`);
                }

                return result;
            } catch (jsonError) {
                console.error(`[Debrid Service] Error parsing JSON response: ${jsonError.message}`);
                console.error(`[Debrid Service] Raw response: ${responseText}`);
                throw new Error(`Invalid JSON response: ${jsonError.message}`);
            }
        } else {
            console.error(`[Debrid Service] Empty response from addMagnet`);
            throw new Error('Empty response from server when adding magnet');
        }
    } catch (error) {
        console.error('[Debrid Service] Error adding magnet link:', error);

        // Enhance error message
        if (error instanceof Error) {
            const enhancedError = new Error(`Failed to add magnet: ${error.message}`);
            enhancedError.stack = error.stack;
            throw enhancedError;
        }

        throw error;
    }
}

/**
 * Add a torrent file to Real-Debrid
 * @param fileData Binary torrent file data
 * @returns Promise with torrent ID
 */
export async function addTorrentFile(fileData: ArrayBuffer): Promise<{ id: string }> {
    try {
        console.log(`[Debrid Service] Adding torrent file to ${API_BASE_URL}/torrents/addTorrent`);

        const response = await fetchWithErrorHandling(`${API_BASE_URL}/torrents/addTorrent`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${API_KEY}`
                // No Content-Type for binary file uploads
            },
            body: fileData
        });

        // Get the response text first to check if it's empty or invalid JSON
        const responseText = await response.text();
        console.log(`[Debrid Service] Raw response from addTorrent: ${responseText}`);

        // Only parse as JSON if there's actual content
        if (responseText && responseText.trim()) {
            try {
                const result = JSON.parse(responseText);
                console.log(`[Debrid Service] Parsed response:`, result);

                if (!result.id) {
                    throw new Error(`Missing torrent ID in response: ${JSON.stringify(result)}`);
                }

                return result;
            } catch (jsonError) {
                console.error(`[Debrid Service] Error parsing JSON response: ${jsonError.message}`);
                console.error(`[Debrid Service] Raw response: ${responseText}`);
                throw new Error(`Invalid JSON response: ${jsonError.message}`);
            }
        } else {
            console.error(`[Debrid Service] Empty response from addTorrent`);
            throw new Error('Empty response from server when adding torrent');
        }
    } catch (error) {
        console.error('[Debrid Service] Error adding torrent file:', error);

        // Enhance error message
        if (error instanceof Error) {
            const enhancedError = new Error(`Failed to add torrent: ${error.message}`);
            enhancedError.stack = error.stack;
            throw enhancedError;
        }

        throw error;
    }
}

/**
 * Select files in a torrent (selects all files by default)
 * @param torrentId ID of the torrent
 * @returns Promise that resolves when files are selected
 */
export async function selectFiles(torrentId: string): Promise<void> {
    try {
        // Add delay to ensure torrent is properly registered in Real-Debrid
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Get torrent information
        const infoResponse = await fetchWithErrorHandling(`${API_BASE_URL}/torrents/info/${torrentId}`, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`
            }
        });

        const infoData = await infoResponse.json();
        console.log("Torrent information received:", infoData);

        // If no files found, retry after delay
        if (!infoData.files || infoData.files.length === 0) {
            console.log("No files found in torrent, waiting and retrying...");
            await new Promise(resolve => setTimeout(resolve, 2000));
            return selectFiles(torrentId);
        }

        // Select all files using "all" instead of specific file IDs
        const selectResponse = await fetchWithErrorHandling(`${API_BASE_URL}/torrents/selectFiles/${torrentId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `files=all`
        });

        return;
    } catch (error) {
        console.error('Error selecting files:', error);
        throw error;
    }
}

/**
 * Delete a torrent from Real-Debrid
 * @param torrentId ID of the torrent to delete
 * @returns Promise that resolves when torrent is deleted
 */
export async function deleteTorrent(torrentId: string): Promise<void> {
    try {
        await fetchWithErrorHandling(`${API_BASE_URL}/torrents/delete/${torrentId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${API_KEY}`
            }
        });

        return;
    } catch (error) {
        console.error(`Error deleting torrent ${torrentId}:`, error);
        throw error;
    }
}

/**
 * Unrestrict a link to get direct download URL
 * @param link Link to unrestrict
 * @returns Promise with unrestricted link data
 */
export async function unrestrictLink(link: string): Promise<UnrestrictedLink> {
    try {
        console.log(`Unrestricting link: ${link} via API: ${API_BASE_URL}/unrestrict/link`);

        const response = await fetchWithErrorHandling(`${API_BASE_URL}/unrestrict/link`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: `link=${encodeURIComponent(link)}`
        });

        const data = await response.json();
        console.log('API response:', data);

        return data;
    } catch (error) {
        console.error('Error unrestricting link:', error);
        throw error;
    }
}

/**
 * Get CSS class based on torrent status
 * @param status Torrent status
 * @returns CSS class name
 */
export function getStatusClass(status: string): string {
    switch (status) {
        case 'downloaded':
        case 'ready':
            return 'ready';
        case 'downloading':
        case 'queued':
        case 'waiting_files_selection':
            return 'processing';
        case 'error':
        case 'dead':
        case 'magnet_error':
            return 'error';
        default:
            return '';
    }
}

/**
 * Format torrent status for display
 * @param status Torrent status
 * @param progress Optional progress percentage
 * @returns Formatted status string
 */
export function formatStatus(status: string, progress: number | null = null): string {
    switch (status) {
        case 'waiting_files_selection':
            return 'Oczekiwanie na wybór plików';
        case 'queued':
            return 'W kolejce';
        case 'downloading':
            // If progress is available, show percentage (rounded to nearest integer)
            if (progress !== null) {
                return `Pobieranie (${Math.floor(progress)}%)`;
            }
            return 'Pobieranie';
        case 'downloaded':
            return 'Gotowe do pobrania';
        case 'error':
            return 'Błąd';
        case 'dead':
            return 'Nieaktywny';
        case 'magnet_error':
            return 'Błąd magnet';
        case 'ready':
            return 'Gotowy';
        default:
            return status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, ' ');
    }
}

/**
 * Extract filename from URL
 * @param url URL to extract filename from
 * @returns Filename or null if not found
 */
export function extractFilename(url: string): string | null {
    try {
        const parsedUrl = new URL(url);
        const pathname = parsedUrl.pathname;
        const segments = pathname.split('/');
        const lastSegment = segments[segments.length - 1];

        // If last segment is empty or doesn't contain a dot, return null
        if (!lastSegment || !lastSegment.includes('.')) {
            return null;
        }

        return decodeURIComponent(lastSegment);
    } catch (e) {
        return null;
    }
}

/**
 * Determine torrent category based on filename and file extensions
 * @param torrent Torrent object
 * @returns Category: 'movies', 'tvshows', or 'other'
 */
export function determineTorrentCategory(torrent: Torrent): TorrentCategory {
    const filename = torrent.filename.toLowerCase();
    const fileExtensions = torrent.files?.map(file => {
        const path = file.path.toLowerCase();
        const extension = path.split('.').pop() || '';
        return extension;
    }) || [];

    // Patterns for movies
    const moviePatterns = [
        /\b(?:1080p|720p|2160p|4k|uhd|bluray|bdrip|web-?dl|webrip|dvdrip|hdrip)\b/i,
        /\b(?:x264|x265|h264|h265|hevc|xvid|divx)\b/i,
        /\b(?:movie|film)\b/i,
        /\.(mkv|mp4|avi|mov)$/i
    ];

    // Patterns for TV shows
    const tvShowPatterns = [
        /\b(?:s\d{1,2}e\d{1,2}|season\s?\d{1,2}|episode\s?\d{1,2})\b/i,
        /\b(?:complete\s+series|tv\s+series|tv\s+show|tv\s+pack)\b/i,
        /\b(?:\d{1,2}x\d{1,2})\b/i  // Format 1x01, 02x05, etc.
    ];

    // Check if filename matches movie patterns
    const isMovie = moviePatterns.some(pattern => pattern.test(filename)) ||
                   fileExtensions.some(ext => ['mkv', 'mp4', 'avi', 'mov'].includes(ext));

    // Check if filename matches TV show patterns
    const isTVShow = tvShowPatterns.some(pattern => pattern.test(filename));

    // Determine category
    if (isTVShow) {
        return 'tvshows';
    } else if (isMovie) {
        return 'movies';
    } else {
        return 'other';
    }
}
