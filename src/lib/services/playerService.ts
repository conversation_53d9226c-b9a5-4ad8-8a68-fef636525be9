/**
 * Service for handling video playback
 */

/**
 * Play a video in a new window or tab
 * @param url URL of the video to play
 * @param title Title to display in the player window
 */
export function playVideo(url: string, title: string): void {
    // Open the video URL in a new window/tab
    window.open(url, '_blank');
}

/**
 * Create an embedded player for the given URL
 * @param url URL of the video to embed
 * @param container Element to append the player to
 */
export function createEmbeddedPlayer(url: string, container: HTMLElement): void {
    // Clear the container
    container.innerHTML = '';

    // Create an iframe for the embedded player
    const iframe = document.createElement('iframe');
    iframe.src = url;
    iframe.width = '100%';
    iframe.height = '100%';
    iframe.allowFullscreen = true;
    iframe.style.border = 'none';

    // Append the iframe to the container
    container.appendChild(iframe);
}

/**
 * Check if a URL is supported for embedded playback
 * @param url URL to check
 * @returns Whether the URL is supported for embedding
 */
export function isEmbeddable(url: string): boolean {
    // Check if the URL is from a supported provider
    const supportedDomains = [
        'cda.pl',
        'dailymotion.com',
        'vimeo.com',
        'youtube.com',
        'youtu.be',
        'player.vimeo.com',
        'drive.google.com',
        'mega.nz'
    ];

    try {
        const urlObj = new URL(url);
        return supportedDomains.some(domain => urlObj.hostname.includes(domain));
    } catch (error) {
        console.error('Error parsing URL:', error);
        return false;
    }
}

/**
 * Save watch history to localStorage
 * @param slug Anime slug
 * @param episode Episode number
 * @param title Anime title
 */
export function saveWatchHistory(slug: string, episode: number, title: string): void {
    try {
        // Get existing history or initialize empty array
        const historyJson = localStorage.getItem('animce_history') || '[]';
        const history = JSON.parse(historyJson);

        // Create history entry
        const entry = {
            slug,
            episode,
            title,
            timestamp: Date.now()
        };

        // Check if this anime is already in history
        const existingIndex = history.findIndex((item: any) => item.slug === slug);

        if (existingIndex !== -1) {
            // Update existing entry
            history[existingIndex] = entry;
        } else {
            // Add new entry
            history.unshift(entry);

            // Limit history to 50 entries
            if (history.length > 50) {
                history.pop();
            }
        }

        // Save updated history
        localStorage.setItem('animce_history', JSON.stringify(history));
    } catch (error) {
        console.error('Error saving watch history:', error);
    }
}

/**
 * Get watch history from localStorage
 * @returns Array of watch history entries
 */
export function getWatchHistory(): any[] {
    try {
        const historyJson = localStorage.getItem('animce_history') || '[]';
        return JSON.parse(historyJson);
    } catch (error) {
        console.error('Error getting watch history:', error);
        return [];
    }
}

/**
 * Clear watch history
 */
export function clearWatchHistory(): void {
    localStorage.removeItem('animce_history');
}

/**
 * Add anime to watchlist
 * @param slug Anime slug
 * @param title Anime title
 * @param cover Anime cover image URL
 */
export function addToWatchlist(slug: string, title: string, cover?: string): void {
    try {
        // Get existing watchlist or initialize empty array
        const watchlistJson = localStorage.getItem('animce_watchlist') || '[]';
        const watchlist = JSON.parse(watchlistJson);

        // Check if anime is already in watchlist
        const existingIndex = watchlist.findIndex((item: any) => item.slug === slug);

        if (existingIndex === -1) {
            // Add new entry
            watchlist.push({
                slug,
                title,
                cover,
                addedAt: Date.now()
            });

            // Save updated watchlist
            localStorage.setItem('animce_watchlist', JSON.stringify(watchlist));
        }
    } catch (error) {
        console.error('Error adding to watchlist:', error);
    }
}

/**
 * Remove anime from watchlist
 * @param slug Anime slug
 */
export function removeFromWatchlist(slug: string): void {
    try {
        // Get existing watchlist
        const watchlistJson = localStorage.getItem('animce_watchlist') || '[]';
        const watchlist = JSON.parse(watchlistJson);

        // Filter out the anime to remove
        const updatedWatchlist = watchlist.filter((item: any) => item.slug !== slug);

        // Save updated watchlist
        localStorage.setItem('animce_watchlist', JSON.stringify(updatedWatchlist));
    } catch (error) {
        console.error('Error removing from watchlist:', error);
    }
}

/**
 * Check if anime is in watchlist
 * @param slug Anime slug
 * @returns Whether the anime is in the watchlist
 */
export function isInWatchlist(slug: string): boolean {
    try {
        const watchlistJson = localStorage.getItem('animce_watchlist') || '[]';
        const watchlist = JSON.parse(watchlistJson);
        return watchlist.some((item: any) => item.slug === slug);
    } catch (error) {
        console.error('Error checking watchlist:', error);
        return false;
    }
}

/**
 * Get watchlist from localStorage
 * @returns Array of watchlist entries
 */
export function getWatchlist(): any[] {
    try {
        const watchlistJson = localStorage.getItem('animce_watchlist') || '[]';
        return JSON.parse(watchlistJson);
    } catch (error) {
        console.error('Error getting watchlist:', error);
        return [];
    }
}
