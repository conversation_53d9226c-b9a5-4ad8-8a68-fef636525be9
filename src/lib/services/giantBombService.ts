/**
 * Service for GiantBomb API communication
 * Handles game information retrieval
 */

import { GIANTBOMB_CONFIG } from '$lib/config/apiConfig';
import { fetchWithAuth } from '$lib/utils/apiUtils';

// Use proxy URL from config
const { PROXY_URL } = GIANTBOMB_CONFIG;

// Data types
export interface Game {
    id: number;
    guid: string;
    name: string;
    deck: string;
    description: string;
    image: {
        icon_url: string;
        medium_url: string;
        screen_url: string;
        screen_large_url: string;
        small_url: string;
        super_url: string;
        thumb_url: string;
        tiny_url: string;
        original_url: string;
    };
    original_release_date: string;
    platforms: Array<{
        id: number;
        name: string;
    }>;
    site_detail_url: string;
}

export interface GameSearchResult {
    id: number;
    guid: string;
    name: string;
    deck: string;
    image: {
        icon_url: string;
        medium_url: string;
        screen_url: string;
        screen_large_url: string;
        small_url: string;
        super_url: string;
        thumb_url: string;
        tiny_url: string;
        original_url: string;
    };
    original_release_date: string;
    platforms: Array<{
        id: number;
        name: string;
    }>;
    site_detail_url: string;
}

/**
 * Search for games by name
 * @param query Search query
 * @returns Promise with array of game search results
 */
export async function searchGames(query: string): Promise<GameSearchResult[]> {
    try {
        console.log(`[GiantBomb] Searching for games with query: "${query}"`);

        // Create URL to our local proxy
        const endpoint = 'search';
        const url = `${PROXY_URL}?endpoint=${encodeURIComponent(endpoint)}&query=${encodeURIComponent(query)}&resources=game&limit=10`;
        console.log(`[GiantBomb] Calling proxy URL: ${url}`);

        const response = await fetchWithAuth(url);

        console.log(`[GiantBomb] Response status: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[GiantBomb] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[GiantBomb] API Response:', data);

        // Check if response is positive
        if (data.error !== 'OK' || !data.results || data.results.length === 0) {
            console.log(`[GiantBomb] No games found for: "${query}"`);
            return [];
        }

        return data.results;
    } catch (error) {
        console.error('[GiantBomb] Error searching games:', error);
        return [];
    }
}

/**
 * Get game details by ID
 * @param gameId Game ID
 * @returns Promise with game details or null if not found
 */
export async function getGameById(gameId: number): Promise<Game | null> {
    try {
        console.log(`[GiantBomb] Getting game details for ID: ${gameId}`);

        // Create URL to our local proxy
        const endpoint = `game/${gameId}`;
        const url = `${PROXY_URL}?endpoint=${encodeURIComponent(endpoint)}`;
        console.log(`[GiantBomb] Calling proxy URL: ${url}`);

        const response = await fetchWithAuth(url);

        console.log(`[GiantBomb] Response status: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[GiantBomb] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[GiantBomb] API Response:', data);

        // Check if response is positive
        if (data.error !== 'OK' || !data.results) {
            console.log(`[GiantBomb] No game found for ID: ${gameId}`);
            return null;
        }

        return data.results;
    } catch (error) {
        console.error('[GiantBomb] Error getting game details:', error);
        return null;
    }
}

/**
 * Get recent games (last 30 days)
 * @param limit Number of games to return (default: 50)
 * @returns Promise with array of recent PC games
 */
export async function getRecentGames(limit: number = 50): Promise<GameSearchResult[]> {
    try {
        console.log(`[GiantBomb] Getting recent PC games (limit: ${limit})`);

        // Calculate date 30 days ago (zwiększamy zakres, aby mieć więcej wyników)
        const today = new Date();
        const pastDate = new Date();
        pastDate.setDate(today.getDate() - 30);

        const todayStr = today.toISOString().split('T')[0];
        const pastDateStr = pastDate.toISOString().split('T')[0];

        // Create URL to our local proxy
        const endpoint = 'games';
        const url = `${PROXY_URL}?endpoint=${encodeURIComponent(endpoint)}&sort=original_release_date:desc&limit=${limit}&filter=original_release_date:${pastDateStr}|${todayStr},platforms:94`;
        console.log(`[GiantBomb] Calling proxy URL: ${url}`);

        const response = await fetchWithAuth(url);

        console.log(`[GiantBomb] Response status: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[GiantBomb] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[GiantBomb] API Response:', data);

        // Check if response is positive
        if (data.error !== 'OK' || !data.results || data.results.length === 0) {
            console.log('[GiantBomb] No recent PC games found');
            return [];
        }

        return data.results;
    } catch (error) {
        console.error('[GiantBomb] Error getting recent PC games:', error);
        return [];
    }
}

/**
 * Get upcoming games (next 60 days)
 * @param limit Number of games to return (default: 50)
 * @returns Promise with array of upcoming PC games
 */
export async function getUpcomingGames(limit: number = 50): Promise<GameSearchResult[]> {
    try {
        console.log(`[GiantBomb] Getting upcoming PC games (limit: ${limit})`);

        // Calculate dates for next 60 days (zwiększamy zakres, aby mieć więcej wyników)
        const today = new Date();
        const futureDate = new Date();
        futureDate.setDate(today.getDate() + 60);

        const todayStr = today.toISOString().split('T')[0];
        const futureDateStr = futureDate.toISOString().split('T')[0];

        // Create URL to our local proxy
        const endpoint = 'games';
        const url = `${PROXY_URL}?endpoint=${encodeURIComponent(endpoint)}&sort=original_release_date:asc&limit=${limit}&filter=original_release_date:${todayStr}|${futureDateStr},platforms:94`;
        console.log(`[GiantBomb] Calling proxy URL: ${url}`);

        const response = await fetchWithAuth(url);

        console.log(`[GiantBomb] Response status: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[GiantBomb] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[GiantBomb] API Response:', data);

        // Check if response is positive
        if (data.error !== 'OK' || !data.results || data.results.length === 0) {
            console.log('[GiantBomb] No upcoming PC games found');
            return [];
        }

        return data.results;
    } catch (error) {
        console.error('[GiantBomb] Error getting upcoming PC games:', error);
        return [];
    }
}
