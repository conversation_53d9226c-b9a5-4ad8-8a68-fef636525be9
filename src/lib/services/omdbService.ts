/**
 * Service for OMDB API communication
 * Handles movie and TV show information retrieval
 */

import { cleanMediaTitle } from '$lib/utils/apiUtils';
import { OMDB_CONFIG } from '$lib/config/apiConfig';

// Use proxy URL from config
const { PROXY_URL } = OMDB_CONFIG;

// Data types
export interface MovieInfo {
    Title: string;
    Year: string;
    Rated: string;
    Released: string;
    Runtime: string;
    Genre: string;
    Director: string;
    Writer: string;
    Actors: string;
    Plot: string;
    Language: string;
    Country: string;
    Awards: string;
    Poster: string;
    Ratings: Array<{
        Source: string;
        Value: string;
    }>;
    Metascore: string;
    imdbRating: string;
    imdbVotes: string;
    imdbID: string;
    Type: string;
    DVD: string;
    BoxOffice: string;
    Production: string;
    Website: string;
    Response: string;
}

/**
 * Get movie or TV show information by title
 * @param title Movie or TV show title
 * @returns Promise with movie information or null if not found
 */
export async function getMovieInfo(title: string): Promise<MovieInfo | null> {
    try {
        console.log(`[OMDb] Looking for information for: "${title}"`);

        // Clean title from unnecessary information
        const cleanTitle = cleanMediaTitle(title);
        console.log(`[OMDb] Cleaned title: "${cleanTitle}"`);

        // If title is empty after cleaning, return null
        if (!cleanTitle) {
            console.log('[OMDb] Empty title after cleaning, returning null');
            return null;
        }

        // Create URL to our local proxy
        const url = `${PROXY_URL}?title=${encodeURIComponent(cleanTitle)}`;
        console.log(`[OMDb] Calling proxy URL: ${url}`);

        const response = await fetch(url);

        console.log(`[OMDb] Response status: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[OMDb] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[OMDb] API Response:', data);

        // Check if response is positive
        if (data.Response === 'False') {
            console.log(`[OMDb] No information found for: "${cleanTitle}"`);
            return null;
        }

        return data;
    } catch (error) {
        console.error('[OMDb] Error fetching information:', error);
        return null;
    }
}

/**
 * Get movie or TV show poster URL by title
 * @param title Movie or TV show title
 * @returns Promise with poster URL or null if not found
 */
export async function getMoviePosterByTitle(title: string): Promise<string | null> {
    try {
        const movieInfo = await getMovieInfo(title);

        if (!movieInfo || !movieInfo.Poster || movieInfo.Poster === 'N/A') {
            console.log(`[OMDb] No poster found for: "${title}"`);
            return null;
        }

        console.log(`[OMDb] Found poster: ${movieInfo.Poster}`);
        return movieInfo.Poster;
    } catch (error) {
        console.error('[OMDb] Error fetching poster:', error);
        return null;
    }
}


