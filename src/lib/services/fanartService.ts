/**
 * Service for fanart.tv API communication
 * Handles fetching movie artwork like logos, backgrounds, etc.
 */

import { fetchWithAuth } from '$lib/utils/apiUtils';
import { FANART_CONFIG } from '$lib/config/apiConfig';

// Cache for artwork data to avoid multiple requests
const artworkCache = new Map<string, FanartMovieArtwork | null>();

// Types for fanart.tv API responses
export interface FanartMovieArtwork {
    name: string;
    tmdb_id: string;
    imdb_id: string;
    hdmovielogo?: FanartImage[];
    movielogo?: FanartImage[];
    hdmovieclearart?: FanartImage[];
    movieart?: FanartImage[];
    moviebackground?: FanartImage[];
    movieposter?: FanartImage[];
    moviedisc?: FanartImage[];
    moviebanner?: FanartImage[];
    moviethumb?: FanartImage[];
}

export interface FanartImage {
    id: string;
    url: string;
    lang: string;
    likes: string;
}

/**
 * Get movie artwork from fanart.tv via our proxy endpoint
 * @param tmdbId TMDB ID of the movie
 * @returns Promise with movie artwork or null if not found
 */
export async function getMovieArtwork(tmdbId: string): Promise<FanartMovieArtwork | null> {
    // Check cache first
    if (artworkCache.has(tmdbId)) {
        console.log(`[Fanart.tv] Using cached artwork for TMDB ID: ${tmdbId}`);
        return artworkCache.get(tmdbId) || null;
    }

    try {
        console.log(`[Fanart.tv] Getting artwork for TMDB ID: ${tmdbId}`);

        // Use our proxy endpoint to avoid CORS issues
        const proxyUrl = `/api/fanart?tmdbId=${tmdbId}`;
        console.log(`[Fanart.tv] Using proxy URL: ${proxyUrl}`);

        // For testing purposes, use regular fetch instead of fetchWithAuth
        // In production, you should use fetchWithAuth to ensure proper authentication
        console.log(`[Fanart.tv] Making request to proxy endpoint`);
        const response = await fetchWithAuth(proxyUrl);

        if (!response.ok) {
            if (response.status === 404) {
                console.log(`[Fanart.tv] No artwork found for TMDB ID: ${tmdbId}`);
                // Cache the null result to avoid repeated requests
                artworkCache.set(tmdbId, null);
                return null;
            }

            const errorText = await response.text();
            console.error(`[Fanart.tv] API Error: ${response.status}`, errorText);
            // Don't cache errors
            return null;
        }

        const data = await response.json();
        console.log(`[Fanart.tv] Successfully retrieved artwork for TMDB ID: ${tmdbId}`);

        // Cache the result
        artworkCache.set(tmdbId, data);

        return data;
    } catch (error) {
        console.error('[Fanart.tv] Error fetching artwork:', error);
        return null;
    }
}

/**
 * Get movie logo from fanart.tv
 * @param tmdbId TMDB ID of the movie
 * @param preferredLang Preferred language for the logo (default: 'en')
 * @returns Promise with logo URL or null if not found
 */
export async function getMovieLogo(tmdbId: string, preferredLang: string = 'en'): Promise<string | null> {
    try {
        console.log(`[Fanart.tv] Getting movie logo for TMDB ID: ${tmdbId}, preferred language: ${preferredLang}`);
        const artwork = await getMovieArtwork(tmdbId);

        if (!artwork) {
            console.log(`[Fanart.tv] No artwork found for TMDB ID: ${tmdbId}`);
            return null;
        }

        // Try to get HD movie logo first
        if (artwork.hdmovielogo && artwork.hdmovielogo.length > 0) {
            console.log(`[Fanart.tv] Found ${artwork.hdmovielogo.length} HD movie logos`);

            // Try to find logo in preferred language
            const preferredLogo = artwork.hdmovielogo.find(logo => logo.lang === preferredLang);

            if (preferredLogo) {
                console.log(`[Fanart.tv] Found HD movie logo in preferred language (${preferredLang}): ${preferredLogo.url}`);
                return preferredLogo.url;
            }

            // If no logo in preferred language, return the first one
            console.log(`[Fanart.tv] No HD movie logo in preferred language, using first available: ${artwork.hdmovielogo[0].url}`);
            return artwork.hdmovielogo[0].url;
        }

        // Fall back to regular movie logo
        if (artwork.movielogo && artwork.movielogo.length > 0) {
            console.log(`[Fanart.tv] Found ${artwork.movielogo.length} regular movie logos`);

            // Try to find logo in preferred language
            const preferredLogo = artwork.movielogo.find(logo => logo.lang === preferredLang);

            if (preferredLogo) {
                console.log(`[Fanart.tv] Found regular movie logo in preferred language (${preferredLang}): ${preferredLogo.url}`);
                return preferredLogo.url;
            }

            // If no logo in preferred language, return the first one
            console.log(`[Fanart.tv] No regular movie logo in preferred language, using first available: ${artwork.movielogo[0].url}`);
            return artwork.movielogo[0].url;
        }

        console.log(`[Fanart.tv] No movie logo found for TMDB ID: ${tmdbId}`);
        return null;
    } catch (error) {
        console.error('[Fanart.tv] Error fetching movie logo:', error);
        return null;
    }
}

/**
 * Get movie background from fanart.tv
 * @param tmdbId TMDB ID of the movie
 * @returns Promise with background URL or null if not found
 */
export async function getMovieBackground(tmdbId: string): Promise<string | null> {
    try {
        console.log(`[Fanart.tv] Getting movie background for TMDB ID: ${tmdbId}`);
        const artwork = await getMovieArtwork(tmdbId);

        if (!artwork) {
            console.log(`[Fanart.tv] No artwork found for TMDB ID: ${tmdbId}`);
            return null;
        }

        if (!artwork.moviebackground || artwork.moviebackground.length === 0) {
            console.log(`[Fanart.tv] No movie background found for TMDB ID: ${tmdbId}`);
            return null;
        }

        console.log(`[Fanart.tv] Found movie background: ${artwork.moviebackground[0].url}`);
        return artwork.moviebackground[0].url;
    } catch (error) {
        console.error('[Fanart.tv] Error fetching movie background:', error);
        return null;
    }
}

/**
 * Get movie clearart from fanart.tv
 * @param tmdbId TMDB ID of the movie
 * @param preferredLang Preferred language for the clearart (default: 'en')
 * @returns Promise with clearart URL or null if not found
 */
export async function getMovieClearart(tmdbId: string, preferredLang: string = 'en'): Promise<string | null> {
    try {
        console.log(`[Fanart.tv] Getting movie clearart for TMDB ID: ${tmdbId}, preferred language: ${preferredLang}`);
        const artwork = await getMovieArtwork(tmdbId);

        if (!artwork) {
            console.log(`[Fanart.tv] No artwork found for TMDB ID: ${tmdbId}`);
            return null;
        }

        // Try to get HD movie clearart first
        if (artwork.hdmovieclearart && artwork.hdmovieclearart.length > 0) {
            console.log(`[Fanart.tv] Found ${artwork.hdmovieclearart.length} HD movie cleararts`);

            // Try to find clearart in preferred language
            const preferredClearart = artwork.hdmovieclearart.find(art => art.lang === preferredLang);

            if (preferredClearart) {
                console.log(`[Fanart.tv] Found HD movie clearart in preferred language (${preferredLang}): ${preferredClearart.url}`);
                return preferredClearart.url;
            }

            // If no clearart in preferred language, return the first one
            console.log(`[Fanart.tv] No HD movie clearart in preferred language, using first available: ${artwork.hdmovieclearart[0].url}`);
            return artwork.hdmovieclearart[0].url;
        }

        // Fall back to regular movie clearart
        if (artwork.movieart && artwork.movieart.length > 0) {
            console.log(`[Fanart.tv] Found ${artwork.movieart.length} regular movie cleararts`);

            // Try to find clearart in preferred language
            const preferredClearart = artwork.movieart.find(art => art.lang === preferredLang);

            if (preferredClearart) {
                console.log(`[Fanart.tv] Found regular movie clearart in preferred language (${preferredLang}): ${preferredClearart.url}`);
                return preferredClearart.url;
            }

            // If no clearart in preferred language, return the first one
            console.log(`[Fanart.tv] No regular movie clearart in preferred language, using first available: ${artwork.movieart[0].url}`);
            return artwork.movieart[0].url;
        }

        console.log(`[Fanart.tv] No movie clearart found for TMDB ID: ${tmdbId}`);
        return null;
    } catch (error) {
        console.error('[Fanart.tv] Error fetching movie clearart:', error);
        return null;
    }
}
