/**
 * Service for IGDB API communication
 * Handles game information retrieval
 */

import { IGDB_CONFIG } from '$lib/config/apiConfig';
import { fetchWithAuth } from '$lib/utils/apiUtils';

// Use proxy URL from config
const { PROXY_URL } = IGDB_CONFIG;

// Data types
export interface IGDBGame {
    id: number;
    name: string;
    summary?: string;
    storyline?: string;
    first_release_date?: number; // Unix timestamp
    cover?: {
        id: number;
        image_id: string;
    };
    screenshots?: Array<{
        id: number;
        image_id: string;
    }>;
    platforms?: Array<{
        id: number;
        name: string;
    }>;
    url?: string;
    slug?: string;
    rating?: number;
    rating_count?: number;
    total_rating?: number;
    total_rating_count?: number;
    hypes?: number;
    follows?: number;
    category?: number; // 0 = main game, 1 = DLC, 2 = expansion, etc.
    status?: number; // 0 = released, 2 = alpha, 3 = beta, etc.
    genres?: Array<{
        id: number;
        name: string;
    }>;
    themes?: Array<{
        id: number;
        name: string;
    }>;
    game_modes?: Array<{
        id: number;
        name: string;
    }>;
    player_perspectives?: Array<{
        id: number;
        name: string;
    }>;
    involved_companies?: Array<{
        id: number;
        company: {
            id: number;
            name: string;
        };
        developer: boolean;
        publisher: boolean;
    }>;
    websites?: Array<{
        id: number;
        url: string;
        category: number; // 1 = official, 2 = wikia, etc.
    }>;
    videos?: Array<{
        id: number;
        name: string;
        video_id: string; // YouTube video ID
    }>;
}

/**
 * Get image URL from IGDB image ID
 * @param imageId IGDB image ID
 * @param size Image size (thumb, cover_small, cover_big, screenshot_med, screenshot_big, screenshot_huge, logo_med, logo_big)
 * @returns Image URL
 */
export function getImageUrl(imageId: string, size: string = 'cover_big'): string {
    return `https://images.igdb.com/igdb/image/upload/t_${size}/${imageId}.jpg`;
}

/**
 * Format Unix timestamp to date string
 * @param timestamp Unix timestamp
 * @returns Formatted date string
 */
export function formatTimestamp(timestamp?: number): string {
    if (!timestamp) return '';
    return new Date(timestamp * 1000).toISOString();
}

/**
 * Get recent PC games (released in the last 90 days)
 * @param limit Number of games to return (default: 50)
 * @returns Promise with array of recent PC games
 */
export async function getRecentGames(limit: number = 50): Promise<IGDBGame[]> {
    try {
        console.log(`[IGDB] Getting recent PC games (limit: ${limit})`);

        // Calculate date 90 days ago (Unix timestamp)
        const today = Math.floor(Date.now() / 1000);
        const ninetyDaysAgo = today - (90 * 24 * 60 * 60);

        // Create query for IGDB API
        // Platform 6 is PC
        // Filter by:
        // - Category 0 is main game (not DLC, expansion, etc.)
        // - Rating >= 75 (only well-rated games)
        // - Sort by release date (newest first)
        const query = `fields name,summary,first_release_date,cover.image_id,platforms.name,slug,url,rating,rating_count,total_rating,total_rating_count,category,hypes,follows;
where platforms = (6) & first_release_date >= ${ninetyDaysAgo} & first_release_date <= ${today} & category = 0 & rating >= 75;
sort first_release_date desc;
limit ${limit};`;

        // Make request to our proxy
        const response = await fetch(PROXY_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                endpoint: 'games',
                query
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[IGDB] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[IGDB] Recent games response:', data);

        // If no results with rating filter, try without it
        if (data.length === 0) {
            console.log('[IGDB] No results with rating filter, trying without it');

            // Create query without rating filter
            const fallbackQuery = `fields name,summary,first_release_date,cover.image_id,platforms.name,slug,url,rating,rating_count,total_rating,total_rating_count,category,hypes,follows;
where platforms = (6) & first_release_date >= ${ninetyDaysAgo} & first_release_date <= ${today} & category = 0;
sort first_release_date desc;
limit ${limit};`;

            // Make fallback request
            const fallbackResponse = await fetch(PROXY_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    endpoint: 'games',
                    query: fallbackQuery
                })
            });

            if (fallbackResponse.ok) {
                const fallbackData = await fallbackResponse.json();
                console.log('[IGDB] Fallback recent games response:', fallbackData);
                return fallbackData;
            }
        }

        return data;
    } catch (error) {
        console.error('[IGDB] Error getting recent PC games:', error);
        return [];
    }
}

/**
 * Get upcoming PC games (releasing in the next 180 days)
 * @param limit Number of games to return (default: 50)
 * @returns Promise with array of upcoming PC games
 */
export async function getUpcomingGames(limit: number = 50): Promise<IGDBGame[]> {
    try {
        console.log(`[IGDB] Getting upcoming PC games (limit: ${limit})`);

        // Calculate dates for next 180 days (Unix timestamp)
        const today = Math.floor(Date.now() / 1000);
        const sixMonthsLater = today + (180 * 24 * 60 * 60);

        // Create query for IGDB API
        // Platform 6 is PC
        // Filter by:
        // - Category 0 is main game (not DLC, expansion, etc.)
        // - Hypes >= 10 (only anticipated games)
        // - Sort by release date (soonest first)
        const query = `fields name,summary,first_release_date,cover.image_id,platforms.name,slug,url,hypes,follows,category,status,total_rating,rating,rating_count;
where platforms = (6) & first_release_date >= ${today} & first_release_date <= ${sixMonthsLater} & category = 0 & hypes >= 10;
sort first_release_date asc;
limit ${limit};`;

        // Make request to our proxy
        const response = await fetch(PROXY_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                endpoint: 'games',
                query
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[IGDB] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[IGDB] Upcoming games response:', data);

        // If no results with hypes filter, try without it
        if (data.length === 0) {
            console.log('[IGDB] No results with hypes filter, trying without it');

            // Create query without hypes filter
            const fallbackQuery = `fields name,summary,first_release_date,cover.image_id,platforms.name,slug,url,hypes,follows,category,status,total_rating,rating,rating_count;
where platforms = (6) & first_release_date >= ${today} & first_release_date <= ${sixMonthsLater} & category = 0;
sort first_release_date asc;
limit ${limit};`;

            // Make fallback request
            const fallbackResponse = await fetch(PROXY_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    endpoint: 'games',
                    query: fallbackQuery
                })
            });

            if (fallbackResponse.ok) {
                const fallbackData = await fallbackResponse.json();
                console.log('[IGDB] Fallback upcoming games response:', fallbackData);
                return fallbackData;
            }
        }

        return data;
    } catch (error) {
        console.error('[IGDB] Error getting upcoming PC games:', error);
        return [];
    }
}

/**
 * Get game details by ID
 * @param gameId Game ID
 * @returns Promise with game details or null if not found
 */
export async function getGameById(gameId: number): Promise<IGDBGame | null> {
    try {
        console.log(`[IGDB] Getting game details for ID: ${gameId}`);

        // Create query for IGDB API
        const query = `fields name,summary,storyline,first_release_date,cover.image_id,screenshots.image_id,platforms.name,slug,url,rating,rating_count,total_rating,total_rating_count,genres.name,themes.name,game_modes.name,player_perspectives.name,involved_companies.company.name,involved_companies.developer,involved_companies.publisher,hypes,follows,category,status,websites.*,videos.*;
where id = ${gameId};`;

        // Make request to our proxy
        const response = await fetch(PROXY_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                endpoint: 'games',
                query
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[IGDB] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[IGDB] Game details response:', data);

        if (!data || data.length === 0) {
            console.log(`[IGDB] No game found with ID: ${gameId}`);
            return null;
        }

        return data[0];
    } catch (error) {
        console.error('[IGDB] Error getting game details:', error);
        return null;
    }
}

/**
 * Search for games by name
 * @param query Search query
 * @returns Promise with array of game search results
 */
export async function searchGames(query: string): Promise<IGDBGame[]> {
    try {
        console.log(`[IGDB] Searching for games with query: "${query}"`);

        // Create query for IGDB API
        const igdbQuery = `fields name,summary,first_release_date,cover.image_id,platforms.name,slug,url;
search "${query}";
where platforms = (6);
limit 10;`;

        // Make request to our proxy
        const response = await fetch(PROXY_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                endpoint: 'games',
                query: igdbQuery
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[IGDB] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[IGDB] Search response:', data);

        return data;
    } catch (error) {
        console.error('[IGDB] Error searching games:', error);
        return [];
    }
}
