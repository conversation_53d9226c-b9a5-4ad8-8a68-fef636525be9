/**
 * Service for SteamGridDB API communication
 * Handles game cover image retrieval
 */

import { cleanGameTitle } from '$lib/utils/apiUtils';
import { STEAMGRIDDB_CONFIG } from '$lib/config/apiConfig';
import { fetchWithAuth } from '$lib/utils/apiUtils';

// Use proxy URL from config
const { PROXY_URL } = STEAMGRIDDB_CONFIG;

// Data types
export interface GameInfo {
    id: number;
    name: string;
    types: string[];
    verified: boolean;
}

export interface GameCover {
    id: number;
    score: number;
    style: string;
    url: string;
    thumb: string;
    author: {
        name: string;
        steam64: string;
        avatar: string;
    };
}

/**
 * Search for games in SteamGridDB
 * @param query Search query
 * @returns Promise with array of game information
 */
export async function searchGames(query: string): Promise<GameInfo[]> {
    try {
        // Clean query using utility function
        const cleanQuery = cleanGameTitle(query);
        console.log(`[SteamGridDB] Clean search query: "${cleanQuery}"`);

        // If query is empty after cleaning, return empty array
        if (!cleanQuery) {
            console.log('[SteamGridDB] Empty query after cleaning, returning []');
            return [];
        }

        // Create URL to our local proxy
        const endpoint = `search/autocomplete/${encodeURIComponent(cleanQuery)}`;
        const url = `${PROXY_URL}?endpoint=${encodeURIComponent(endpoint)}`;
        console.log(`[SteamGridDB] Calling proxy URL: ${url}`);

        const response = await fetchWithAuth(url);

        console.log(`[SteamGridDB] Response status: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[SteamGridDB] API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[SteamGridDB] API Response:', data);

        // If no results found, try searching just the first word
        if ((!data.data || data.data.length === 0) && cleanQuery.includes(' ')) {
            const firstWord = cleanQuery.split(' ')[0];
            console.log(`[SteamGridDB] Trying to search just the first word: "${firstWord}"`);

            return searchGames(firstWord);
        }

        console.log(`[SteamGridDB] Found ${data.data?.length || 0} games`);
        return data.data || [];
    } catch (error) {
        console.error('[SteamGridDB] Error searching games:', error);
        return [];
    }
}

/**
 * Get game covers from SteamGridDB
 * @param gameId Game ID
 * @returns Promise with array of game covers
 */
export async function getGameCovers(gameId: number): Promise<GameCover[]> {
    try {
        // Create URL to our local proxy
        const endpoint = `grids/game/${gameId}`;
        const url = `${PROXY_URL}?endpoint=${encodeURIComponent(endpoint)}`;
        console.log(`[SteamGridDB] Getting covers via proxy: ${url}`);

        const response = await fetchWithAuth(url);

        console.log(`[SteamGridDB] Covers response status: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[SteamGridDB] Covers API Error: ${response.status}`, errorText);
            throw new Error(`Error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[SteamGridDB] Covers API Response:', data);

        return data.data || [];
    } catch (error) {
        console.error('[SteamGridDB] Error fetching covers:', error);
        return [];
    }
}

/**
 * Get game cover URL by game name
 * @param gameName Game name
 * @returns Promise with cover URL or null if not found
 */
export async function getGameCoverByName(gameName: string): Promise<string | null> {
    try {
        console.log(`[SteamGridDB] Looking for cover for: "${gameName}"`);

        // Search for game by name
        const games = await searchGames(gameName);

        console.log(`[SteamGridDB] Found ${games.length} games:`,
            games.map(g => `${g.name} (id: ${g.id})`).join(', '));

        if (games.length === 0) {
            console.log(`[SteamGridDB] No games found for: "${gameName}"`);
            return null;
        }

        // Get covers for the first found game
        const covers = await getGameCovers(games[0].id);

        console.log(`[SteamGridDB] Found ${covers.length} covers for ${games[0].name}`);

        if (covers.length === 0) {
            console.log(`[SteamGridDB] No covers for ${games[0].name}`);
            return null;
        }

        // Return URL of the first cover
        console.log(`[SteamGridDB] Returning cover: ${covers[0].url}`);
        return covers[0].url;
    } catch (error) {
        console.error('[SteamGridDB] Error fetching cover by name:', error);
        return null;
    }
}
