/**
 * Utility functions for compressing and decompressing data
 * to reduce localStorage usage
 */

/**
 * Compresses data by removing unnecessary fields and optimizing structure
 * @param data The data to compress
 * @returns Compressed data
 */
export function compressData(data: any): any {
    if (!data) return null;
    
    // For game sources with downloads array
    if (data.downloads && Array.isArray(data.downloads)) {
        // Create a more compact representation of the downloads
        return {
            name: data.name,
            downloads: data.downloads.map((download: any) => ({
                t: download.title, // title
                u: download.uris && download.uris.length > 0 ? [download.uris[0]] : [], // only first URI
                d: download.uploadDate, // date
                s: download.fileSize // size
            }))
        };
    }
    
    // For other types of data, return as is
    return data;
}

/**
 * Decompresses data back to its original format
 * @param data The compressed data
 * @returns Decompressed data
 */
export function decompressData(data: any): any {
    if (!data) return null;
    
    // For compressed game sources
    if (data.downloads && Array.isArray(data.downloads) && 
        data.downloads.length > 0 && data.downloads[0].t) {
        
        // Expand the compact representation back to original format
        return {
            name: data.name,
            downloads: data.downloads.map((download: any) => ({
                title: download.t,
                uris: download.u || [],
                uploadDate: download.d,
                fileSize: download.s
            }))
        };
    }
    
    // For other types of data, return as is
    return data;
}
