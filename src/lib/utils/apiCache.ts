/**
 * API request caching and rate limiting utilities
 */

import { browser } from '$app/environment';
import { getStorageItem, setStorageItem } from './storageUtils';

// Cache item interface
interface CacheItem<T> {
    data: T;
    timestamp: number;
    expiry: number;
}

// Cache options interface
interface CacheOptions {
    /** Cache expiration time in milliseconds */
    expiry?: number;
    /** Force refresh even if cache is valid */
    forceRefresh?: boolean;
    /** Cache key prefix */
    prefix?: string;
}

// Default cache options
const DEFAULT_CACHE_OPTIONS: CacheOptions = {
    expiry: 60 * 60 * 1000, // 1 hour
    forceRefresh: false,
    prefix: 'api_cache_'
};

// Rate limiting map to track API calls
const rateLimitMap = new Map<string, number[]>();

/**
 * Cached fetch function with rate limiting
 * @param url URL to fetch
 * @param options Fetch options
 * @param cacheOptions Cache options
 * @returns Promise with response data
 */
export async function cachedFetch<T>(
    url: string,
    options: RequestInit = {},
    cacheOptions: CacheOptions = {}
): Promise<T> {
    // Merge default options
    const opts = { ...DEFAULT_CACHE_OPTIONS, ...cacheOptions };
    
    // Generate cache key
    const cacheKey = `${opts.prefix}${url}`;
    
    // Check if we should use cache
    if (browser && !opts.forceRefresh) {
        const cachedData = getStorageItem<CacheItem<T>>(cacheKey);
        
        if (cachedData) {
            const now = Date.now();
            
            // If cache is still valid, return cached data
            if (now - cachedData.timestamp < cachedData.expiry) {
                console.log(`[API Cache] Using cached data for ${url}`);
                return cachedData.data;
            }
        }
    }
    
    // Apply rate limiting
    await applyRateLimit(url);
    
    // Fetch fresh data
    try {
        const response = await fetch(url, options);
        
        if (!response.ok) {
            throw new Error(`API error: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Cache the response if in browser
        if (browser) {
            const cacheItem: CacheItem<T> = {
                data,
                timestamp: Date.now(),
                expiry: opts.expiry || DEFAULT_CACHE_OPTIONS.expiry!
            };
            
            setStorageItem(cacheKey, cacheItem);
        }
        
        return data;
    } catch (error) {
        console.error(`[API Cache] Error fetching ${url}:`, error);
        throw error;
    }
}

/**
 * Apply rate limiting to API requests
 * @param url URL being requested
 * @param maxRequests Maximum requests per time window
 * @param timeWindow Time window in milliseconds
 */
async function applyRateLimit(
    url: string,
    maxRequests: number = 5,
    timeWindow: number = 1000
): Promise<void> {
    // Extract domain for rate limiting
    const domain = new URL(url).hostname;
    
    // Get current timestamp
    const now = Date.now();
    
    // Get or initialize timestamps for this domain
    let timestamps = rateLimitMap.get(domain) || [];
    
    // Filter out timestamps outside the time window
    timestamps = timestamps.filter(timestamp => now - timestamp < timeWindow);
    
    // Check if we've exceeded the rate limit
    if (timestamps.length >= maxRequests) {
        // Calculate time to wait
        const oldestTimestamp = timestamps[0];
        const timeToWait = timeWindow - (now - oldestTimestamp);
        
        console.log(`[API Cache] Rate limit reached for ${domain}, waiting ${timeToWait}ms`);
        
        // Wait until we can make another request
        await new Promise(resolve => setTimeout(resolve, timeToWait));
        
        // Recursively try again after waiting
        return applyRateLimit(url, maxRequests, timeWindow);
    }
    
    // Add current timestamp to the list
    timestamps.push(now);
    
    // Update the map
    rateLimitMap.set(domain, timestamps);
}

/**
 * Clear API cache for a specific URL or all cache
 * @param url Optional URL to clear cache for
 * @param prefix Cache key prefix
 */
export function clearApiCache(url?: string, prefix: string = DEFAULT_CACHE_OPTIONS.prefix!): void {
    if (!browser) return;
    
    if (url) {
        // Clear cache for specific URL
        const cacheKey = `${prefix}${url}`;
        localStorage.removeItem(cacheKey);
    } else {
        // Clear all API cache
        const keys = Object.keys(localStorage);
        
        for (const key of keys) {
            if (key.startsWith(prefix)) {
                localStorage.removeItem(key);
            }
        }
    }
}
