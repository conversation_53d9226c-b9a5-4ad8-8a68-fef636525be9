/**
 * Keyboard navigation utilities
 */

import { browser } from '$app/environment';

// Keyboard shortcut interface
export interface KeyboardShortcut {
    key: string;
    ctrlKey?: boolean;
    altKey?: boolean;
    shiftKey?: boolean;
    metaKey?: boolean;
    action: () => void;
    description: string;
    global?: boolean;
}

// Keyboard navigation manager
class KeyboardNavigationManager {
    private shortcuts: KeyboardShortcut[] = [];
    private enabled: boolean = true;
    private initialized: boolean = false;
    
    /**
     * Initialize keyboard navigation
     */
    public init(): void {
        if (!browser || this.initialized) return;
        
        // Add event listener
        window.addEventListener('keydown', this.handleKeyDown.bind(this));
        
        this.initialized = true;
    }
    
    /**
     * Register a keyboard shortcut
     * @param shortcut Keyboard shortcut to register
     */
    public register(shortcut: KeyboardShortcut): void {
        // Initialize if not already
        if (!this.initialized) {
            this.init();
        }
        
        // Add shortcut to list
        this.shortcuts.push(shortcut);
    }
    
    /**
     * Register multiple keyboard shortcuts
     * @param shortcuts Keyboard shortcuts to register
     */
    public registerMany(shortcuts: KeyboardShortcut[]): void {
        shortcuts.forEach(shortcut => this.register(shortcut));
    }
    
    /**
     * Unregister a keyboard shortcut
     * @param key Key to unregister
     * @param modifiers Modifier keys
     */
    public unregister(
        key: string,
        modifiers: { ctrlKey?: boolean; altKey?: boolean; shiftKey?: boolean; metaKey?: boolean } = {}
    ): void {
        this.shortcuts = this.shortcuts.filter(shortcut => {
            return !(
                shortcut.key === key &&
                shortcut.ctrlKey === (modifiers.ctrlKey || false) &&
                shortcut.altKey === (modifiers.altKey || false) &&
                shortcut.shiftKey === (modifiers.shiftKey || false) &&
                shortcut.metaKey === (modifiers.metaKey || false)
            );
        });
    }
    
    /**
     * Enable keyboard navigation
     */
    public enable(): void {
        this.enabled = true;
    }
    
    /**
     * Disable keyboard navigation
     */
    public disable(): void {
        this.enabled = false;
    }
    
    /**
     * Get all registered shortcuts
     * @returns Array of keyboard shortcuts
     */
    public getShortcuts(): KeyboardShortcut[] {
        return [...this.shortcuts];
    }
    
    /**
     * Handle keydown event
     * @param event Keyboard event
     */
    private handleKeyDown(event: KeyboardEvent): void {
        if (!this.enabled) return;
        
        // Skip if in input, textarea, or contenteditable
        const target = event.target as HTMLElement;
        if (
            target.tagName === 'INPUT' ||
            target.tagName === 'TEXTAREA' ||
            target.isContentEditable
        ) {
            // Only process global shortcuts in input fields
            const shortcut = this.findMatchingShortcut(event, true);
            if (shortcut) {
                event.preventDefault();
                shortcut.action();
            }
            return;
        }
        
        // Find matching shortcut
        const shortcut = this.findMatchingShortcut(event);
        
        if (shortcut) {
            event.preventDefault();
            shortcut.action();
        }
    }
    
    /**
     * Find matching shortcut for keyboard event
     * @param event Keyboard event
     * @param globalOnly Whether to only match global shortcuts
     * @returns Matching shortcut or undefined
     */
    private findMatchingShortcut(event: KeyboardEvent, globalOnly: boolean = false): KeyboardShortcut | undefined {
        return this.shortcuts.find(shortcut => {
            if (globalOnly && !shortcut.global) {
                return false;
            }
            
            return (
                shortcut.key.toLowerCase() === event.key.toLowerCase() &&
                (shortcut.ctrlKey || false) === event.ctrlKey &&
                (shortcut.altKey || false) === event.altKey &&
                (shortcut.shiftKey || false) === event.shiftKey &&
                (shortcut.metaKey || false) === event.metaKey
            );
        });
    }
}

// Export singleton instance
export const keyboardNavigation = new KeyboardNavigationManager();

/**
 * Register common keyboard shortcuts
 */
export function registerCommonShortcuts(): void {
    keyboardNavigation.registerMany([
        {
            key: '/',
            action: () => {
                // Focus search input
                const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i], input[placeholder*="szukaj" i]') as HTMLInputElement;
                if (searchInput) {
                    searchInput.focus();
                }
            },
            description: 'Focus search',
            global: true
        },
        {
            key: 'Escape',
            action: () => {
                // Close modal or dialog
                const closeButton = document.querySelector('[aria-label="Close"], [aria-label="Zamknij"], .modal-close-btn') as HTMLElement;
                if (closeButton) {
                    closeButton.click();
                }
            },
            description: 'Close dialog',
            global: true
        },
        {
            key: '?',
            shiftKey: true,
            action: () => {
                // Show keyboard shortcuts help
                console.log('Keyboard shortcuts:', keyboardNavigation.getShortcuts());
                // TODO: Implement a help dialog
            },
            description: 'Show keyboard shortcuts',
            global: true
        }
    ]);
}
