/**
 * Utility functions for API interactions
 */

import { authStore } from '$lib/stores/authStore';
import { get } from 'svelte/store';

/**
 * Fetch with authentication
 * Automatically adds the JWT token to the request headers
 *
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @returns Promise with the fetch response
 */
export async function fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
    // Get the current auth token
    const auth = get(authStore);

    // Create headers with authentication if token exists
    const headers = new Headers(options.headers || {});

    if (auth.token) {
        headers.set('Authorization', `Bearer ${auth.token}`);
    }

    // Merge with existing options
    const fetchOptions: RequestInit = {
        ...options,
        headers
    };

    // Execute the fetch request with error handling
    return fetchWithErrorHandling(url, fetchOptions);
}

/**
 * Simplified GET request with authentication
 *
 * @param url - The URL to fetch
 * @returns Promise with the fetch response
 */
export async function getWithAuth(url: string): Promise<Response> {
    return fetchWithAuth(url, { method: 'GET' });
}

/**
 * Simplified POST request with authentication
 *
 * @param url - The URL to fetch
 * @param data - The data to send (will be converted to JSON)
 * @returns Promise with the fetch response
 */
export async function postWithAuth(url: string, data: any): Promise<Response> {
    return fetchWithAuth(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    });
}

/**
 * Fetch with error handling
 * @param url URL to fetch
 * @param options Fetch options
 * @returns Promise with response
 */
export async function fetchWithErrorHandling(url: string, options: RequestInit = {}): Promise<Response> {
    try {
        console.log(`[API Utils] Fetching URL: ${url}`);
        console.log(`[API Utils] Request method: ${options.method || 'GET'}`);

        // Add timeout to fetch requests
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        const fetchOptions = {
            ...options,
            signal: controller.signal
        };

        const response = await fetch(url, fetchOptions);
        clearTimeout(timeoutId);

        console.log(`[API Utils] Response status: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[API Utils] API Error (${response.status}):`, errorText);

            // Create a more detailed error
            const error = new Error(`Error ${response.status}: ${errorText || response.statusText}`);
            (error as any).status = response.status;
            (error as any).url = url;
            (error as any).method = options.method || 'GET';

            throw error;
        }

        return response;
    } catch (error) {
        if (error.name === 'AbortError') {
            console.error(`[API Utils] Request timeout for URL: ${url}`);
            throw new Error(`Request timeout after 30 seconds: ${url}`);
        }

        console.error(`[API Utils] Fetch error for URL ${url}:`, error);
        throw error;
    }
}

/**
 * Clean movie/TV show title by removing quality, codecs, etc.
 * @param title Original title
 * @returns Cleaned title
 */
export function cleanMediaTitle(title: string): string {
    if (!title) return '';

    let cleanTitle = title.trim();

    // Detect and extract TV show name (remove S01E01 pattern)
    const tvShowPattern = /(.+?)(?:\s+S\d{1,2}E\d{1,2}|\s+\d{1,2}x\d{1,2})/i;
    const tvShowMatch = cleanTitle.match(tvShowPattern);

    if (tvShowMatch) {
        return tvShowMatch[1].trim();
    }

    // Remove patterns for movies
    const patterns = [
        // Quality and resolution
        /\b\d{3,4}p\b/gi, // 720p, 1080p, 2160p
        /\b(4k|uhd|hdr|hdr10|dolby\s*vision|dv|8k)\b/gi,

        // Sources
        /\b(bluray|bdrip|blu-ray|remux|dvdrip|dvd-rip|web-?dl|webrip|web|hdtv|pdtv|tvrip|hdcam|hdts|camrip|dvdscr)\b/gi,

        // Video codecs
        /\b(x264|x265|h264|h265|hevc|xvid|divx|av1|vp9)\b/gi,

        // Audio codecs
        /\b(aac|ac3|dts|dtshd|truehd|dd5\.1|dd\+|atmos|flac|opus)\b/gi,

        // Streaming platforms
        /\b(amzn|amazon|hulu|netflix|nf|disney\+|hbo)\b/gi,

        // Scene and release groups (common patterns)
        /\b(RARBG|YIFY|YTS|EZTV|ETTV|SPARKS|AMIABLE|FGT)\b/gi,

        // Other technical info
        /\b(10bit|8bit|hi10p)\b/gi,
        /\b(dual|multi|subs?)\b/gi,

        // File extensions
        /\.(mkv|mp4|avi|mov)$/i,

        // Year pattern at the end
        /\s+\(\d{4}\)$/,

        // Brackets and parentheses with content
        /\[.*?\]/g,
        /\(.*?\)/g,

        // Common separators
        /\s*-\s*(?!.*-)/
    ];

    // Apply all patterns
    patterns.forEach(pattern => {
        cleanTitle = cleanTitle.replace(pattern, ' ');
    });

    // Clean up multiple spaces and trim
    cleanTitle = cleanTitle.replace(/\s+/g, ' ').trim();

    return cleanTitle;
}

/**
 * Clean game title by removing unnecessary information
 * @param title Original title
 * @returns Cleaned title
 */
export function cleanGameTitle(title: string): string {
    if (!title) return '';

    let cleanTitle = title.trim();

    // Remove patterns specific to games
    const patterns = [
        // Common additions to game titles
        /\b(REPACK|PROPER|GOG|CODEX|PLAZA|SKIDROW|HOODLUM|DODI|FitGirl)\b/gi,
        /\b(Remastered|Deluxe|Edition|Complete|Collection|GOTY)\b/gi,

        // Technical info
        /\b(v\d+\.\d+|\d+\.\d+)\b/gi, // Version numbers

        // Brackets and parentheses with content
        /\[.*?\]/g,
        /\(.*?\)/g,

        // Common separators
        /\s*-\s*(?!.*-)/
    ];

    // Apply all patterns
    patterns.forEach(pattern => {
        cleanTitle = cleanTitle.replace(pattern, ' ');
    });

    // Clean up multiple spaces and trim
    cleanTitle = cleanTitle.replace(/\s+/g, ' ').trim();

    return cleanTitle;
}

/**
 * Compress data to save space in localStorage
 * @param data Data to compress
 * @returns Compressed data as string
 */
export function compressData(data: any): string {
    try {
        // Simple compression by removing unnecessary whitespace
        return JSON.stringify(data);
    } catch (error) {
        console.error('Error compressing data:', error);
        return '';
    }
}

/**
 * Decompress data from localStorage
 * @param compressedData Compressed data string
 * @returns Decompressed data
 */
export function decompressData(compressedData: string): any {
    try {
        // Parse the JSON string
        return JSON.parse(compressedData);
    } catch (error) {
        console.error('Error decompressing data:', error);
        return null;
    }
}
