/**
 * Environment variable utilities
 * Provides functions for accessing environment variables with fallbacks
 */

import { browser } from '$app/environment';

/**
 * Get an environment variable with a fallback value
 * @param key Environment variable key
 * @param fallback Fallback value if environment variable is not set
 * @returns Environment variable value or fallback
 */
export function getEnv(key: string, fallback: string = ''): string {
    // In browser, environment variables are not available
    if (browser) {
        return fallback;
    }
    
    // In Node.js, get from process.env
    return process.env[key] || fallback;
}

/**
 * Get a boolean environment variable
 * @param key Environment variable key
 * @param fallback Fallback value if environment variable is not set
 * @returns Boolean value of environment variable
 */
export function getBoolEnv(key: string, fallback: boolean = false): boolean {
    const value = getEnv(key, fallback ? 'true' : 'false');
    return value.toLowerCase() === 'true';
}

/**
 * Get a number environment variable
 * @param key Environment variable key
 * @param fallback Fallback value if environment variable is not set
 * @returns Number value of environment variable
 */
export function getNumEnv(key: string, fallback: number = 0): number {
    const value = getEnv(key, fallback.toString());
    const parsed = parseFloat(value);
    return isNaN(parsed) ? fallback : parsed;
}

/**
 * Get an array environment variable (comma-separated)
 * @param key Environment variable key
 * @param fallback Fallback value if environment variable is not set
 * @returns Array value of environment variable
 */
export function getArrayEnv(key: string, fallback: string[] = []): string[] {
    const value = getEnv(key, fallback.join(','));
    return value ? value.split(',').map(item => item.trim()) : fallback;
}

/**
 * Get a JSON environment variable
 * @param key Environment variable key
 * @param fallback Fallback value if environment variable is not set
 * @returns Parsed JSON value of environment variable
 */
export function getJsonEnv<T>(key: string, fallback: T): T {
    const value = getEnv(key, '');
    
    if (!value) {
        return fallback;
    }
    
    try {
        return JSON.parse(value) as T;
    } catch (error) {
        console.error(`Error parsing JSON environment variable ${key}:`, error);
        return fallback;
    }
}
