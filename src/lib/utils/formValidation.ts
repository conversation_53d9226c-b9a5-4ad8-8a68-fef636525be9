/**
 * Form validation utilities
 */

// Validation rule type
export type ValidationRule = (value: any) => string | null;

// Validation result interface
export interface ValidationResult {
    isValid: boolean;
    errors: Record<string, string | null>;
}

/**
 * Create a required field validation rule
 * @param message Custom error message
 * @returns Validation rule function
 */
export function required(message: string = 'This field is required'): ValidationRule {
    return (value: any) => {
        if (value === null || value === undefined || value === '') {
            return message;
        }
        
        if (Array.isArray(value) && value.length === 0) {
            return message;
        }
        
        return null;
    };
}

/**
 * Create a minimum length validation rule
 * @param length Minimum length
 * @param message Custom error message
 * @returns Validation rule function
 */
export function minLength(length: number, message?: string): ValidationRule {
    return (value: any) => {
        if (value === null || value === undefined || value === '') {
            return null; // Let required handle empty values
        }
        
        const stringValue = String(value);
        
        if (stringValue.length < length) {
            return message || `Must be at least ${length} characters`;
        }
        
        return null;
    };
}

/**
 * Create a maximum length validation rule
 * @param length Maximum length
 * @param message Custom error message
 * @returns Validation rule function
 */
export function maxLength(length: number, message?: string): ValidationRule {
    return (value: any) => {
        if (value === null || value === undefined || value === '') {
            return null; // Let required handle empty values
        }
        
        const stringValue = String(value);
        
        if (stringValue.length > length) {
            return message || `Must be at most ${length} characters`;
        }
        
        return null;
    };
}

/**
 * Create a pattern validation rule
 * @param pattern Regular expression pattern
 * @param message Custom error message
 * @returns Validation rule function
 */
export function pattern(pattern: RegExp, message: string = 'Invalid format'): ValidationRule {
    return (value: any) => {
        if (value === null || value === undefined || value === '') {
            return null; // Let required handle empty values
        }
        
        const stringValue = String(value);
        
        if (!pattern.test(stringValue)) {
            return message;
        }
        
        return null;
    };
}

/**
 * Create an email validation rule
 * @param message Custom error message
 * @returns Validation rule function
 */
export function email(message: string = 'Invalid email address'): ValidationRule {
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return pattern(emailPattern, message);
}

/**
 * Create a URL validation rule
 * @param message Custom error message
 * @returns Validation rule function
 */
export function url(message: string = 'Invalid URL'): ValidationRule {
    return (value: any) => {
        if (value === null || value === undefined || value === '') {
            return null; // Let required handle empty values
        }
        
        try {
            new URL(String(value));
            return null;
        } catch (error) {
            return message;
        }
    };
}

/**
 * Create a numeric validation rule
 * @param message Custom error message
 * @returns Validation rule function
 */
export function numeric(message: string = 'Must be a number'): ValidationRule {
    return (value: any) => {
        if (value === null || value === undefined || value === '') {
            return null; // Let required handle empty values
        }
        
        if (isNaN(Number(value))) {
            return message;
        }
        
        return null;
    };
}

/**
 * Create a minimum value validation rule
 * @param min Minimum value
 * @param message Custom error message
 * @returns Validation rule function
 */
export function min(min: number, message?: string): ValidationRule {
    return (value: any) => {
        if (value === null || value === undefined || value === '') {
            return null; // Let required handle empty values
        }
        
        const numValue = Number(value);
        
        if (isNaN(numValue) || numValue < min) {
            return message || `Must be at least ${min}`;
        }
        
        return null;
    };
}

/**
 * Create a maximum value validation rule
 * @param max Maximum value
 * @param message Custom error message
 * @returns Validation rule function
 */
export function max(max: number, message?: string): ValidationRule {
    return (value: any) => {
        if (value === null || value === undefined || value === '') {
            return null; // Let required handle empty values
        }
        
        const numValue = Number(value);
        
        if (isNaN(numValue) || numValue > max) {
            return message || `Must be at most ${max}`;
        }
        
        return null;
    };
}

/**
 * Create a custom validation rule
 * @param validator Custom validator function
 * @param message Custom error message
 * @returns Validation rule function
 */
export function custom(validator: (value: any) => boolean, message: string): ValidationRule {
    return (value: any) => {
        if (!validator(value)) {
            return message;
        }
        
        return null;
    };
}

/**
 * Validate a form field with multiple rules
 * @param value Field value
 * @param rules Validation rules
 * @returns First error message or null if valid
 */
export function validateField(value: any, rules: ValidationRule[]): string | null {
    for (const rule of rules) {
        const error = rule(value);
        if (error) {
            return error;
        }
    }
    
    return null;
}

/**
 * Validate a form with multiple fields and rules
 * @param values Form values
 * @param validationRules Validation rules for each field
 * @returns Validation result
 */
export function validateForm(
    values: Record<string, any>,
    validationRules: Record<string, ValidationRule[]>
): ValidationResult {
    const errors: Record<string, string | null> = {};
    let isValid = true;
    
    // Validate each field
    for (const field in validationRules) {
        const rules = validationRules[field];
        const value = values[field];
        
        const error = validateField(value, rules);
        errors[field] = error;
        
        if (error) {
            isValid = false;
        }
    }
    
    return { isValid, errors };
}
