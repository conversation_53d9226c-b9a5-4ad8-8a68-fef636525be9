/**
 * Utility functions for localStorage handling with compression and error handling
 */

import { compressData, decompressData } from './apiUtils';
import { STORAGE_KEYS } from '../config/apiConfig';

/**
 * Check if localStorage is available
 * @returns Boolean indicating if localStorage is available
 */
export function isLocalStorageAvailable(): boolean {
    try {
        const testKey = '__test__';
        localStorage.setItem(testKey, testKey);
        localStorage.removeItem(testKey);
        return true;
    } catch (e) {
        return false;
    }
}

/**
 * Get item from localStorage with error handling and decompression
 * @param key Storage key
 * @param compressed Whether the data is compressed
 * @returns Parsed data or null if not found or error
 */
export function getStorageItem<T>(key: string, compressed: boolean = false): T | null {
    try {
        const item = localStorage.getItem(key);
        if (!item) return null;
        
        // If data is compressed, decompress it
        if (compressed) {
            return decompressData(item);
        }
        
        return JSON.parse(item);
    } catch (error) {
        console.error(`Error getting item from localStorage (${key}):`, error);
        return null;
    }
}

/**
 * Set item in localStorage with error handling and compression
 * @param key Storage key
 * @param value Data to store
 * @param compress Whether to compress the data
 * @returns Boolean indicating success
 */
export function setStorageItem<T>(key: string, value: T, compress: boolean = false): boolean {
    try {
        let dataToStore: string;
        
        // If compression is enabled, compress the data
        if (compress) {
            dataToStore = compressData(value);
        } else {
            dataToStore = JSON.stringify(value);
        }
        
        localStorage.setItem(key, dataToStore);
        return true;
    } catch (error) {
        console.error(`Error setting item in localStorage (${key}):`, error);
        
        // If quota exceeded error, try to clear some space
        if (error instanceof DOMException && (error.name === 'QuotaExceededError' || 
            error.toString().includes('quota') || 
            error.toString().includes('storage'))) {
            
            // Try to clear old cache data
            try {
                // Clear game search data if it exists
                if (key !== STORAGE_KEYS.GAME_SEARCH_DATA) {
                    localStorage.removeItem(STORAGE_KEYS.GAME_SEARCH_DATA);
                }
                
                // Try again with compression if not already compressed
                if (!compress) {
                    return setStorageItem(key, value, true);
                }
            } catch (clearError) {
                console.error('Error clearing localStorage space:', clearError);
            }
        }
        
        return false;
    }
}

/**
 * Remove item from localStorage with error handling
 * @param key Storage key
 * @returns Boolean indicating success
 */
export function removeStorageItem(key: string): boolean {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error(`Error removing item from localStorage (${key}):`, error);
        return false;
    }
}

/**
 * Clear all items from localStorage with error handling
 * @returns Boolean indicating success
 */
export function clearStorage(): boolean {
    try {
        localStorage.clear();
        return true;
    } catch (error) {
        console.error('Error clearing localStorage:', error);
        return false;
    }
}

/**
 * Get the size of localStorage in bytes
 * @returns Size in bytes or -1 if error
 */
export function getStorageSize(): number {
    try {
        let totalSize = 0;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
                const value = localStorage.getItem(key) || '';
                totalSize += key.length + value.length;
            }
        }
        return totalSize * 2; // UTF-16 characters are 2 bytes each
    } catch (error) {
        console.error('Error calculating localStorage size:', error);
        return -1;
    }
}

/**
 * Get the remaining space in localStorage in bytes (approximate)
 * @returns Remaining space in bytes or -1 if error
 */
export function getRemainingStorageSpace(): number {
    try {
        // Typical localStorage limit is 5MB
        const defaultLimit = 5 * 1024 * 1024;
        const currentSize = getStorageSize();
        
        if (currentSize === -1) return -1;
        
        return Math.max(0, defaultLimit - currentSize);
    } catch (error) {
        console.error('Error calculating remaining localStorage space:', error);
        return -1;
    }
}
