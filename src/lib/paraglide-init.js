/**
 * Initialize Paraglide with the correct locale from localStorage
 * This file should be imported early in the application lifecycle
 */

import { setLocale } from '$lib/paraglide/runtime';

// Initialize Paraglide with the correct locale
function initParaglide() {
    if (typeof window !== 'undefined') {
        // Get the locale from localStorage or use the default (pl)
        const storedLocale = localStorage.getItem('paraglide-locale');
        if (storedLocale && (storedLocale === 'en' || storedLocale === 'pl')) {
            setLocale(storedLocale);
            
            // Update the HTML lang attribute
            document.documentElement.lang = storedLocale;
        } else {
            // Default to Polish
            setLocale('pl');
            localStorage.setItem('paraglide-locale', 'pl');
        }
    }
}

// Run initialization
initParaglide();
