@import 'tailwindcss';
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@import url('https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');

@layer base {
  :root {
    --bg-color: #0d0f11;
    --sidebar-bg: #16191d;
    --card-bg: #1e2227;
    --item-bg: #23272d; /* Minimalnie jaśniejszy od card-bg */
    --primary: #f4accb;
    --secondary: #d1d5db;
    --accent: #3dd68c;
    --error: #f87171;
    --text: #f1f5f9;
    --text-secondary: #9ca3af;
    --border: #2c313a;
  }

  * {
    box-sizing: border-box;
    font-family: 'Ubuntu', sans-serif;
    outline: none !important;
    box-shadow: none !important;
  }

  body {
    @apply min-h-screen bg-[var(--bg-color)] text-[var(--text)];
  }

  h2 {
    @apply text-2xl font-bold;
  }

  h3 {
    @apply text-sm font-normal mt-0 pt-0;
  }
}

@layer components {
  .app-container {
    @apply flex min-h-screen relative;
  }

  .app-container.page-dragging::after {
    content: '';
    @apply absolute inset-0 border-4 border-dashed border-[var(--primary)] rounded-lg pointer-events-none z-10;
  }

  .page-dropzone-message {
    @apply fixed inset-0 flex items-center justify-center bg-[var(--bg-color)]/80 z-20 transition-opacity duration-300;
  }

  .page-dropzone-message p {
    @apply text-[var(--primary)] text-3xl font-bold bg-[var(--card-bg)] px-8 py-6 rounded-lg border-2 border-dashed border-[var(--primary)] shadow-lg;
  }

  /* Desktop sidebar */
  .sidebar {
    @apply h-full w-[250px] bg-[#15181c] rounded-lg border border-[var(--border)] py-5 shrink-0 rounded-tr-none rounded-br-none;
  }

  .sidebar-header {
    @apply px-5 pb-5 mb-5 border-b border-[var(--border)];
  }

  .sidebar-logo {
    @apply flex flex-col items-center gap-2 text-[var(--primary)] font-bold text-[1.2rem];
  }

  .language-switcher-container {
    @apply flex justify-center mt-4;
  }

  .sidebar-language-switcher {
    @apply mx-auto;
  }

  .nav-menu {
    @apply list-none;
  }

  .nav-item {
    @apply mb-1;
  }

  .nav-link {
    @apply flex items-center gap-2 px-5 py-2 text-[var(--text-secondary)] no-underline transition-all border-l-4 border-transparent w-full text-left text-sm cursor-pointer;
  }

  .nav-link:hover {
    @apply bg-white/5 text-[var(--text)];
  }

  .nav-link.active {
    @apply text-[var(--primary)] bg-pink-400/10 border-l-[var(--primary)];
  }

  .nav-badge {
    @apply inline-flex items-center justify-center min-w-[20px] h-[20px] text-xs font-bold text-white bg-pink-500 ml-2 rounded-full px-1;
  }

  .nav-divider {
    @apply h-[1px] bg-[var(--border)] my-3 mx-5;
  }

  .nav-category {
    @apply mt-4 px-5 py-1 text-xs font-bold tracking-widest text-[var(--text-secondary)] opacity-70 uppercase;
  }

  .nav-category-beta {
    @apply flex items-center justify-between;
  }

  .beta-badge {
    @apply bg-pink-500 text-white text-[10px] px-1.5 py-0.5 rounded font-bold;
  }

  /* Mobile bottom navigation */
  .mobile-nav {
    @apply hidden fixed bottom-0 left-0 right-0 bg-[var(--card-bg)] border-t border-[var(--border)] z-50 px-4 py-4 shadow-lg overflow-x-auto;
    scrollbar-width: none; /* Firefox */
    -webkit-overflow-scrolling: touch;
  }

  .mobile-nav::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .mobile-nav-menu {
    @apply flex items-center list-none w-full mx-auto;
    justify-content: space-between;
    min-width: max-content;
  }

  .mobile-nav-item {
    @apply flex justify-center w-16 h-16;
  }

  .mobile-nav-link {
    @apply flex items-center justify-center p-4 text-[var(--text-secondary)] no-underline transition-all cursor-pointer border-none outline-none w-full h-full relative;
  }

  .mobile-nav-badge {
    @apply absolute top-1 right-1 inline-flex items-center justify-center min-w-[18px] h-[18px] text-xs font-bold text-white bg-green-500 rounded-full px-1;
  }

  .mobile-nav-link.active {
    @apply text-[var(--primary)];
  }

  .mobile-nav-link.active::after {
    content: '';
    @apply absolute bottom-1 w-1.5 h-1.5 bg-[var(--primary)] rounded-full;
  }

  .mobile-nav-link:hover {
    @apply text-[var(--text)];
  }

  .mobile-nav-divider {
    @apply w-[1px] h-10 bg-[var(--border)] mx-1;
  }

  .main-content {
    @apply flex-grow p-5  min-h-screen pl-0;
  }

  header {
    @apply flex flex-col items-center mb-8 mt-8;
  }

  header h1 {
    @apply text-center w-full text-[var(--primary)] ml-2;
  }

  .logo {
    @apply h-10 mr-4;
  }

  .card {
    @apply bg-[var(--card-bg)] rounded-lg border border-[var(--border)] p-5 mb-5 relative h-full border-l-0 rounded-tl-none rounded-bl-none;
  }

  /* Override card styles for mobile to ensure they're fully rounded */
  @media (max-width: 768px) {
    .card {
      @apply rounded-tl-lg rounded-bl-lg border-l;
    }
  }

  .card.dragging {
    @apply border-[var(--primary)] border-2 border-dashed;
  }

  .dropzone-message {
    @apply absolute inset-0 flex items-center justify-center bg-[var(--card-bg)]/90 z-10 rounded-lg transition-opacity duration-300;
  }

  .dropzone-message p {
    @apply text-[var(--primary)] text-xl font-bold bg-[var(--card-bg)] px-4 py-2 rounded-lg border border-dashed border-[var(--primary)];
  }

  .hidden {
    @apply hidden;
  }

  .tabs {
    @apply flex mb-5;
  }

  .tab {
    @apply px-5 py-2 cursor-pointer border-b-2 border-[var(--card-bg)] transition-all;
  }

  .tab.active {
    @apply border-b-[2px] border-[var(--primary)] text-[var(--primary)];
  }

  .tab:hover:not(.active) {
    @apply border-b-2 border-[var(--text-secondary)];
  }

  .tab-content {
    @apply hidden;
  }

  .tab-content.active {
    @apply block;
  }

  .input-group {
    @apply mb-4;
  }

  .input-group label {
    @apply block mb-1 text-[var(--text-secondary)];
  }

  .magnet-input {
    @apply w-full bg-[var(--item-bg)] border border-[#444] rounded px-4 py-2 text-[var(--text)];
  }

  .file-input-container {
    @apply relative overflow-hidden inline-block;
  }

  .file-input-container input[type=file] {
    @apply absolute left-0 top-0 opacity-0 w-full h-full cursor-pointer;
  }

  .file-input-btn {
    @apply bg-[var(--item-bg)] text-[var(--text)] border border-[#444] rounded px-4 py-2 inline-block;
  }

  .file-name {
    @apply ml-2 text-[var(--text-secondary)];
  }

  .btn {
    @apply border-0 rounded px-4 py-2 cursor-pointer font-semibold transition-all inline-flex items-center justify-center gap-2;
  }

  .btn-primary {
    @apply bg-white text-[#2c313a];
  }

  .btn-secondary {
    @apply bg-[var(--secondary)] text-[#111827];
  }

  .btn-accent {
    @apply bg-[var(--accent)] text-white;
  }

  .btn-error {
    @apply bg-[var(--error)] text-white;
  }

  .btn-outline {
    @apply bg-transparent border border-white text-[var(--text)];
  }

  .btn-outline:hover {
    @apply text-[var(--primary)];
  }

  .btn-disabled {
    @apply bg-[#333] text-[#666] cursor-not-allowed opacity-70 pointer-events-none;
  }

  .resource-list {
    @apply flex flex-col gap-4;
  }

  .resource-item {
    @apply bg-[var(--item-bg)] rounded-md p-4 relative flex flex-row items-center justify-between flex-wrap gap-2;
  }

  .resource-item-info {
    @apply flex-1 min-w-[200px];
  }

  .resource-item h3 {
    @apply mb-2 text-base truncate;
  }

  .resource-status {
    @apply text-sm text-[var(--text-secondary)] mb-2;
  }

  .resource-status.ready {
    @apply text-[var(--secondary)];
  }

  .resource-status.processing {
    @apply text-[var(--primary)];
  }

  .resource-status.error {
    @apply text-[var(--error)];
  }

  /* Game cover styles */
  .game-cover-container {
    @apply relative overflow-hidden rounded-md bg-[#1a1a1a];
  }

  /* Ensure all card-like elements have rounded corners on mobile */
  @media (max-width: 768px) {
    .game-card, .anime-card, .media-item, .game-cover-container, .anime-cover, .media-poster {
      border-radius: 0.5rem !important;
    }
  }

  .resource-progress {
    @apply h-[6px] w-full bg-[#444] rounded mb-4 overflow-hidden;
  }

  .progress-bar {
    @apply h-full bg-[var(--primary)] rounded w-0;
  }

  .action-btns {
    @apply flex gap-2;
  }

  .notification {
    @apply fixed bottom-5 right-5 px-5 py-4 rounded text-white font-bold opacity-0 translate-y-2 transition-all z-[1000];
  }

  .notification.success {
    @apply bg-green-600;
  }

  .notification.error {
    @apply bg-[var(--error)];
  }

  .notification.show {
    @apply opacity-100 translate-y-0;
  }

  .loading {
    @apply inline-block w-3 h-3 border-2 border-solid border-[#251418] border-t-[var(--primary)] rounded-full animate-spin ml-2;
  }

  .empty-state {
    @apply text-center py-10 text-[var(--text-secondary)] w-full;
  }

  .empty-state p {
    @apply mb-5;
  }

  .pagination {
    @apply flex justify-center items-center mt-5 gap-2;
  }

  .pagination-btn {
    @apply bg-[var(--item-bg)] text-[var(--text)] border-0 rounded px-3 py-2 cursor-pointer transition-all;
  }

  .pagination-btn:hover:not(.active):not(:disabled) {
    @apply bg-[#444];
  }

  .pagination-btn.active {
    @apply bg-[var(--primary)] text-black font-bold;
  }

  .pagination-btn:disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  input[type=text]:hover {
    @apply border-pink-300;
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .app-container {
      @apply flex-col pb-20; /* Add padding at the bottom for the mobile nav */
    }

    .sidebar-container {
      @apply hidden;
    }

    .mobile-nav {
      @apply flex;
    }

    .main-content {
      @apply p-3 pt-4; /* Smaller padding on mobile */
      width: 100%;
    }

    /* Adjust card styles for mobile */
    .card {
      @apply p-4 mb-4 rounded-lg;
      border-radius: 0.5rem !important;
    }

    /* Make buttons more touch-friendly */
    .btn {
      @apply py-3;
    }

    /* Adjust form elements for mobile */
    .input-group {
      @apply mb-3;
    }

    .magnet-input {
      @apply py-3;
    }

    /* Adjust notification position for mobile */
    .notification {
      @apply bottom-24;
    }
  }
}