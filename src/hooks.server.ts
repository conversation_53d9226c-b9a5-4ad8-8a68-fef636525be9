import type { Handle } from '@sveltejs/kit';
import { paraglideMiddleware } from '$lib/paraglide/server';

const handleParaglide: Handle = ({ event, resolve }) => {
	try {
		return paraglideMiddleware(event.request, ({ request, locale }) => {
			try {
				event.request = request;

				return resolve(event, {
					transformPageChunk: ({ html }) => {
						try {
							return html.replace('%paraglide.lang%', locale || 'pl');
						} catch (error) {
							console.error('Error in transformPageChunk:', error);
							return html;
						}
					}
				});
			} catch (error) {
				console.error('Error in paraglideMiddleware callback:', error);
				return resolve(event);
			}
		});
	} catch (error) {
		console.error('Error in paraglideMiddleware:', error);
		return resolve(event);
	}
};

export const handle: Handle = handleParaglide;
