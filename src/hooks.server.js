import { isPublicRoute, authenticateRequest } from '$lib/server/auth';
import { json } from '@sveltejs/kit';

/** @type {import('@sveltejs/kit').Handle} */
export async function handle({ event, resolve }) {
	// Check if the request is for an API route
	const isApiRoute = event.url.pathname.startsWith('/api/');

	// Skip authentication for non-API routes and public routes
	if (!isApiRoute || isPublicRoute(event.url.pathname)) {
		return await resolve(event);
	}

	// Authenticate API requests
	const isAuthenticated = authenticateRequest(event.request);

	if (!isAuthenticated) {
		// Return 401 Unauthorized for API routes
		return json({
			success: false,
			message: 'Unauthorized: Authentication required'
		}, { status: 401 });
	}

	// Continue with authenticated request
	return await resolve(event);
}
