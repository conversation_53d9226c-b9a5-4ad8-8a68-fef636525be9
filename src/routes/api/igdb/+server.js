/**
 * IGDB API proxy route
 * Handles requests to the IGDB API to bypass CORS restrictions
 */

import { IGDB_CONFIG } from '$lib/config/apiConfig';
import { json } from '@sveltejs/kit';

// Disable prerendering for this API route
export const prerender = false;

// Get API configuration from centralized config
const { CLIENT_ID, CLIENT_SECRET, BASE_URL, AUTH_URL } = IGDB_CONFIG;

// Cache for the access token
let accessToken = null;
let tokenExpiration = 0;

/**
 * Get a valid access token for IGDB API
 * @returns {Promise<string>} Access token
 */
async function getAccessToken() {
    // Check if we have a valid token
    const now = Date.now();
    if (accessToken && tokenExpiration > now) {
        console.log('[IGDB Proxy] Using cached access token');
        return accessToken;
    }

    // Get a new token
    console.log('[IGDB Proxy] Getting new access token');
    console.log(`[IGDB Proxy] Client ID: ${CLIENT_ID}`);
    console.log(`[IGDB Proxy] Client Secret: ${CLIENT_SECRET.substring(0, 5)}...`);

    try {
        const tokenUrl = `${AUTH_URL}?client_id=${CLIENT_ID}&client_secret=${CLIENT_SECRET}&grant_type=client_credentials`;
        console.log(`[IGDB Proxy] Token URL: ${tokenUrl}`);

        const response = await fetch(tokenUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log(`[IGDB Proxy] Auth response status: ${response.status}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[IGDB Proxy] Auth Error (${response.status}):`, errorText);
            throw new Error(`IGDB Auth error: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('[IGDB Proxy] Auth response data:', data);

        accessToken = data.access_token;
        // Set expiration time (subtract 60 seconds to be safe)
        tokenExpiration = now + (data.expires_in - 60) * 1000;

        console.log(`[IGDB Proxy] Got new access token: ${accessToken.substring(0, 10)}...`);
        console.log(`[IGDB Proxy] Token expires in ${data.expires_in} seconds`);

        return accessToken;
    } catch (error) {
        console.error('[IGDB Proxy] Error getting access token:', error);
        throw error;
    }
}

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        console.log('[IGDB Proxy] Received request');

        // Get request body
        const requestData = await request.json();
        console.log('[IGDB Proxy] Request data:', requestData);

        // Get endpoint and query from request
        const { endpoint, query } = requestData;

        if (!endpoint) {
            console.error('[IGDB Proxy] Missing endpoint parameter');
            return json({
                error: 'Missing endpoint parameter'
            }, { status: 400 });
        }

        // Get access token
        console.log('[IGDB Proxy] Getting access token...');
        const token = await getAccessToken();
        console.log('[IGDB Proxy] Got access token:', token ? token.substring(0, 10) + '...' : 'null');

        // Create URL to IGDB API
        const apiUrl = `${BASE_URL}/${endpoint}`;
        console.log(`[IGDB Proxy] Calling API: ${apiUrl}`);
        console.log(`[IGDB Proxy] Query: ${query}`);
        console.log(`[IGDB Proxy] Headers: Client-ID=${CLIENT_ID}, Authorization=Bearer ${token ? token.substring(0, 10) + '...' : 'null'}`);

        // Make request to IGDB API
        console.log('[IGDB Proxy] Sending request to IGDB API...');
        console.log('[IGDB Proxy] Query (raw):', query);

        // Log query with escaped newlines for debugging
        const debugQuery = query.replace(/\n/g, '\\n');
        console.log('[IGDB Proxy] Query (escaped):', debugQuery);

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Client-ID': CLIENT_ID,
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json',
                'Content-Type': 'text/plain'
            },
            body: query
        });

        console.log(`[IGDB Proxy] Response status: ${response.status}`);

        // Check if response is OK
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[IGDB Proxy] API Error (${response.status}):`, errorText);

            return json({
                error: `IGDB API error: ${response.status}`,
                details: errorText
            }, { status: response.status });
        }

        // Get response data
        const data = await response.json();
        console.log('[IGDB Proxy] Response data (first few items):', data.slice(0, 2));
        console.log(`[IGDB Proxy] Total items returned: ${data.length}`);

        // Return data
        return json(data);
    } catch (error) {
        console.error('[IGDB Proxy] Error:', error);

        return json({
            error: 'IGDB Proxy error',
            message: error.message
        }, { status: 500 });
    }
}

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function OPTIONS() {
    return new Response(null, {
        status: 204,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type',
            'Access-Control-Max-Age': '86400' // 24 hours
        }
    });
}
