// Disable prerendering for this API route
export const prerender = false;

import { json } from '@sveltejs/kit';
import fs from 'fs/promises';
import path from 'path';
import { DEFAULT_DISCORD_CONFIG } from '$lib/config/serverConfig.js';

const CONFIG_PATH = path.join(process.cwd(), '.config', 'discord', 'config.json');

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const data = await request.json();

        // Ensure config directory exists
        try {
            await fs.mkdir(path.join(process.cwd(), '.config', 'discord'), { recursive: true });
        } catch (mkdirError) {
            console.error('Error creating config directory:', mkdirError);
        }

        // Save configuration
        await fs.writeFile(CONFIG_PATH, JSON.stringify(data, null, 2), 'utf-8');

        return json({
            success: true,
            message: 'Discord bot configuration saved successfully'
        });
    } catch (error) {
        console.error('Discord config error:', error);
        return json({
            success: false,
            message: 'An error occurred while saving Discord bot configuration'
        }, { status: 500 });
    }
}

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET() {
    try {
        // Try to read existing configuration
        try {
            const configData = await fs.readFile(CONFIG_PATH, 'utf-8');
            const config = JSON.parse(configData);

            return json({
                success: true,
                config
            });
        } catch (readError) {
            // If file doesn't exist, return default config from environment variables
            return json({
                success: true,
                config: DEFAULT_DISCORD_CONFIG
            });
        }
    } catch (error) {
        console.error('Error reading Discord config:', error);
        return json({
            success: false,
            message: 'An error occurred while reading Discord bot configuration'
        }, { status: 500 });
    }
}
