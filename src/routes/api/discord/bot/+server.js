// Disable prerendering for this API route
export const prerender = false;

import { json } from '@sveltejs/kit';
import { initializeBot, shutdownBot, isBotInitialized, restartBot } from '$lib/server/discord-bot.js';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const { action } = await request.json();

        switch (action) {
            case 'start':
                try {
                    const success = await initializeBot();
                    return json({
                        success,
                        message: success ? 'Bot Discord został uruchomiony' : 'Nie udało się uruchomić bota Discord'
                    });
                } catch (startError) {
                    console.error('Error starting Discord bot:', startError);
                    return json({
                        success: false,
                        message: `Nie udało się uruchomić bota Discord: ${startError.message}`
                    }, { status: 500 });
                }

            case 'stop':
                try {
                    await shutdownBot();
                    return json({
                        success: true,
                        message: 'Bot Discord został zatrzymany'
                    });
                } catch (stopError) {
                    console.error('Error stopping Discord bot:', stopError);
                    return json({
                        success: false,
                        message: `Nie udało się zatrzymać bota Discord: ${stopError.message}`
                    }, { status: 500 });
                }

            case 'restart':
                try {
                    const restartSuccess = await restartBot();
                    return json({
                        success: restartSuccess,
                        message: restartSuccess ? 'Bot Discord został zrestartowany' : 'Nie udało się zrestartować bota Discord'
                    });
                } catch (restartError) {
                    console.error('Error restarting Discord bot:', restartError);
                    return json({
                        success: false,
                        message: `Nie udało się zrestartować bota Discord: ${restartError.message}`
                    }, { status: 500 });
                }

            case 'status':
                return json({
                    success: true,
                    isRunning: isBotInitialized()
                });

            default:
                return json({
                    success: false,
                    message: 'Nieznana akcja'
                }, { status: 400 });
        }
    } catch (error) {
        console.error('Discord bot API error:', error);
        return json({
            success: false,
            message: `Wystąpił błąd podczas wykonywania akcji: ${error.message}`
        }, { status: 500 });
    }
}
