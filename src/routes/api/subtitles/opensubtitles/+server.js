import { json } from '@sveltejs/kit';
import { authenticateRequest } from '$lib/server/auth';
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';

// Folder dla tymczasowych plików napisów
const SUBTITLES_DIR = path.join(process.cwd(), 'static', 'subtitles');

// Upewnij się, że folder istnieje
try {
    if (!fs.existsSync(SUBTITLES_DIR)) {
        fs.mkdirSync(SUBTITLES_DIR, { recursive: true });
    }
} catch (error) {
    console.error('Error creating subtitles directory:', error);
}

// Konfiguracja API OpenSubtitles
const OPENSUBTITLES_API_URL = 'https://api.opensubtitles.com/api/v1';
const OPENSUBTITLES_API_KEY = process.env.OPENSUBTITLES_API_KEY;

/**
 * Konwertuje SRT do VTT
 * @param {string} srtContent Zawartość pliku SRT
 * @returns {string} Zawartość pliku VTT
 */
function convertSrtToVtt(srtContent) {
    // Sprawdź, czy plik jest już w formacie WebVTT
    if (srtContent.trim().startsWith('WEBVTT')) {
        return srtContent;
    }

    // Dodaj nagłówek WebVTT
    let vttContent = 'WEBVTT\n\n';

    // Zamień format czasu z SRT (00:00:00,000) na VTT (00:00:00.000)
    const lines = srtContent.split('\n');
    let inTimestamp = false;

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // Pomiń numery sekwencji
        if (/^\d+$/.test(line)) {
            continue;
        }

        // Konwertuj znaczniki czasu
        if (line.includes('-->')) {
            inTimestamp = true;
            vttContent += line.replace(/,/g, '.') + '\n';
        } else if (line === '') {
            inTimestamp = false;
            vttContent += '\n';
        } else {
            vttContent += line + '\n';
        }
    }

    return vttContent;
}

/**
 * Wyszukuje napisy za pomocą API OpenSubtitles
 * @param {Request} request
 */
export async function GET({ url, request }) {
    // Sprawdź autoryzację
    const isAuthenticated = authenticateRequest(request);
    if (!isAuthenticated) {
        return new Response(JSON.stringify({ error: 'Unauthorized' }), {
            status: 401,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    try {
        // Pobierz parametry wyszukiwania
        const query = url.searchParams.get('query');
        const imdbId = url.searchParams.get('imdbid');
        const language = url.searchParams.get('language') || 'pl';
        const year = url.searchParams.get('year');

        if (!query && !imdbId) {
            return json({ error: 'Missing query or imdbId parameter' }, { status: 400 });
        }

        console.log(`[OpenSubtitles] Searching subtitles for query: ${query}, imdbId: ${imdbId}, language: ${language}, year: ${year}`);

        // Przygotuj parametry wyszukiwania
        const searchParams = new URLSearchParams();
        if (query) searchParams.append('query', query);
        if (imdbId) searchParams.append('imdb_id', imdbId);
        if (language) searchParams.append('languages', language);
        if (year) searchParams.append('year', year);

        // Dodaj dodatkowe parametry
        searchParams.append('type', 'all');
        searchParams.append('order_by', 'download_count');
        searchParams.append('order_direction', 'desc');

        // Wykonaj zapytanie do API OpenSubtitles
        const response = await fetch(`${OPENSUBTITLES_API_URL}/subtitles?${searchParams.toString()}`, {
            method: 'GET',
            headers: {
                'Api-Key': OPENSUBTITLES_API_KEY,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'ProsiaczekApp v1.0.0'
            }
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error(`[OpenSubtitles] API error: ${response.status}`, errorData);
            return json({ error: `OpenSubtitles API error: ${response.status}` }, { status: response.status });
        }

        const data = await response.json();
        console.log(`[OpenSubtitles] Found ${data.data?.length || 0} subtitles`);

        // Zwróć wyniki wyszukiwania
        return json({
            subtitles: data.data?.map(item => ({
                id: item.id,
                file_id: item.attributes.files[0]?.file_id,
                filename: item.attributes.files[0]?.name,
                language: item.attributes.language,
                download_count: item.attributes.download_count,
                rating: item.attributes.ratings,
                format: item.attributes.files[0]?.file_format,
                fps: item.attributes.fps,
                release: item.attributes.release,
                movie_name: item.attributes.feature_details?.movie_name,
                year: item.attributes.feature_details?.year,
                imdb_id: item.attributes.feature_details?.imdb_id,
                download_url: `/api/subtitles/opensubtitles/download?file_id=${item.attributes.files[0]?.file_id}`
            })) || []
        });
    } catch (error) {
        console.error('[OpenSubtitles] Error searching subtitles:', error);
        return json({ error: 'Failed to search subtitles' }, { status: 500 });
    }
}

/**
 * Pobiera napisy za pomocą API OpenSubtitles
 * @param {Request} request
 */
export async function POST({ request }) {
    // Sprawdź autoryzację
    const isAuthenticated = authenticateRequest(request);
    if (!isAuthenticated) {
        return new Response(JSON.stringify({ error: 'Unauthorized' }), {
            status: 401,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    try {
        const body = await request.json();
        const fileId = body.file_id;

        if (!fileId) {
            return json({ error: 'Missing file_id parameter' }, { status: 400 });
        }

        console.log(`[OpenSubtitles] Downloading subtitle with file_id: ${fileId}`);

        // Wykonaj zapytanie do API OpenSubtitles, aby uzyskać link do pobrania
        const downloadResponse = await fetch(`${OPENSUBTITLES_API_URL}/download`, {
            method: 'POST',
            headers: {
                'Api-Key': OPENSUBTITLES_API_KEY,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': 'ProsiaczekApp v1.0.0'
            },
            body: JSON.stringify({
                file_id: fileId
            })
        });

        if (!downloadResponse.ok) {
            const errorData = await downloadResponse.json();
            console.error(`[OpenSubtitles] Download API error: ${downloadResponse.status}`, errorData);
            return json({ error: `OpenSubtitles Download API error: ${downloadResponse.status}` }, { status: downloadResponse.status });
        }

        const downloadData = await downloadResponse.json();
        const downloadLink = downloadData.link;

        if (!downloadLink) {
            return json({ error: 'No download link provided by OpenSubtitles API' }, { status: 500 });
        }

        console.log(`[OpenSubtitles] Got download link: ${downloadLink}`);

        // Pobierz plik napisów
        const subtitleResponse = await fetch(downloadLink, {
            method: 'GET'
        });

        if (!subtitleResponse.ok) {
            console.error(`[OpenSubtitles] Error downloading subtitle: ${subtitleResponse.status}`);
            return json({ error: `Failed to download subtitle: ${subtitleResponse.status}` }, { status: subtitleResponse.status });
        }

        // Odczytaj zawartość pliku
        const subtitleContent = await subtitleResponse.text();

        // Konwertuj do VTT
        const vttContent = convertSrtToVtt(subtitleContent);

        // Generuj unikalną nazwę pliku VTT
        const vttFileName = `${uuidv4()}.vtt`;
        const vttFilePath = path.join(SUBTITLES_DIR, vttFileName);

        // Zapisz plik VTT
        fs.writeFileSync(vttFilePath, vttContent);

        // Zwróć URL do lokalnego pliku
        const localSubtitleUrl = `/subtitles/${vttFileName}`;
        console.log(`[OpenSubtitles] Saved VTT to: ${localSubtitleUrl}`);

        return json({ url: localSubtitleUrl });
    } catch (error) {
        console.error('[OpenSubtitles] Error downloading subtitle:', error);
        return json({ error: 'Failed to download subtitle' }, { status: 500 });
    }
}
