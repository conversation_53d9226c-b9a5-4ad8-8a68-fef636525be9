import { json } from '@sveltejs/kit';
import { extractToken } from '$lib/server/auth';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url, fetch, request, cookies }) {
    try {
        // Pobierz URL napisów z parametru zapytania
        const subtitleUrl = url.searchParams.get('url');

        if (!subtitleUrl) {
            return new Response('Missing subtitle URL parameter', { status: 400 });
        }

        console.log(`Proxying subtitle from: ${subtitleUrl}`);

        // Pobierz token autoryzacyjny
        const token = extractToken(request);

        // Przygotuj opcje dla fetch z autoryzacją
        const fetchOptions = {
            headers: {
                'Authorization': token ? `Bearer ${token}` : '',
                'Accept': 'text/plain, application/octet-stream'
            }
        };

        // Pobierz napisy z oryginalnego URL
        const response = await fetch(subtitleUrl, fetchOptions);

        if (!response.ok) {
            console.error(`Failed to fetch subtitles: ${response.status}`, await response.text());
            return new Response(`Failed to fetch subtitles: ${response.status}`, { status: response.status });
        }

        // Pobierz zawartość napisów
        const subtitleContent = await response.text();

        // Zwróć napisy z odpowiednimi nagłówkami
        return new Response(subtitleContent, {
            headers: {
                'Content-Type': 'text/plain; charset=utf-8',
                'Content-Disposition': 'attachment; filename="subtitles.srt"',
                'Access-Control-Allow-Origin': '*', // Pozwól na dostęp z dowolnej domeny
                'Cache-Control': 'max-age=3600' // Cache na 1 godzinę
            }
        });
    } catch (error) {
        console.error('Error proxying subtitles:', error);
        return new Response(`Error proxying subtitles: ${error.message}`, { status: 500 });
    }
}
