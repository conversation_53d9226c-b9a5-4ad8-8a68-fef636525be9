import { json } from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import fetch from 'node-fetch';

/**
 * Weryfikuje token JWT
 * @param {string} token Token JWT do weryfikacji
 * @returns {boolean} <PERSON><PERSON> token jest ważny
 */
function verifyToken(token) {
    try {
        if (!token) return false;

        // <PERSON>u<PERSON> prefix "Bearer " jeśli istnieje
        if (token.startsWith('Bearer ')) {
            token = token.slice(7);
        }

        // Weryfikuj token z kluczem z zmiennej środowiskowej lub domyślnym
        const secret = process.env.JWT_SECRET || 'your_jwt_secret';
        jwt.verify(token, secret);
        return true;
    } catch (error) {
        console.error('Error verifying token:', error);
        return false;
    }
}

/**
 * Czyści tytuł filmu/serialu
 * @param {string} title Tytuł do wyczyszczenia
 * @returns {string} Wyczyszczony tytuł
 */
function cleanTitle(title) {
    const originalTitle = title;
    const cleanedTitle = title
        .replace(/\.(mkv|mp4|avi)$/i, '')  // Usuń rozszerzenie pliku
        .replace(/\.\d{4}\..*$/i, '')      // Usuń rok i wszystko po nim
        .replace(/\(\d{4}\).*$/i, '')      // Usuń rok w nawiasach i wszystko po nim
        .replace(/\.(1080p|720p|2160p|4k|uhd|bluray|web-dl|webrip|hdtv|x264|x265|hevc|aac|ac3).*$/i, '') // Usuń informacje o jakości
        .replace(/\.(s\d{2}e\d{2}|season.\d+|episode.\d+).*$/i, '') // Usuń informacje o sezonie/odcinku
        .replace(/1080p|720p|2160p|4K|BluRay|WEB-DL|WEBRip|HDTV|x264|x265|HEVC|AAC|DD|AC3|Dual Audio|Hindi|Eng|ESub|By~.*?~.*$/gi, '') // Usuń informacje o jakości
        .replace(/Multi|UHD|Blu-Ray|HDR|ATMOS|DTOne/gi, '') // Usuń dodatkowe informacje o jakości
        .replace(/\./g, ' ')               // Zamień kropki na spacje
        .replace(/\s+/g, ' ')              // Zamień wielokrotne spacje na pojedyncze
        .trim();

    console.log(`Original title: ${originalTitle}`);
    console.log(`Cleaned title: ${cleanedTitle}`);

    return cleanedTitle;
}

/**
 * Wyciąga rok z nazwy pliku
 * @param {string} filename Nazwa pliku
 * @returns {number|null} Rok lub null, jeśli nie znaleziono
 */
function extractYear(filename) {
    // Próbujemy wyciągnąć rok z nazwy pliku (np. "Movie.Name.2020.1080p.mkv")
    let yearMatch = filename.match(/\.(\d{4})\./);
    if (yearMatch) {
        const year = parseInt(yearMatch[1], 10);
        if (year >= 1900 && year <= new Date().getFullYear()) {
            return year;
        }
    }

    // Próbujemy wyciągnąć rok z nawiasów (np. "Movie Name (2020)")
    yearMatch = filename.match(/\((\d{4})\)/);
    if (yearMatch) {
        const year = parseInt(yearMatch[1], 10);
        if (year >= 1900 && year <= new Date().getFullYear()) {
            return year;
        }
    }

    return null;
}

/**
 * Endpoint do wyszukiwania napisów (alternatywna metoda)
 */
export async function GET({ url, request }) {
    try {
        // Weryfikacja tokena JWT
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !verifyToken(authHeader)) {
            return json({
                error: 'Unauthorized',
                subtitles: []
            }, { status: 401 });
        }

        const query = url.searchParams.get('query');
        const lang = url.searchParams.get('lang') || 'pl';

        if (!query) {
            return json({
                error: 'Query parameter is required',
                subtitles: []
            }, { status: 400 });
        }

        console.log(`Searching subtitles for: ${query}, language: ${lang}`);

        // Wyciągamy tytuł i rok z zapytania
        const title = cleanTitle(query);
        const year = extractYear(query);

        console.log(`Parsed title: "${title}", year: ${year}`);

        // Mapowanie kodów języków
        const languageMap = {
            'pl': { code: 'pol', name: 'polish' },
            'en': { code: 'eng', name: 'english' },
            'fr': { code: 'fre', name: 'french' },
            'es': { code: 'spa', name: 'spanish' },
            'de': { code: 'ger', name: 'german' },
            'it': { code: 'ita', name: 'italian' },
            'ru': { code: 'rus', name: 'russian' },
            'pt': { code: 'por', name: 'portuguese' },
            'zh': { code: 'chi', name: 'chinese' },
            'ja': { code: 'jpn', name: 'japanese' }
        };

        // Pobierz kod i nazwę języka
        const langInfo = languageMap[lang] || { code: 'pol', name: 'polish' };
        console.log(`Language code: ${lang} -> ${langInfo.code}, ${langInfo.name}`);

        // Tworzymy link do OpenSubtitles
        const openSubtitlesUrl = `https://www.opensubtitles.org/pl/search/sublanguageid-${langInfo.code}/moviename-${encodeURIComponent(title)}${year ? `/year-${year}` : ''}`;

        // Tworzymy link do Napiprojekt
        const napiUrl = `https://www.napiprojekt.pl/napisy-${encodeURIComponent(title.toLowerCase().replace(/\s+/g, '-'))}`;

        // Tworzymy link do Subscene
        const subsceneUrl = `https://subscene.com/subtitles/searchbytitle?query=${encodeURIComponent(title)}&l=${langInfo.name}`;

        // Tworzymy link do Podnapisi
        // Format: https://podnapisi.net/en/subtitles/pl-scarface-1983/P7ke
        const podnapisiSlug = `${langInfo.code.substring(0, 2)}-${title.toLowerCase().replace(/\s+/g, '-')}${year ? `-${year}` : ''}`;
        const podnapisiUrl = `https://podnapisi.net/en/subtitles/${podnapisiSlug}/P7ke`;

        // Alternatywny link do wyszukiwania w Podnapisi
        const podnapisiSearchUrl = `https://www.podnapisi.net/subtitles/search/?keywords=${encodeURIComponent(title)}${year ? `&year=${year}` : ''}&language=${langInfo.code}`;

        // Zwracamy wyniki
        return json({
            query,
            title,
            year,
            language: lang,
            subtitles: [
                {
                    provider: 'opensubtitles',
                    language: lang,
                    page_link: openSubtitlesUrl,
                    download_link: openSubtitlesUrl,
                    filename: `${title}${year ? `.${year}` : ''}.${lang}.srt`,
                    release_info: '',
                    hearing_impaired: false,
                    score: 100
                },
                {
                    provider: 'podnapisi-direct',
                    language: lang,
                    page_link: podnapisiUrl,
                    download_link: podnapisiUrl,
                    filename: `${title}${year ? `.${year}` : ''}.${lang}.srt`,
                    release_info: 'Direct link',
                    hearing_impaired: false,
                    score: 98
                },
                {
                    provider: 'podnapisi-search',
                    language: lang,
                    page_link: podnapisiSearchUrl,
                    download_link: podnapisiSearchUrl,
                    filename: `${title}${year ? `.${year}` : ''}.${lang}.srt`,
                    release_info: 'Search page',
                    hearing_impaired: false,
                    score: 95
                },
                {
                    provider: 'napiprojekt',
                    language: lang,
                    page_link: napiUrl,
                    download_link: napiUrl,
                    filename: `${title}${year ? `.${year}` : ''}.${lang}.srt`,
                    release_info: '',
                    hearing_impaired: false,
                    score: 90
                },
                {
                    provider: 'subscene',
                    language: lang,
                    page_link: subsceneUrl,
                    download_link: subsceneUrl,
                    filename: `${title}${year ? `.${year}` : ''}.${lang}.srt`,
                    release_info: '',
                    hearing_impaired: false,
                    score: 80
                }
            ]
        });
    } catch (error) {
        console.error('Error searching subtitles:', error);
        return json({
            error: error.message || 'An error occurred while searching subtitles',
            subtitles: []
        }, { status: 500 });
    }
}
