import { json } from '@sveltejs/kit';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import os from 'os';
import jwt from 'jsonwebtoken';

const execAsync = promisify(exec);

/**
 * Weryfikuje token JWT
 * @param {string} token Token JWT do weryfikacji
 * @returns {boolean} Czy token jest ważny
 */
function verifyToken(token) {
    try {
        if (!token) return false;

        // Usuń prefix "Bearer " jeśli istnieje
        if (token.startsWith('Bearer ')) {
            token = token.slice(7);
        }

        // Weryfikuj token z kluczem z zmiennej środowiskowej lub domyślnym
        const secret = process.env.JWT_SECRET || 'your_jwt_secret';
        jwt.verify(token, secret);
        return true;
    } catch (error) {
        console.error('Error verifying token:', error);
        return false;
    }
}

/**
 * Endpoint do wyszukiwania napisów używając bezpośrednio Pythona i subliminal
 */
export async function GET({ url, request }) {
    try {
        // Weryfikacja tokena JWT
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !verifyToken(authHeader)) {
            return json({
                error: 'Unauthorized',
                subtitles: []
            }, { status: 401 });
        }

        const query = url.searchParams.get('query');
        const lang = url.searchParams.get('lang') || 'pl';

        if (!query) {
            return json({
                error: 'Query parameter is required',
                subtitles: []
            }, { status: 400 });
        }

        console.log(`Searching subtitles for: ${query}, language: ${lang}`);

        // Tworzymy tymczasowy plik Pythona
        const tempDir = path.join(os.tmpdir(), 'subliminal-python-' + Date.now());

        try {
            fs.mkdirSync(tempDir, { recursive: true });
        } catch (err) {
            console.error('Error creating temp directory:', err);
            return json({
                error: 'Failed to create temporary directory',
                subtitles: []
            }, { status: 500 });
        }

        // Wyciągamy tytuł i rok z zapytania
        let title = query;
        let year = null;

        // Próbujemy wyciągnąć rok z nazwy pliku (np. "Movie.Name.2020.1080p.mkv")
        let yearMatch = query.match(/\.(\d{4})\./);
        if (yearMatch) {
            year = parseInt(yearMatch[1], 10);
        }

        // Próbujemy wyciągnąć rok z nawiasów (np. "Movie Name (2020)")
        if (!year) {
            yearMatch = query.match(/\((\d{4})\)/);
            if (yearMatch) {
                year = parseInt(yearMatch[1], 10);
            }
        }

        // Czyścimy tytuł z rozszerzeń plików i innych informacji
        title = title.replace(/\.(mkv|mp4|avi)$/, '')  // Usuń rozszerzenie pliku
                     .replace(/^\/+/, '')              // Usuń ukośniki na początku tytułu

        // Zapisz oryginalny tytuł przed dalszym czyszczeniem
        const originalTitle = title;

        // Bardziej zaawansowane czyszczenie tytułu
        // Najpierw spróbuj wyodrębnić tytuł i rok za pomocą typowych wzorców
        let cleanedTitle = '';
        let extractedYear = null;

        // Wzorzec 1: "The.Movie.Title.2020.1080p.x264"
        const pattern1 = /^(.*?)\.(\d{4})[\.\s]/;
        const match1 = originalTitle.match(pattern1);

        // Wzorzec 2: "The Movie Title (2020) 1080p"
        const pattern2 = /^(.*?)\s*\((\d{4})\)/;
        const match2 = originalTitle.match(pattern2);

        // Wzorzec 3: "The Movie Title 2020 1080p"
        const pattern3 = /^(.*?)\s+(\d{4})\s+\d+p/;
        const match3 = originalTitle.match(pattern3);

        if (match1) {
            cleanedTitle = match1[1].replace(/\./g, ' ');
            extractedYear = parseInt(match1[2], 10);
        } else if (match2) {
            cleanedTitle = match2[1].trim();
            extractedYear = parseInt(match2[2], 10);
        } else if (match3) {
            cleanedTitle = match3[1].trim();
            extractedYear = parseInt(match3[2], 10);
        } else {
            // Jeśli żaden wzorzec nie pasuje, użyj standardowego czyszczenia
            cleanedTitle = originalTitle
                .replace(/\.\d{4}\..*$/, '')      // Usuń rok i wszystko po nim
                .replace(/\(\d{4}\).*$/, '')      // Usuń rok w nawiasach i wszystko po nim
                .replace(/1080p|720p|2160p|4K|BluRay|WEB-DL|WEBRip|HDTV|x264|x265|HEVC|AAC|DD|AC3|Dual Audio|Hindi|Eng|ESub|By~.*?~.*$/gi, '') // Usuń informacje o jakości
                .replace(/Multi|UHD|Blu-Ray|HDR|ATMOS|DTOne/gi, '') // Usuń dodatkowe informacje o jakości
                .replace(/\[.*?\]/g, '')          // Usuń wszystko w nawiasach kwadratowych
                .replace(/\.\d+\.\d+$/i, '')      // Usuń numery wersji na końcu
                .replace(/\./g, ' ')              // Zamień kropki na spacje
                .replace(/\s+/g, ' ')             // Zamień wielokrotne spacje na pojedyncze
                .trim();
        }

        // Jeśli znaleźliśmy rok, użyj go
        if (extractedYear && !year) {
            year = extractedYear;
        }

        // Ustaw oczyszczony tytuł
        title = cleanedTitle;

        // Sprawdź, czy tytuł nie został uszkodzony przez czyszczenie
        console.log(`Original query: ${query}`);
        console.log(`Cleaned title: ${title}`);

        console.log(`Parsed title: "${title}", year: ${year}`);

        // Tworzymy skrypt Pythona
        const pythonScript = `
import json
import sys
import os
import pkg_resources
from babelfish import Language
from subliminal import download_best_subtitles, region, scan_video
from subliminal.video import Movie, Episode

# Print subliminal version
try:
    subliminal_version = pkg_resources.get_distribution("subliminal").version
    print(f"Debug: Subliminal version: {subliminal_version}", file=sys.stderr)
except Exception as e:
    print(f"Debug: Could not get subliminal version: {e}", file=sys.stderr)

# Initialize cache
region.configure('dogpile.cache.memory')

# Parse input
title = "${title.replace(/"/g, '\\"')}"
year = ${year || 'None'}
language_code = "${lang}"

# Map language codes (subliminal uses ISO-639-3 codes)
language_map = {
    'pl': 'pol',
    'en': 'eng',
    'fr': 'fra',
    'es': 'spa',
    'de': 'deu',
    'it': 'ita',
    'ru': 'rus',
    'pt': 'por',
    'zh': 'zho',
    'ja': 'jpn',
    'ar': 'ara',
    'hi': 'hin',
    'ko': 'kor',
    'nl': 'nld',
    'sv': 'swe',
    'no': 'nor',
    'da': 'dan',
    'fi': 'fin',
    'cs': 'ces',
    'hu': 'hun',
    'tr': 'tur',
    'el': 'ell',
    'he': 'heb',
    'uk': 'ukr',
    'ro': 'ron',
    'bg': 'bul',
    'hr': 'hrv',
    'sk': 'slk',
    'sl': 'slv',
    'sr': 'srp',
    'th': 'tha',
    'vi': 'vie',
    'id': 'ind',
    'ms': 'msa',
    'fa': 'fas',
}

# Get the ISO-639-3 code for the language
language = language_map.get(language_code, language_code)
print(f"Debug: Language code: {language_code} -> {language}", file=sys.stderr)

# Debugging
print("Debug: Processing title: " + title, file=sys.stderr)
print("Debug: Year: " + str(year), file=sys.stderr)

# Create a temporary file path
temp_file_path = os.path.join(os.getcwd(), "temp_video.mkv")

try:
    # Method 1: Create a fake Movie object with the correct parameters
    # The API changed in newer versions, so we need to try different approaches
    try:
        # Try with both name and title first (based on error message)
        video = Movie(name=title, title=title)
        print("Debug: Created Movie object with name and title parameters", file=sys.stderr)
    except TypeError as e:
        print(f"Debug: TypeError with name and title parameters: {e}", file=sys.stderr)
        try:
            # Try with name, title and year
            video = Movie(name=title, title=title, year=year)
            print("Debug: Created Movie object with name, title and year parameters", file=sys.stderr)
        except TypeError as e:
            print(f"Debug: TypeError with name, title and year parameters: {e}", file=sys.stderr)
            try:
                # Try with all possible parameters
                video = Movie(
                    name=title,
                    title=title,
                    year=year,
                    path=temp_file_path
                )
                print("Debug: Created Movie object with all parameters", file=sys.stderr)
            except TypeError as e:
                print(f"Debug: TypeError with all parameters: {e}", file=sys.stderr)
                try:
                    # Try newer API
                    video = Movie(name=title, year=year)
                    print("Debug: Created Movie object with name parameter", file=sys.stderr)
                except TypeError as e:
                    print(f"Debug: TypeError with name parameter: {e}", file=sys.stderr)
                    try:
                        # Try older API
                        video = Movie(title=title, year=year)
                        print("Debug: Created Movie object with title parameter", file=sys.stderr)
                    except TypeError as e:
                        print(f"Debug: TypeError with title parameter: {e}", file=sys.stderr)
                        try:
                            # Try with path parameter (some versions require this)
                            video = Movie(name=title, year=year, path=temp_file_path)
                            print("Debug: Created Movie object with name and path parameters", file=sys.stderr)
                        except TypeError as e:
                            print(f"Debug: TypeError with name and path parameters: {e}", file=sys.stderr)
                            try:
                                # Try with title and path parameters
                                video = Movie(title=title, year=year, path=temp_file_path)
                                print("Debug: Created Movie object with title and path parameters", file=sys.stderr)
                            except TypeError as e:
                                print(f"Debug: TypeError with title and path parameters: {e}", file=sys.stderr)
                                try:
                                    # Last resort - try with minimal parameters
                                    video = Movie(path=temp_file_path)
                                    print("Debug: Created Movie object with only path parameter", file=sys.stderr)
                                except Exception as e:
                                    print(f"Debug: All attempts to create Movie object failed: {e}", file=sys.stderr)
                                    print("Debug: Falling back to alternative method", file=sys.stderr)
                                    # Return empty results to trigger fallback to alternative method
                                    print(json.dumps([]))
                                    sys.exit(0)

    # Download subtitles
    print("Debug: Downloading subtitles...", file=sys.stderr)
    subtitles = download_best_subtitles([video], {Language(language)})
    print("Debug: Downloaded subtitles: " + str(bool(subtitles)), file=sys.stderr)

    # Process results
    results = []
    if video in subtitles and subtitles[video]:
        print("Debug: Found " + str(len(subtitles[video])) + " subtitles", file=sys.stderr)
        for subtitle in subtitles[video]:
            sub_info = {
                'provider': getattr(subtitle, 'provider_name', ''),
                'language': str(subtitle.language),
                'page_link': getattr(subtitle, 'page_link', ''),
                'download_link': getattr(subtitle, 'download_link', ''),
                'filename': getattr(subtitle, 'filename', ''),
                'release_info': getattr(subtitle, 'release_info', ''),
                'hearing_impaired': getattr(subtitle, 'hearing_impaired', False),
                'score': getattr(subtitle, 'score', 0)
            }
            results.append(sub_info)
            print("Debug: Subtitle: " + str(sub_info), file=sys.stderr)
    else:
        print("Debug: No subtitles found", file=sys.stderr)

    # Print results as JSON
    print(json.dumps(results))
except Exception as e:
    print("Debug: Error: " + str(e), file=sys.stderr)
    print(json.dumps([]))
`;

        const pythonScriptPath = path.join(tempDir, 'search_subtitles.py');

        try {
            fs.writeFileSync(pythonScriptPath, pythonScript);
        } catch (err) {
            console.error('Error writing Python script:', err);
            return json({
                error: 'Failed to create Python script',
                subtitles: []
            }, { status: 500 });
        }

        // Uruchamiamy skrypt Pythona
        console.log('Executing Python script...');

        let stdout, stderr;
        try {
            const result = await execAsync(`python3 "${pythonScriptPath}"`);
            stdout = result.stdout;
            stderr = result.stderr;
        } catch (err) {
            console.error('Error executing Python script:', err);

            // Wyświetl stderr, jeśli jest dostępny
            if (err.stderr) {
                console.error('Python stderr:', err.stderr);
                stderr = err.stderr;
            }

            // Wyświetl stdout, jeśli jest dostępny
            if (err.stdout) {
                console.log('Python stdout (despite error):', err.stdout);
                stdout = err.stdout;
            }

            // Spróbujmy wyczyścić tymczasowe pliki
            try {
                fs.unlinkSync(pythonScriptPath);
                fs.rmdirSync(tempDir, { recursive: true });
            } catch (cleanupErr) {
                console.error('Error cleaning up temp files:', cleanupErr);
            }

            // Jeśli mamy jakieś dane wyjściowe, spróbujmy je sparsować
            if (stdout && stdout.trim()) {
                try {
                    const results = JSON.parse(stdout);
                    if (Array.isArray(results)) {
                        return json({
                            query,
                            title,
                            year,
                            language: lang,
                            error: stderr || err.message,
                            subtitles: results
                        });
                    }
                } catch (parseErr) {
                    console.error('Error parsing stdout despite error:', parseErr);
                }
            }

            return json({
                error: 'Failed to execute Python script: ' + (err.message || 'Unknown error'),
                stderr: stderr || err.stderr || 'No stderr output',
                subtitles: []
            }, { status: 500 });
        }

        console.log('Python script output:', stdout);

        if (stderr) {
            console.error('Python script error:', stderr);
        }

        // Parsujemy wyniki
        let subtitles = [];
        try {
            subtitles = JSON.parse(stdout);
        } catch (err) {
            console.error('Error parsing Python output:', err);
            return json({
                error: 'Failed to parse Python output',
                subtitles: []
            }, { status: 500 });
        }

        // Spróbujmy wyczyścić tymczasowe pliki
        try {
            fs.unlinkSync(pythonScriptPath);
            fs.rmdirSync(tempDir, { recursive: true });
        } catch (cleanupErr) {
            console.error('Error cleaning up temp files:', cleanupErr);
        }

        return json({
            query,
            title,
            year,
            language: lang,
            subtitles
        });
    } catch (error) {
        console.error('Error searching subtitles:', error);
        return json({
            error: error.message || 'An error occurred while searching subtitles',
            subtitles: []
        }, { status: 500 });
    }
}
