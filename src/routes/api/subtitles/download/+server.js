import { json } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import { pipeline } from 'stream/promises';
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';
import { authenticateRequest } from '$lib/server/auth';
import { createWriteStream, createReadStream } from 'fs';
import { Readable } from 'stream';
import { promisify } from 'util';
import yauzl from 'yauzl';
import iconv from 'iconv-lite';
import jschardet from 'jschardet';

// Folder dla tymczasowych plików napisów
const SUBTITLES_DIR = path.join(process.cwd(), 'static', 'subtitles');
const TEMP_DIR = path.join(process.cwd(), 'static', 'subtitles', 'temp');

// Upewnij się, że foldery istnieją
try {
    if (!fs.existsSync(SUBTITLES_DIR)) {
        fs.mkdirSync(SUBTITLES_DIR, { recursive: true });
    }
    if (!fs.existsSync(TEMP_DIR)) {
        fs.mkdirSync(TEMP_DIR, { recursive: true });
    }
} catch (error) {
    console.error('Error creating subtitles directories:', error);
}

// Promisify yauzl functions
const openZipFromBuffer = promisify((buffer, options, callback) => {
    yauzl.fromBuffer(buffer, options, callback);
});

/**
 * Konwertuje SRT do VTT
 * @param {string} srtContent Zawartość pliku SRT
 * @returns {string} Zawartość pliku VTT
 */
function convertSrtToVtt(srtContent) {
    // Sprawdź, czy plik jest już w formacie WebVTT
    if (srtContent.trim().startsWith('WEBVTT')) {
        return srtContent;
    }

    // Sprawdź, czy plik jest w formacie MicroDVD (z nawiasami klamrowymi)
    if (isMicroDvdFormat(srtContent)) {
        console.log('[Subtitles] Detected MicroDVD format, converting to WebVTT');
        return convertMicroDvdToVtt(srtContent);
    }

    // Sprawdź, czy plik jest w formacie SubRip (SRT)
    const isSrt = /\d+\s*\n\s*\d{2}:\d{2}:\d{2},\d{3}\s*-->\s*\d{2}:\d{2}:\d{2},\d{3}\s*\n/.test(srtContent);

    // Sprawdź, czy plik jest w formacie SubViewer (SUB)
    const isSub = /\[\d{2}:\d{2}:\d{2}\.\d{2}\]\[\d{2}:\d{2}:\d{2}\.\d{2}\]/.test(srtContent);

    // Dodaj nagłówek WebVTT
    let vttContent = 'WEBVTT\n\n';

    if (isSrt) {
        console.log('[Subtitles] Detected SubRip (SRT) format, converting to WebVTT');

        // Zamień format czasu z SRT (00:00:00,000) na VTT (00:00:00.000)
        const lines = srtContent.split('\n');
        let inTimestamp = false;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Pomiń numery sekwencji
            if (/^\d+$/.test(line)) {
                continue;
            }

            // Konwertuj znaczniki czasu
            if (line.includes('-->')) {
                inTimestamp = true;
                vttContent += line.replace(/,/g, '.') + '\n';
            } else if (line === '') {
                inTimestamp = false;
                vttContent += '\n';
            } else {
                vttContent += line + '\n';
            }
        }
    } else if (isSub) {
        console.log('[Subtitles] Detected SubViewer (SUB) format, converting to WebVTT');

        // Konwertuj format SubViewer na WebVTT
        const lines = srtContent.split('\n');

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Konwertuj znaczniki czasu z formatu [00:00:00.00][00:00:00.00] na format WebVTT
            const timeMatch = line.match(/\[(\d{2}:\d{2}:\d{2})\.(\d{2})\]\[(\d{2}:\d{2}:\d{2})\.(\d{2})\](.*)/);

            if (timeMatch) {
                const startTime = timeMatch[1] + '.' + (timeMatch[2] + '0');
                const endTime = timeMatch[3] + '.' + (timeMatch[4] + '0');
                const text = timeMatch[5].trim();

                vttContent += `${startTime} --> ${endTime}\n${text}\n\n`;
            } else if (line !== '') {
                // Jeśli to nie jest linia z czasem, a nie jest pusta, to może to być kontynuacja tekstu
                vttContent += line + '\n\n';
            }
        }
    } else {
        console.log('[Subtitles] Unknown subtitle format, trying generic conversion');

        // Próba generycznej konwersji - zakładamy, że to może być SRT z nietypowym formatowaniem
        const lines = srtContent.split('\n');
        let inTimestamp = false;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Pomiń numery sekwencji
            if (/^\d+$/.test(line)) {
                continue;
            }

            // Konwertuj znaczniki czasu - obsługuj różne separatory
            if (line.includes('-->')) {
                inTimestamp = true;
                // Zamień wszystkie separatory (przecinki, kropki) na kropki w formacie WebVTT
                const convertedLine = line.replace(/(\d{2}:\d{2}:\d{2})[,\.](\d{3})/g, '$1.$2');
                vttContent += convertedLine + '\n';
            } else if (line === '') {
                inTimestamp = false;
                vttContent += '\n';
            } else {
                vttContent += line + '\n';
            }
        }
    }

    // Upewnij się, że plik kończy się pustą linią
    if (!vttContent.endsWith('\n\n')) {
        vttContent += '\n';
    }

    return vttContent;
}

/**
 * Sprawdza, czy zawartość jest w formacie MicroDVD
 * @param {string} content Zawartość pliku
 * @returns {boolean} Czy plik jest w formacie MicroDVD
 */
function isMicroDvdFormat(content) {
    // Sprawdź pierwsze kilka linii, czy pasują do formatu {123}{456}Text
    const lines = content.split('\n').slice(0, 10); // Sprawdź pierwsze 10 linii
    const microDvdRegex = /^\{(\d+)\}\{(\d+)\}(.*)$/;

    // Jeśli przynajmniej 3 linie pasują do formatu, uznajemy że to MicroDVD
    let matchCount = 0;
    for (const line of lines) {
        if (line.trim() && microDvdRegex.test(line.trim())) {
            matchCount++;
        }
    }

    console.log(`[Subtitles] MicroDVD format detection: ${matchCount} matching lines out of ${lines.length}`);
    return matchCount >= 3;
}

/**
 * Konwertuje napisy MicroDVD do formatu WebVTT
 * @param {string} content Zawartość pliku MicroDVD
 * @returns {string} Zawartość pliku WebVTT
 */
function convertMicroDvdToVtt(content) {
    // Domyślna liczba klatek na sekundę
    const fps = detectFps(content) || 23.976;
    console.log(`[Subtitles] Using FPS: ${fps}`);

    const lines = content.split('\n');
    const microDvdRegex = /^\{(\d+)\}\{(\d+)\}(.*)$/;
    const outputLines = ['WEBVTT\n'];

    for (const line of lines) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        const match = microDvdRegex.exec(trimmedLine);
        if (match) {
            const startFrame = parseInt(match[1]);
            const endFrame = parseInt(match[2]);
            let text = match[3].trim();

            // Konwertuj klatki na timestamp
            const startTs = framesToTimestamp(startFrame, fps);
            const endTs = framesToTimestamp(endFrame, fps);

            // Tymczasowo wyłączamy naprawianie kodowania
            // text = fixEncoding(text);
            console.log('[Subtitles] Skipping encoding fix, assuming UTF-8');

            // Dodaj linię z czasem i tekstem
            outputLines.push(`${startTs} --> ${endTs}`);
            outputLines.push(text);
            outputLines.push(''); // Pusta linia po napisie
        }
    }

    return outputLines.join('\n');
}

/**
 * Konwertuje numer klatki na timestamp WebVTT
 * @param {number} frame Numer klatki
 * @param {number} fps Liczba klatek na sekundę
 * @returns {string} Timestamp w formacie WebVTT
 */
function framesToTimestamp(frame, fps) {
    const seconds = frame / fps;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const millis = Math.floor((seconds - Math.floor(seconds)) * 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${millis.toString().padStart(3, '0')}`;
}

/**
 * Próbuje naprawić kodowanie tekstu
 * @param {string} text Tekst do naprawy
 * @returns {string} Naprawiony tekst
 */
function fixEncoding(text) {
    // Sprawdź, czy tekst zawiera polskie znaki, które mogą być źle zakodowane
    const hasPolishChars = /[ąćęłńóśźżĄĆĘŁŃÓŚŹŻ]/.test(text);

    // Sprawdź, czy tekst zawiera znaki, które mogą wskazywać na problemy z kodowaniem
    const hasPotentialEncodingIssues = /[\u00F3\u0105\u0107\u0119\u0142\u0144\u00F3\u015B\u017A\u017C\u00F1\u00E1\u00E9\u00ED\u00F3\u00FA\u00FC\u00D3\u0104\u0106\u0118\u0141\u0143\u00D3\u015A\u0179\u017B]/.test(text);

    // Jeśli tekst zawiera polskie znaki i nie ma potencjalnych problemów z kodowaniem, prawdopodobnie jest już poprawnie zakodowany
    if (hasPolishChars && !hasPotentialEncodingIssues) {
        console.log('[Subtitles] Text already contains Polish characters and no encoding issues, keeping as is');
        return text;
    }

    console.log('[Subtitles] Attempting to fix encoding issues');

    // Użyj jschardet do wykrycia kodowania
    const detectionResult = jschardet.detect(text);
    console.log(`[Subtitles] jschardet detected encoding: ${detectionResult.encoding} (confidence: ${detectionResult.confidence})`);

    // Funkcja do testowania jakości dekodowania
    const scoreEncoding = (decoded) => {
        // Liczba polskich znaków w tekście
        const polishCharsCount = (decoded.match(/[ąćęłńóśźżĄĆĘŁŃÓŚŹŻ]/g) || []).length;

        // Liczba dziwnych znaków, które mogą wskazywać na problemy z kodowaniem
        const strangeCharsCount = (decoded.match(/[\u00F0-\u00FF\u0080-\u009F\u00A0-\u00BF]/g) || []).length;

        // Oblicz wynik - więcej polskich znaków to lepiej, więcej dziwnych znaków to gorzej
        return polishCharsCount * 2 - strangeCharsCount;
    };

    // Tablica możliwych kodowań do sprawdzenia
    let encodings = [
        'windows-1250',  // Najpopularniejsze dla polskich znaków w Windows
        'iso-8859-2',    // Popularne w starszych systemach
        'cp1250',        // Alias dla windows-1250
        'latin2',        // Alias dla iso-8859-2
        'utf-8',         // Standardowe kodowanie
        'latin1',        // ISO-8859-1, czasami używane
        'ascii'          // Ostatnia próba z ASCII
    ];

    // Jeśli jschardet wykrył kodowanie z wysoką pewnością, dodaj je na początek listy
    if (detectionResult.encoding && detectionResult.confidence > 0.5) {
        // Mapuj nazwy kodowań z jschardet na nazwy obsługiwane przez iconv-lite
        const encodingMap = {
            'windows-1250': 'windows-1250',
            'windows-1251': 'windows-1251',
            'windows-1252': 'windows-1252',
            'windows-1253': 'windows-1253',
            'iso-8859-1': 'latin1',
            'iso-8859-2': 'iso-8859-2',
            'iso-8859-5': 'iso-8859-5',
            'iso-8859-7': 'iso-8859-7',
            'utf-8': 'utf-8',
            'ascii': 'ascii',
            'Big5': 'big5',
            'GB2312': 'gb2312',
            'EUC-KR': 'euc-kr',
            'EUC-JP': 'euc-jp',
            'Shift_JIS': 'shift_jis'
        };

        const mappedEncoding = encodingMap[detectionResult.encoding] || detectionResult.encoding;

        // Usuń wykryte kodowanie z listy, jeśli już tam jest
        encodings = encodings.filter(enc => enc.toLowerCase() !== mappedEncoding.toLowerCase());

        // Dodaj wykryte kodowanie na początek listy
        encodings.unshift(mappedEncoding);

        console.log(`[Subtitles] Added detected encoding ${mappedEncoding} to the beginning of the list`);
    }

    // Przechowuj najlepsze dekodowanie i jego wynik
    let bestDecoded = text;
    let bestScore = scoreEncoding(text);
    let bestEncoding = 'original';

    // Spróbuj różnych kodowań
    for (const encoding of encodings) {
        try {
            // Konwertuj tekst na bufor używając binarnego kodowania
            const buffer = Buffer.from(text, 'binary');

            // Dekoduj bufor używając określonego kodowania
            const decoded = iconv.decode(buffer, encoding);

            // Oblicz wynik dla tego dekodowania
            const score = scoreEncoding(decoded);

            console.log(`[Subtitles] Encoding ${encoding} score: ${score}`);

            // Jeśli to dekodowanie ma lepszy wynik, zapisz je
            if (score > bestScore) {
                bestDecoded = decoded;
                bestScore = score;
                bestEncoding = encoding;
                console.log(`[Subtitles] Found better encoding: ${encoding} with score ${score}`);
            }
        } catch (e) {
            console.error(`[Subtitles] Error decoding with ${encoding}:`, e);
        }
    }

    // Dodatkowa próba z podwójnym dekodowaniem (czasami pomaga)
    try {
        // Najpierw dekoduj jako latin1, a potem jako utf-8
        const buffer = Buffer.from(text, 'binary');
        const latin1Decoded = iconv.decode(buffer, 'latin1');
        const utf8Buffer = Buffer.from(latin1Decoded, 'binary');
        const doubleDecoded = iconv.decode(utf8Buffer, 'utf-8');

        const score = scoreEncoding(doubleDecoded);
        console.log(`[Subtitles] Double decoding (latin1->utf8) score: ${score}`);

        if (score > bestScore) {
            bestDecoded = doubleDecoded;
            bestScore = score;
            bestEncoding = 'double(latin1->utf8)';
            console.log(`[Subtitles] Double decoding is better with score ${score}`);
        }
    } catch (e) {
        console.error('[Subtitles] Error with double decoding:', e);
    }

    // Dodatkowa próba z podwójnym dekodowaniem (windows-1250 -> utf-8)
    try {
        // Najpierw dekoduj jako windows-1250, a potem jako utf-8
        const buffer = Buffer.from(text, 'binary');
        const win1250Decoded = iconv.decode(buffer, 'windows-1250');
        const utf8Buffer = Buffer.from(win1250Decoded, 'binary');
        const doubleDecoded = iconv.decode(utf8Buffer, 'utf-8');

        const score = scoreEncoding(doubleDecoded);
        console.log(`[Subtitles] Double decoding (windows-1250->utf8) score: ${score}`);

        if (score > bestScore) {
            bestDecoded = doubleDecoded;
            bestScore = score;
            bestEncoding = 'double(windows-1250->utf8)';
            console.log(`[Subtitles] Double decoding (windows-1250->utf8) is better with score ${score}`);
        }
    } catch (e) {
        console.error('[Subtitles] Error with double decoding (windows-1250->utf8):', e);
    }

    console.log(`[Subtitles] Using encoding: ${bestEncoding} with score: ${bestScore}`);

    // Zwróć najlepsze dekodowanie
    return bestDecoded;
}

/**
 * Próbuje wykryć FPS na podstawie nazwy pliku lub zawartości
 * @param {string} content Zawartość pliku
 * @returns {number|null} Wykryte FPS lub null
 */
function detectFps(content) {
    // Sprawdź, czy w pierwszych liniach jest informacja o FPS
    const lines = content.split('\n').slice(0, 5);
    for (const line of lines) {
        // Niektóre pliki MicroDVD mają informację o FPS w pierwszej linii
        // Format: {1}{1}23.976
        const fpsMatch = /^\{1\}\{1\}(\d+\.\d+)$/.exec(line.trim());
        if (fpsMatch) {
            return parseFloat(fpsMatch[1]);
        }
    }

    // Domyślnie zwróć null (użyj domyślnego FPS)
    return null;
}

/**
 * Sprawdza, czy plik jest archiwum ZIP
 * @param {Buffer} buffer Bufor pliku
 * @returns {boolean} Czy plik jest archiwum ZIP
 */
function isZipFile(buffer) {
    // Sprawdź sygnaturę ZIP (pierwsze 4 bajty: 0x50 0x4B 0x03 0x04)
    return buffer.length >= 4 &&
           buffer[0] === 0x50 &&
           buffer[1] === 0x4B &&
           buffer[2] === 0x03 &&
           buffer[3] === 0x04;
}

/**
 * Rozpakuj archiwum ZIP i znajdź plik z napisami
 * @param {Buffer} zipBuffer Bufor archiwum ZIP
 * @returns {Promise<string>} Zawartość pliku z napisami
 */
async function extractSubtitlesFromZip(zipBuffer) {
    return new Promise((resolve, reject) => {
        yauzl.fromBuffer(zipBuffer, { lazyEntries: true }, (err, zipfile) => {
            if (err) return reject(err);

            let foundSubtitles = false;
            let subtitleContent = '';
            let foundEntries = [];

            zipfile.on('entry', (entry) => {
                const fileName = entry.fileName.toLowerCase();
                // Dodaj wpis do listy znalezionych plików
                foundEntries.push(fileName);

                // Szukaj plików z napisami (.srt, .sub, .txt, .ssa, .ass)
                if (fileName.endsWith('.srt') ||
                    fileName.endsWith('.sub') ||
                    fileName.endsWith('.txt') ||
                    fileName.endsWith('.ssa') ||
                    fileName.endsWith('.ass')) {

                    foundSubtitles = true;
                    console.log(`[Subtitles] Found subtitle file in ZIP: ${entry.fileName}`);

                    zipfile.openReadStream(entry, (err, readStream) => {
                        if (err) return reject(err);

                        const chunks = [];
                        readStream.on('data', (chunk) => chunks.push(chunk));
                        readStream.on('end', () => {
                            const buffer = Buffer.concat(chunks);

                            // Tymczasowo zakładamy, że wszystkie napisy są w UTF-8
                            subtitleContent = buffer.toString('utf8');
                            console.log('[Subtitles] Assuming UTF-8 encoding for subtitles in ZIP');

                            zipfile.readEntry();
                        });
                    });
                } else {
                    zipfile.readEntry();
                }
            });

            zipfile.on('end', () => {
                if (foundSubtitles) {
                    resolve(subtitleContent);
                } else {
                    console.log(`[Subtitles] No subtitle files found in ZIP. Found entries: ${foundEntries.join(', ')}`);

                    // Jeśli nie znaleziono plików z napisami, spróbuj użyć pierwszego pliku w archiwum
                    if (foundEntries.length > 0) {
                        console.log(`[Subtitles] Trying to use first file in archive: ${foundEntries[0]}`);

                        // Otwórz archiwum ponownie
                        yauzl.fromBuffer(zipBuffer, { lazyEntries: true }, (err, zipfile) => {
                            if (err) return reject(err);

                            zipfile.on('entry', (entry) => {
                                if (entry.fileName.toLowerCase() === foundEntries[0]) {
                                    zipfile.openReadStream(entry, (err, readStream) => {
                                        if (err) return reject(err);

                                        const chunks = [];
                                        readStream.on('data', (chunk) => chunks.push(chunk));
                                        readStream.on('end', () => {
                                            const buffer = Buffer.concat(chunks);

                                            // Spróbuj różne kodowania
                                            try {
                                                subtitleContent = buffer.toString('utf8');
                                            } catch (e) {
                                                try {
                                                    subtitleContent = iconv.decode(buffer, 'windows-1250');
                                                } catch (e) {
                                                    try {
                                                        subtitleContent = iconv.decode(buffer, 'iso-8859-2');
                                                    } catch (e) {
                                                        try {
                                                            subtitleContent = iconv.decode(buffer, 'cp1250');
                                                        } catch (e) {
                                                            subtitleContent = buffer.toString('utf8');
                                                        }
                                                    }
                                                }
                                            }

                                            resolve(subtitleContent);
                                        });
                                    });
                                } else {
                                    zipfile.readEntry();
                                }
                            });

                            zipfile.on('end', () => {
                                reject(new Error('Could not extract any file from ZIP archive'));
                            });

                            zipfile.readEntry();
                        });
                    } else {
                        reject(new Error('No files found in ZIP archive'));
                    }
                }
            });

            zipfile.readEntry();
        });
    });
}

/**
 * Pobiera napisy z URL i zapisuje je lokalnie
 * @param {Request} request
 */
export async function GET({ url, request }) {
    // Sprawdź autoryzację
    const isAuthenticated = authenticateRequest(request);
    if (!isAuthenticated) {
        return new Response(JSON.stringify({ error: 'Unauthorized' }), {
            status: 401,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    try {
        // Pobierz URL napisów z parametrów zapytania
        const subtitleUrl = url.searchParams.get('url');

        if (!subtitleUrl) {
            return json({ error: 'Missing subtitle URL' }, { status: 400 });
        }

        console.log(`[Subtitles] Downloading subtitles from: ${subtitleUrl}`);

        // Funkcja do pobierania z ponownymi próbami
        const fetchWithRetry = async (url, options = {}, maxRetries = 3, delay = 1000) => {
            let lastError;
            let lastResponse;
            let currentUrl = url;

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    console.log(`[Subtitles] Download attempt ${attempt}/${maxRetries}`);

                    // Dodaj timeout do opcji fetch
                    const fetchOptions = {
                        ...options,
                        timeout: 30000, // 30 sekund timeout
                    };

                    // Użyj node-fetch z timeoutem
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 30000);

                    // Wyświetl szczegóły żądania dla debugowania
                    console.log(`[Subtitles] Fetch options:`, {
                        url: currentUrl,
                        method: fetchOptions.method || 'GET',
                        headers: fetchOptions.headers ? Object.keys(fetchOptions.headers) : 'none',
                        redirect: fetchOptions.redirect
                    });

                    const fetchOptionsWithSignal = {
                        ...fetchOptions,
                        signal: controller.signal
                    };

                    const response = await fetch(currentUrl, fetchOptionsWithSignal);
                    clearTimeout(timeoutId);

                    lastResponse = response;

                    // Wyświetl szczegóły odpowiedzi dla debugowania
                    console.log(`[Subtitles] Response:`, {
                        status: response.status,
                        statusText: response.statusText,
                        url: response.url,
                        type: response.type,
                        redirected: response.redirected,
                        contentType: response.headers.get('content-type')
                    });

                    if (response.ok) {
                        return response;
                    }

                    lastError = new Error(`HTTP error: ${response.status} ${response.statusText}`);
                    console.error(`[Subtitles] Attempt ${attempt} failed: ${lastError.message}`);

                    // Jeśli to błąd 403 (Forbidden) lub 502 (Bad Gateway), spróbuj alternatywnego podejścia
                    if (response.status === 403 || response.status === 502) {
                        console.log(`[Subtitles] Detected ${response.status} error, trying alternative approach for next attempt`);

                        // Modyfikuj opcje dla następnej próby
                        if (!options.headers) options.headers = {};

                        // Dodaj lub zmodyfikuj nagłówki, które mogą pomóc
                        options.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
                        options.headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8';

                        // Usuń problematyczne nagłówki
                        delete options.headers['Accept-Encoding'];
                        delete options.headers['Sec-Ch-Ua'];
                        delete options.headers['Sec-Ch-Ua-Mobile'];
                        delete options.headers['Sec-Ch-Ua-Platform'];

                        // Dodaj losowy parametr do URL, aby uniknąć cache
                        const separator = currentUrl.includes('?') ? '&' : '?';
                        currentUrl = `${currentUrl}${separator}nocache=${Date.now()}`;
                    }

                    // Jeśli to nie jest ostatnia próba, poczekaj przed kolejną
                    if (attempt < maxRetries) {
                        const waitTime = delay * attempt; // Zwiększaj opóźnienie z każdą próbą
                        console.log(`[Subtitles] Waiting ${waitTime}ms before next attempt`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                    }
                } catch (error) {
                    lastError = error;
                    console.error(`[Subtitles] Attempt ${attempt} failed with error:`, error);

                    // Jeśli to błąd timeout, zwiększ timeout dla następnej próby
                    if (error.name === 'AbortError') {
                        console.log(`[Subtitles] Timeout error, increasing timeout for next attempt`);
                    }

                    // Jeśli to nie jest ostatnia próba, poczekaj przed kolejną
                    if (attempt < maxRetries) {
                        const waitTime = delay * attempt;
                        console.log(`[Subtitles] Waiting ${waitTime}ms before next attempt`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                    }
                }
            }

            // Jeśli wszystkie próby się nie powiodły, rzuć ostatni błąd
            console.error(`[Subtitles] All download attempts failed: ${lastError.message}`);

            // Jeśli mamy odpowiedź, dodaj ją do błędu dla lepszego debugowania
            if (lastResponse) {
                console.error(`[Subtitles] Last response: status=${lastResponse.status}, url=${lastResponse.url}`);
            }

            throw lastError;
        };

        // Pobierz plik napisów z ponownymi próbami
        let response;
        try {
            // Określ odpowiednie nagłówki w zależności od domeny
            let headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'pl,en-US;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Cache-Control': 'max-age=0',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            };

            // Dodaj specyficzne nagłówki dla Podnapisi
            if (subtitleUrl.includes('podnapisi.net')) {
                headers['Referer'] = 'https://www.podnapisi.net/';
                headers['Origin'] = 'https://www.podnapisi.net';
                headers['Host'] = 'www.podnapisi.net';

                // Dodaj cookie, które może być wymagane przez Podnapisi
                headers['Cookie'] = 'pn_language=pl; pn_country=PL; pn_cookie_policy=1';
            } else if (subtitleUrl.includes('opensubtitles.org')) {
                headers['Referer'] = 'https://www.opensubtitles.org/';
                headers['Origin'] = 'https://www.opensubtitles.org';
                headers['Host'] = 'www.opensubtitles.org';
            }

            console.log(`[Subtitles] Downloading subtitles from: ${subtitleUrl}`);

            // Użyj fetchWithRetry z opcją redirect: 'follow' i nagłówkami
            response = await fetchWithRetry(subtitleUrl, {
                headers,
                redirect: 'follow' // Automatycznie podążaj za przekierowaniami
            });

            console.log(`[Subtitles] Response status: ${response.status}, URL: ${response.url}`);
            console.log(`[Subtitles] Content-Type: ${response.headers.get('content-type')}`);
        } catch (error) {
            console.error(`[Subtitles] All download attempts failed:`, error);
            return json({ error: `Failed to download subtitles after multiple attempts: ${error.message}` }, { status: 502 });
        }

        if (!response.ok) {
            console.error(`[Subtitles] Failed to download subtitles: ${response.status} ${response.statusText}`);
            return json({ error: `Failed to download subtitles: ${response.status}` }, { status: response.status });
        }

        // Sprawdź typ zawartości
        const contentType = response.headers.get('content-type');
        console.log(`[Subtitles] Content-Type: ${contentType}`);

        // Jeśli otrzymaliśmy HTML zamiast pliku napisów lub ZIP, to coś poszło nie tak
        if (contentType && contentType.includes('text/html')) {
            console.error(`[Subtitles] Received HTML instead of subtitles`);
            return json({ error: 'Received HTML instead of subtitles file' }, { status: 500 });
        }

        // Pobierz dane jako bufor
        const buffer = Buffer.from(await response.arrayBuffer());

        // Generuj unikalną nazwę pliku VTT
        const vttFileName = `${uuidv4()}.vtt`;
        const vttFilePath = path.join(SUBTITLES_DIR, vttFileName);

        let vttContent = '';

        // Sprawdź, czy plik jest archiwum ZIP
        if (isZipFile(buffer)) {
            console.log('[Subtitles] Detected ZIP file, extracting...');
            try {
                // Rozpakuj archiwum i znajdź plik z napisami
                const subtitleContent = await extractSubtitlesFromZip(buffer);
                // Konwertuj do VTT
                vttContent = convertSrtToVtt(subtitleContent);
            } catch (zipError) {
                console.error('[Subtitles] Error extracting ZIP:', zipError);
                return json({ error: 'Failed to extract subtitles from ZIP' }, { status: 500 });
            }
        } else {
            // Zakładamy, że plik jest w formacie SRT lub podobnym
            console.log('[Subtitles] Processing as SRT file...');

            // Tymczasowo zakładamy, że wszystkie napisy są w UTF-8
            let textContent = buffer.toString('utf8');
            console.log('[Subtitles] Assuming UTF-8 encoding for subtitles');

            // Konwertuj do VTT
            vttContent = convertSrtToVtt(textContent);
        }

        // Zapisz plik VTT
        fs.writeFileSync(vttFilePath, vttContent);

        // Zwróć URL do lokalnego pliku
        const localSubtitleUrl = `/subtitles/${vttFileName}`;
        console.log(`[Subtitles] Saved VTT to: ${localSubtitleUrl}`);

        return json({ url: localSubtitleUrl });
    } catch (error) {
        console.error('[Subtitles] Error processing subtitles:', error);
        return json({ error: 'Failed to process subtitles' }, { status: 500 });
    }
}
