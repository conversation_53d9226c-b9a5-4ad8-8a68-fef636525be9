/**
 * API endpoints for watch functionality
 */

// Disable prerendering for this API route
export const prerender = false;

import { json } from '@sveltejs/kit';
import {
    getWatchedItems,
    addWatchedItem,
    removeWatchedItem,
    checkWatchedItems,
    markWatchedItemAsNotified
} from '$lib/server/watch-service';

/**
 * GET handler - Get all watched items
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET() {
    try {
        const items = await getWatchedItems();

        return json({
            success: true,
            items
        });
    } catch (error) {
        console.error('Error getting watched items:', error);
        return json({
            success: false,
            message: 'An error occurred while getting watched items'
        }, { status: 500 });
    }
}

/**
 * POST handler - Add a new watched item
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const data = await request.json();
        const { query } = data;

        if (!query) {
            return json({
                success: false,
                message: 'Query is required'
            }, { status: 400 });
        }

        const result = await addWatchedItem(query);

        if (!result.success) {
            return json(result, { status: 400 });
        }

        return json(result);
    } catch (error) {
        console.error('Error adding watched item:', error);
        return json({
            success: false,
            message: 'An error occurred while adding watched item'
        }, { status: 500 });
    }
}

/**
 * DELETE handler - Remove a watched item
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function DELETE({ url }) {
    try {
        const id = url.searchParams.get('id');

        if (!id) {
            return json({
                success: false,
                message: 'Item ID is required'
            }, { status: 400 });
        }

        const result = await removeWatchedItem(id);

        if (!result.success) {
            return json(result, { status: 400 });
        }

        return json(result);
    } catch (error) {
        console.error('Error removing watched item:', error);
        return json({
            success: false,
            message: 'An error occurred while removing watched item'
        }, { status: 500 });
    }
}
