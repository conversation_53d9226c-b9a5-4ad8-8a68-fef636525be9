/**
 * API endpoint for checking watched items for new results
 */

// Disable prerendering for this API route
export const prerender = false;

import { json } from '@sveltejs/kit';
import { checkWatchedItems } from '$lib/server/watch-service';
import { sendDiscordMessage } from '$lib/server/discord-bot';

/**
 * GET handler - Check all watched items for new results
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        // Check if this is a manual check or automated
        const isManual = url.searchParams.get('manual') === 'true';

        // Check watched items
        const result = await checkWatchedItems();

        if (!result.success) {
            return json(result, { status: 500 });
        }

        // If there are new results and this is an automated check,
        // send notifications via Discord
        if (result.newResults.length > 0 && !isManual) {
            try {
                // Send Discord notifications
                for (const { item, results } of result.newResults) {
                    const message = `🔍 **Znaleziono nowe wyniki dla "${item.query}"!**\n\n` +
                        results.map((result, index) =>
                            `${index + 1}. **${result.title}** (${result.source})\n` +
                            `   Rozmiar: ${result.fileSize || 'Nieznany'}\n`
                        ).join('\n') +
                        '\n\nMożesz dodać te wyniki do pobierania w zakładce "Watch" na stronie.';

                    await sendDiscordMessage(message);
                }
            } catch (discordError) {
                console.error('Error sending Discord notifications:', discordError);
                // Continue even if Discord notifications fail
            }
        }

        return json({
            success: true,
            newResults: result.newResults,
            totalChecked: result.newResults.length
        });
    } catch (error) {
        console.error('Error checking watched items:', error);
        return json({
            success: false,
            message: 'An error occurred while checking watched items'
        }, { status: 500 });
    }
}
