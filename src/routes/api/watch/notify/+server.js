/**
 * API endpoint for marking watched items as notified
 */

import { json } from '@sveltejs/kit';
import { markWatchedItemAsNotified } from '$lib/server/watch-service';

/**
 * POST handler - Mark a watched item as notified
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const data = await request.json();
        const { id } = data;
        
        if (!id) {
            return json({
                success: false,
                message: 'Item ID is required'
            }, { status: 400 });
        }
        
        const result = await markWatchedItemAsNotified(id);
        
        if (!result.success) {
            return json(result, { status: 400 });
        }
        
        return json(result);
    } catch (error) {
        console.error('Error marking watched item as notified:', error);
        return json({
            success: false,
            message: 'An error occurred while marking watched item as notified'
        }, { status: 500 });
    }
}
