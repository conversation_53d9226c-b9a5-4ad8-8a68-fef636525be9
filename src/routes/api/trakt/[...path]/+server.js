/**
 * API endpoint for proxying requests to Trakt.tv API
 * This is a catch-all route that handles all paths under /api/trakt
 */

import { json } from '@sveltejs/kit';
import { TRAKT_CONFIG } from '$lib/config/apiConfig';
import { verifyToken } from '$lib/server/auth';

// Disable prerendering for this API route
export const prerender = false;

// Trakt.tv API base URL
const API_BASE_URL = TRAKT_CONFIG.BASE_URL;
const CLIENT_ID = TRAKT_CONFIG.CLIENT_ID;
const CLIENT_SECRET = TRAKT_CONFIG.CLIENT_SECRET;

/**
 * Handle GET requests
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url, request, params }) {
    try {
        // Verify JWT token
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return json({
                error: 'Unauthorized',
                details: 'Missing or invalid authorization token'
            }, {
                status: 401
            });
        }

        const token = authHeader.split(' ')[1];
        const decoded = verifyToken(token);

        if (!decoded) {
            return json({
                error: 'Unauthorized',
                details: 'Invalid or expired token'
            }, {
                status: 401
            });
        }

        // Get the path from the params
        const path = params.path || '';

        // Build the target URL
        const targetUrl = new URL(path, API_BASE_URL);
        console.log(`[Trakt Proxy] Target URL: ${targetUrl.toString()}`);

        // Copy query parameters
        url.searchParams.forEach((value, key) => {
            targetUrl.searchParams.append(key, value);
        });

        // Prepare headers
        const headers = new Headers();

        // Add Trakt.tv API headers
        headers.append('Content-Type', 'application/json');
        headers.append('trakt-api-version', '2');
        headers.append('trakt-api-key', CLIENT_ID);

        // Copy relevant headers from the original request
        for (const [key, value] of request.headers.entries()) {
            // Skip headers that shouldn't be forwarded
            if (!['host', 'origin', 'referer', 'content-length', 'authorization'].includes(key.toLowerCase())) {
                headers.append(key, value);
            }
        }

        console.log(`[Trakt Proxy] Request headers:`, Object.fromEntries([...headers.entries()]));

        // Make the request to Trakt.tv API
        console.log(`[Trakt Proxy] Sending request to: ${targetUrl.toString()}`);
        const response = await fetch(targetUrl.toString(), {
            method: 'GET',
            headers: headers,
            redirect: 'follow'
        });

        console.log(`[Trakt Proxy] Response status: ${response.status}`);

        // Check if response is OK
        if (!response.ok) {
            console.error(`[Trakt Proxy] Error response from API: ${response.status} ${response.statusText}`);
            const errorText = await response.text();
            console.error(`[Trakt Proxy] Error details: ${errorText}`);

            // Return error response
            return json({
                error: `API Error: ${response.status} ${response.statusText}`,
                details: errorText
            }, {
                status: response.status
            });
        }

        // Get response body
        const data = await response.json();

        // Return success response
        return json(data);
    } catch (error) {
        console.error('[Trakt Proxy] Error:', error);

        // Return error response
        return json({
            error: 'Internal Server Error',
            details: error.message
        }, {
            status: 500
        });
    }
}

/**
 * Handle POST requests
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ url, request, params }) {
    try {
        // Verify JWT token
        const authHeader = request.headers.get('authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return json({
                error: 'Unauthorized',
                details: 'Missing or invalid authorization token'
            }, {
                status: 401
            });
        }

        const token = authHeader.split(' ')[1];
        const decoded = verifyToken(token);

        if (!decoded) {
            return json({
                error: 'Unauthorized',
                details: 'Invalid or expired token'
            }, {
                status: 401
            });
        }

        // Get the path from the params
        const path = params.path || '';

        // Build the target URL
        const targetUrl = new URL(path, API_BASE_URL);
        console.log(`[Trakt Proxy] Target URL: ${targetUrl.toString()}`);

        // Copy query parameters
        url.searchParams.forEach((value, key) => {
            targetUrl.searchParams.append(key, value);
        });

        // Get request body
        const body = await request.json();

        // Prepare headers
        const headers = new Headers();

        // Add Trakt.tv API headers
        headers.append('Content-Type', 'application/json');
        headers.append('trakt-api-version', '2');
        headers.append('trakt-api-key', CLIENT_ID);

        // Copy relevant headers from the original request
        for (const [key, value] of request.headers.entries()) {
            // Skip headers that shouldn't be forwarded
            if (!['host', 'origin', 'referer', 'content-length', 'authorization'].includes(key.toLowerCase())) {
                headers.append(key, value);
            }
        }

        console.log(`[Trakt Proxy] Request headers:`, Object.fromEntries([...headers.entries()]));

        // Make the request to Trakt.tv API
        console.log(`[Trakt Proxy] Sending request to: ${targetUrl.toString()}`);
        const response = await fetch(targetUrl.toString(), {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(body),
            redirect: 'follow'
        });

        console.log(`[Trakt Proxy] Response status: ${response.status}`);

        // Check if response is OK
        if (!response.ok) {
            console.error(`[Trakt Proxy] Error response from API: ${response.status} ${response.statusText}`);
            const errorText = await response.text();
            console.error(`[Trakt Proxy] Error details: ${errorText}`);

            // Return error response
            return json({
                error: `API Error: ${response.status} ${response.statusText}`,
                details: errorText
            }, {
                status: response.status
            });
        }

        // Get response body
        const data = await response.json();

        // Return success response
        return json(data);
    } catch (error) {
        console.error('[Trakt Proxy] Error:', error);

        // Return error response
        return json({
            error: 'Internal Server Error',
            details: error.message
        }, {
            status: 500
        });
    }
}
