import { json } from '@sveltejs/kit';
import { getTorrentioStreams } from '$lib/server/torrentio-service.js';
import { TORRENTIO_API_BASE } from '$lib/server/torrentio-service.js';

export async function GET({ url }) {
    try {
        const imdbId = url.searchParams.get('imdbId');
        const season = parseInt(url.searchParams.get('season'), 10);
        const episode = parseInt(url.searchParams.get('episode'), 10);

        if (!imdbId) {
            return json({
                error: 'IMDB ID is required',
                results: []
            }, { status: 400 });
        }

        if (isNaN(season) || isNaN(episode)) {
            return json({
                error: 'Valid season and episode numbers are required',
                results: []
            }, { status: 400 });
        }

        console.log(`Fetching releases for ${imdbId} S${season}E${episode}`);

        // First, try to get the TV show info from OMDB to get the title
        let title = "Loading...";
        try {
            const omdbResponse = await fetch(`http://www.omdbapi.com/?apikey=c24805a2&i=${imdbId}`);
            if (omdbResponse.ok) {
                const omdbData = await omdbResponse.json();
                if (omdbData.Response === 'True') {
                    title = omdbData.Title;
                }
            }
        } catch (omdbError) {
            console.error('Error fetching OMDB data:', omdbError);
        }

        // Create a mock OMDB item with the IMDB ID
        const mockOmdbItem = {
            imdbID: imdbId,
            Title: title,
            Year: "",
            Type: "series",
            Poster: "N/A"
        };

        // Get all releases for this specific episode
        const releases = await getTorrentioStreams(mockOmdbItem, season, episode);

        console.log(`Found ${releases.length} releases for ${imdbId} S${season}E${episode}`);

        // If no releases found, try to get all episodes and filter manually
        if (releases.length === 0) {
            console.log(`No releases found with direct URL, trying to get all episodes and filter manually`);

            // Get all episodes
            const allEpisodes = await getTorrentioStreams(mockOmdbItem);

            // Check if we have any episodes at all
            if (allEpisodes.length === 0) {
                console.log(`No episodes found for ${imdbId}. This might be due to an incorrect IMDB ID.`);

                // Try to find the correct IMDB ID using Trakt.tv
                try {
                    console.log(`Trying to find correct IMDB ID for "${title}" using Trakt.tv`);

                    // Search Trakt.tv for the show
                    const traktSearchUrl = `https://api.trakt.tv/search/show?query=${encodeURIComponent(title)}`;

                    const traktResponse = await fetch(traktSearchUrl, {
                        headers: {
                            'Content-Type': 'application/json',
                            'trakt-api-key': '01d887218070ff644527e1fb9524ce51b096fd15faef26fd6c6f60ef7dac7978',
                            'trakt-api-version': '2'
                        }
                    });

                    if (traktResponse.ok) {
                        const traktData = await traktResponse.json();

                        if (traktData && traktData.length > 0) {
                            // Find the best match
                            const bestMatch = traktData[0];
                            const traktImdbId = bestMatch.show.ids.imdb;

                            if (traktImdbId && traktImdbId !== imdbId) {
                                console.log(`Found alternative IMDB ID from Trakt.tv: ${traktImdbId} for "${title}" (original: ${imdbId})`);

                                // Try again with the new IMDB ID
                                const alternativeMockItem = {
                                    ...mockOmdbItem,
                                    imdbID: traktImdbId
                                };

                                const alternativeReleases = await getTorrentioStreams(alternativeMockItem, season, episode);

                                if (alternativeReleases.length > 0) {
                                    console.log(`Found ${alternativeReleases.length} releases using alternative IMDB ID ${traktImdbId}`);

                                    // Sort by seeders and return
                                    alternativeReleases.sort((a, b) => b.seeders - a.seeders);
                                    return json(alternativeReleases);
                                } else {
                                    console.log(`No releases found with alternative IMDB ID ${traktImdbId} either`);

                                    // Try getting all episodes with the alternative ID
                                    const allAlternativeEpisodes = await getTorrentioStreams(alternativeMockItem);

                                    if (allAlternativeEpisodes.length > 0) {
                                        // Filter for the specific episode
                                        const filteredAlternativeReleases = allAlternativeEpisodes.filter(item => {
                                            if (!item.episodeInfo) return false;
                                            return item.episodeInfo.season === season && item.episodeInfo.episode === episode;
                                        });

                                        if (filteredAlternativeReleases.length > 0) {
                                            console.log(`Found ${filteredAlternativeReleases.length} releases after manual filtering with alternative IMDB ID`);

                                            // Sort by seeders and return
                                            filteredAlternativeReleases.sort((a, b) => b.seeders - a.seeders);
                                            return json(filteredAlternativeReleases);
                                        }
                                    }

                                    // Return error with suggestion to use the alternative ID
                                    return json({
                                        error: `No episodes found for this show. We tried with IMDB ID ${imdbId} and alternative ID ${traktImdbId}.`,
                                        alternativeImdbId: traktImdbId,
                                        debug: {
                                            originalImdbId: imdbId,
                                            alternativeImdbId: traktImdbId,
                                            title,
                                            season,
                                            episode
                                        }
                                    }, { status: 404 });
                                }
                            }
                        }
                    }
                } catch (traktError) {
                    console.error('Error searching Trakt.tv:', traktError);
                }

                // Return a helpful error message
                return json({
                    error: `No episodes found for this show. This might be due to an incorrect IMDB ID or the show not being available.`,
                    debug: {
                        imdbId,
                        title,
                        season,
                        episode
                    }
                }, { status: 404 });
            }

            // Filter for the specific episode
            const filteredReleases = allEpisodes.filter(item => {
                if (!item.episodeInfo) return false;

                // Log each episode for debugging
                console.log(`Checking episode: S${item.episodeInfo.season}E${item.episodeInfo.episode} against S${season}E${episode}`);

                return item.episodeInfo.season === season && item.episodeInfo.episode === episode;
            });

            console.log(`Found ${filteredReleases.length} releases after manual filtering`);

            if (filteredReleases.length > 0) {
                // Check if any of the filtered releases have the needsSpecificUrl flag
                const needsSpecificUrl = filteredReleases.some(item => item.needsSpecificUrl);

                if (needsSpecificUrl) {
                    console.log(`Found episodes that need specific URL format, fetching with specific format`);

                    // Use the imported TORRENTIO_API_BASE

                    // Construct the specific URL for this episode
                    const specificUrl = `${TORRENTIO_API_BASE}/sort=size/stream/series/${imdbId}:${season}:${episode}.json`;
                    console.log(`Fetching streams from specific URL: ${specificUrl}`);

                    try {
                        const response = await fetch(specificUrl);

                        if (response.ok) {
                            const data = await response.json();
                            const streams = data.streams || [];

                            if (streams.length > 0) {
                                console.log(`Found ${streams.length} streams using specific URL format`);

                                // Process these streams into our format
                                const specificMockItem = {
                                    ...mockOmdbItem,
                                    Title: `${title} S${season}E${episode}`
                                };

                                // Process the streams directly
                                const processedStreams = streams.map(stream => {
                                    try {
                                        const { infoHash, fileIdx, name, title: streamTitle, behaviorHints } = stream;

                                        // Skip if missing essential data
                                        if (!infoHash) return null;

                                        // Get filename from behaviorHints if available
                                        const filename = behaviorHints?.filename || '';

                                        // Extract quality, size, and seeders from the title
                                        const quality = streamTitle.match(/\b(720p|1080p|2160p|4K)\b/i)?.[1] || '';
                                        const size = streamTitle.match(/\b(\d+(\.\d+)?)\s*(GB|MB)\b/i)?.[0] || '';
                                        const seeders = parseInt(streamTitle.match(/\b(\d+)\s*seeders?\b/i)?.[1] || '0', 10);

                                        // Create magnet link
                                        const magnetLink = `magnet:?xt=urn:btih:${infoHash}&dn=${encodeURIComponent(specificMockItem.Title)}`;

                                        return {
                                            title: specificMockItem.Title,
                                            year: specificMockItem.Year,
                                            imdbID: imdbId,
                                            poster: specificMockItem.Poster,
                                            type: 'tvshow',
                                            infoHash: infoHash,
                                            fileIdx: fileIdx || 0,
                                            quality: quality,
                                            size: size,
                                            seeders: seeders,
                                            languages: [],
                                            filename: filename,
                                            episodeInfo: {
                                                season: season,
                                                episode: episode
                                            },
                                            magnetLink: magnetLink,
                                            source: 'Torrentio',
                                            originalTitle: streamTitle
                                        };
                                    } catch (error) {
                                        console.error(`Error processing stream: ${error}`);
                                        return null;
                                    }
                                }).filter(Boolean); // Remove null entries

                                if (processedStreams.length > 0) {
                                    console.log(`Processed ${processedStreams.length} streams using specific URL format`);

                                    // Sort by seeders and return
                                    processedStreams.sort((a, b) => b.seeders - a.seeders);
                                    return json(processedStreams);
                                }
                            }
                        }
                    } catch (error) {
                        console.error(`Error fetching specific episode: ${error}`);
                    }
                }

                // If we didn't find any releases with the specific URL format, use the filtered releases
                // Sort by seeders (highest first)
                filteredReleases.sort((a, b) => b.seeders - a.seeders);
                return json(filteredReleases);
            } else {
                // No episodes found for this specific season/episode
                return json({
                    error: `No releases found for S${season}E${episode} of "${title}".`,
                    debug: {
                        imdbId,
                        title,
                        season,
                        episode,
                        totalEpisodes: allEpisodes.length
                    }
                }, { status: 404 });
            }
        }

        // Sort by seeders (highest first)
        releases.sort((a, b) => b.seeders - a.seeders);

        return json(releases);
    } catch (error) {
        console.error('Error fetching episode releases:', error);
        return json({
            error: error.message || 'An error occurred while fetching episode releases',
            results: []
        }, { status: 500 });
    }
}
