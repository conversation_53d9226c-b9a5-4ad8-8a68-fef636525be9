/**
 * API endpoint for searching movies and TV shows via OMDB and Torrentio
 */

import { json } from '@sveltejs/kit';
import { searchMedia } from '$lib/server/torrentio-service';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        const query = url.searchParams.get('query');
        const type = url.searchParams.get('type'); // 'movie' or 'series'

        if (!query || query.trim().length < 3) {
            return json({
                error: 'Query must be at least 3 characters long',
                results: []
            }, { status: 400 });
        }

        console.log(`Searching for movies/TV shows with query: ${query} (Type: ${type || 'all'})`);

        // Use our updated Torrentio integration to search for media
        // This now searches OMDB first, then gets torrents from Torrentio
        const results = await searchMedia(query);

        // Filter results by type if specified
        let filteredResults = results;
        if (type === 'movie') {
            filteredResults = results.filter(result => result.type === 'movie');
            console.log(`Filtered to ${filteredResults.length} movies`);
        } else if (type === 'series') {
            filteredResults = results.filter(result => result.type === 'tvshow');
            console.log(`Filtered to ${filteredResults.length} TV shows`);
        }

        if (!filteredResults || filteredResults.length === 0) {
            console.log('No results found');
            return json({
                results: [],
                message: 'No results found'
            });
        }

        console.log(`Found ${filteredResults.length} results`);
        return json({ results: filteredResults });
    } catch (error) {
        console.error('Search error:', error);
        return json({
            error: `An error occurred during search: ${error.message}`,
            results: [],
            debug: {
                error: error.toString(),
                stack: error.stack,
                message: error.message
            }
        }, { status: 500 });
    }
}
