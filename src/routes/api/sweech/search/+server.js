// This route is not prerendered because it needs to run server-side code
export const prerender = false;

import { json } from '@sveltejs/kit';
import { searchGames } from '$lib/server/nxbrew-direct';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        const query = url.searchParams.get('query');

        if (!query || query.trim().length < 3) {
            return json({
                error: 'Query must be at least 3 characters long',
                results: []
            }, { status: 400 });
        }

        console.log(`Searching for games with query: ${query}`);

        // Use our direct integration to search for games
        const results = await searchGames(query);

        if (!results || results.length === 0) {
            console.log('No results found, using simulation');
            const simulatedResults = simulateSearchResults(query);
            return json({
                results: simulatedResults,
                simulation: true
            });
        }

        console.log(`Found ${results.length} results`);
        return json({ results });
    } catch (error) {
        console.error('Search error:', error);
        return json({
            error: `An error occurred during search: ${error.message}`,
            results: [],
            debug: {
                error: error.toString(),
                stack: error.stack,
                message: error.message
            }
        }, { status: 500 });
    }
}

/**
 * Simulate search results for development purposes
 * In production, this would be replaced with actual nxbrew-dl output
 */
function simulateSearchResults(query) {
    const lowerQuery = query.toLowerCase();

    // Sample game data
    const allGames = [
        {
            title: 'The Legend of Zelda: Breath of the Wild',
            url: 'https://example.com/zelda-botw',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1293830/header.jpg',
            region: 'USA',
            size: '13.4 GB'
        },
        {
            title: 'Super Mario Odyssey',
            url: 'https://example.com/mario-odyssey',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1659420/header.jpg',
            region: 'EUR',
            size: '5.7 GB'
        },
        {
            title: 'Animal Crossing: New Horizons',
            url: 'https://example.com/animal-crossing',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1158310/header.jpg',
            region: 'USA',
            size: '6.2 GB'
        },
        {
            title: 'Mario Kart 8 Deluxe',
            url: 'https://example.com/mario-kart-8',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1406830/header.jpg',
            region: 'USA',
            size: '7.0 GB'
        },
        {
            title: 'Splatoon 3',
            url: 'https://example.com/splatoon-3',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1506830/header.jpg',
            region: 'EUR',
            size: '6.0 GB'
        },
        {
            title: 'Metroid Dread',
            url: 'https://example.com/metroid-dread',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1005300/header.jpg',
            region: 'USA',
            size: '4.1 GB'
        },
        {
            title: 'Pokémon Scarlet',
            url: 'https://example.com/pokemon-scarlet',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1627720/header.jpg',
            region: 'USA',
            size: '7.2 GB'
        },
        {
            title: 'Pokémon Violet',
            url: 'https://example.com/pokemon-violet',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1627730/header.jpg',
            region: 'USA',
            size: '7.2 GB'
        },
        {
            title: 'Kirby and the Forgotten Land',
            url: 'https://example.com/kirby-forgotten-land',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1517850/header.jpg',
            region: 'USA',
            size: '5.8 GB'
        },
        {
            title: 'Fire Emblem: Three Houses',
            url: 'https://example.com/fire-emblem-three-houses',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1102190/header.jpg',
            region: 'EUR',
            size: '11.9 GB'
        },
        {
            title: 'Xenoblade Chronicles 3',
            url: 'https://example.com/xenoblade-chronicles-3',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1814050/header.jpg',
            region: 'USA',
            size: '15.0 GB'
        },
        {
            title: 'Bayonetta 3',
            url: 'https://example.com/bayonetta-3',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1636300/header.jpg',
            region: 'USA',
            size: '15.4 GB'
        },
        {
            title: 'Super Smash Bros. Ultimate',
            url: 'https://example.com/super-smash-bros-ultimate',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1332010/header.jpg',
            region: 'USA',
            size: '16.7 GB'
        },
        {
            title: 'Mario + Rabbids Sparks of Hope',
            url: 'https://example.com/mario-rabbids-sparks-of-hope',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1144360/header.jpg',
            region: 'EUR',
            size: '7.5 GB'
        },
        {
            title: 'Luigi\'s Mansion 3',
            url: 'https://example.com/luigis-mansion-3',
            image: 'https://cdn.cloudflare.steamstatic.com/steam/apps/1140390/header.jpg',
            region: 'USA',
            size: '6.3 GB'
        }
    ];

    // Filter games based on the query
    return allGames.filter(game =>
        game.title.toLowerCase().includes(lowerQuery)
    );
}
