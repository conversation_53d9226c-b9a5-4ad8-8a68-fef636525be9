// Disable prerendering for this API route
export const prerender = false;

import { json } from '@sveltejs/kit';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET() {
    try {
        // Pobierz konfigurację
        let config = {
            download_dir: '/Users/<USER>/Downloads'
        };

        try {
            const configPath = path.join(process.cwd(), '.config', 'nxbrew-dl', 'config.json');
            const configData = await fs.readFile(configPath, 'utf-8');
            config = JSON.parse(configData);
        } catch (configError) {
            console.error('Error reading config:', configError);
        }

        // Sprawdź status pobierania
        let downloadStatus = [];

        // Spróbuj różne warianty komendy statusu
        let statusOutput = null;

        // Spróbuj wersję z podkreślnikiem (nxbrew_dl)
        try {
            const { stdout, stderr } = await execAsync('python -m nxbrew_dl status --json');
            if (stdout && stdout.trim()) {
                statusOutput = stdout;
                console.log('Status using nxbrew_dl succeeded');
            }
        } catch (statusError1) {
            console.error('Status using nxbrew_dl failed:', statusError1.message);

            // Spróbuj wersję z myślnikiem (nxbrew)
            try {
                const { stdout, stderr } = await execAsync('python -m nxbrew status --json');
                if (stdout && stdout.trim()) {
                    statusOutput = stdout;
                    console.log('Status using nxbrew succeeded');
                }
            } catch (statusError2) {
                console.error('Status using nxbrew failed:', statusError2.message);

                // Spróbuj bezpośrednio komendę nxbrew-dl
                try {
                    const { stdout, stderr } = await execAsync('nxbrew-dl status --json');
                    if (stdout && stdout.trim()) {
                        statusOutput = stdout;
                        console.log('Status using direct nxbrew-dl command succeeded');
                    }
                } catch (statusError3) {
                    console.error('Status using direct nxbrew-dl command failed:', statusError3.message);
                }
            }
        }

        // Jeśli nie udało się pobrać statusu, sprawdź katalog pobierania
        if (!statusOutput) {
            console.log('No status output, checking download directory');

            try {
                // Sprawdź pliki w katalogu pobierania
                const downloadDir = config.download_dir || '/Users/<USER>/Downloads';
                const files = await fs.readdir(downloadDir);

                // Filtruj pliki związane z nxbrew-dl
                const nxbrewFiles = files.filter(file =>
                    file.endsWith('.nsp') ||
                    file.endsWith('.xci') ||
                    file.endsWith('.nsp.part') ||
                    file.endsWith('.xci.part') ||
                    file.includes('nxbrew')
                );

                // Pobierz informacje o plikach
                for (const file of nxbrewFiles) {
                    const filePath = path.join(downloadDir, file);
                    const stats = await fs.stat(filePath);

                    downloadStatus.push({
                        filename: file,
                        path: filePath,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime,
                        isComplete: !file.endsWith('.part')
                    });
                }

                console.log(`Found ${downloadStatus.length} nxbrew-related files in download directory`);
            } catch (dirError) {
                console.error('Error checking download directory:', dirError);
            }
        } else {
            // Parsuj wynik komendy status
            try {
                const parsedStatus = JSON.parse(statusOutput);
                downloadStatus = parsedStatus.downloads || [];
                console.log(`Parsed ${downloadStatus.length} downloads from status command`);
            } catch (parseError) {
                console.error('Error parsing status output:', parseError);
            }
        }

        // Jeśli nadal nie mamy statusu, zwróć symulowane dane
        if (downloadStatus.length === 0) {
            console.log('No download status found, using simulated data');
            downloadStatus = [
                {
                    title: 'The Legend of Zelda: Breath of the Wild',
                    filename: 'The_Legend_of_Zelda_Breath_of_the_Wild.nsp',
                    progress: 100,
                    status: 'complete',
                    size: '13.4 GB',
                    path: path.join(config.download_dir, 'The_Legend_of_Zelda_Breath_of_the_Wild.nsp')
                },
                {
                    title: 'Super Mario Odyssey',
                    filename: 'Super_Mario_Odyssey.nsp.part',
                    progress: 45,
                    status: 'downloading',
                    size: '5.7 GB',
                    path: path.join(config.download_dir, 'Super_Mario_Odyssey.nsp.part')
                }
            ];
        }

        return json({
            success: true,
            downloads: downloadStatus
        });
    } catch (error) {
        console.error('Status error:', error);
        return json({
            success: false,
            message: `An error occurred while checking status: ${error.message}`,
            downloads: []
        }, { status: 500 });
    }
}
