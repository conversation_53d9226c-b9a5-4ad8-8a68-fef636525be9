// Disable prerendering for this API route
export const prerender = false;

import { json } from '@sveltejs/kit';
import { downloadGame } from '$lib/server/nxbrew-direct';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const data = await request.json();
        const { gameUrl } = data;

        if (!gameUrl) {
            return json({
                success: false,
                message: 'Game URL is required'
            }, { status: 400 });
        }

        console.log(`Downloading game from URL: ${gameUrl}`);

        // Use our direct integration to download the game
        const result = await downloadGame(gameUrl);

        if (!result.success) {
            console.log('Download failed:', result.message);
            return json({
                success: false,
                message: result.message || 'Failed to download game'
            }, { status: 500 });
        }

        console.log('Download successful:', result.message);

        // Return success response with game info
        return json({
            success: true,
            message: result.message || 'Game added to JDownloader successfully',
            game_info: result.game_info || {}
        });
    } catch (error) {
        console.error('Download error:', error);
        return json({
            success: false,
            message: `An error occurred during download: ${error.message}`
        }, { status: 500 });
    }
}
