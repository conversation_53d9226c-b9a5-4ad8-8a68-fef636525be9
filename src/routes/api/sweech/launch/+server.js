import { json } from '@sveltejs/kit';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const data = await request.json();
        const { gameUrl } = data;

        // Uruchom nxbrew-dl jako osobny proces
        try {
            // Je<PERSON>li podano URL gry, otwórz nxbrew-dl z tym URL
            if (gameUrl) {
                console.log(`Launching nxbrew-dl with URL: ${gameUrl}`);
                exec(`nxbrew-dl "${gameUrl}"`, (error, stdout, stderr) => {
                    if (error) {
                        console.error(`Error launching nxbrew-dl: ${error.message}`);
                    }
                    if (stderr) {
                        console.error(`nxbrew-dl stderr: ${stderr}`);
                    }
                    console.log(`nxbrew-dl stdout: ${stdout}`);
                });
            } else {
                // W przeciwnym razie po prostu uruchom nxbrew-dl
                console.log('Launching nxbrew-dl');
                exec('nxbrew-dl', (error, stdout, stderr) => {
                    if (error) {
                        console.error(`Error launching nxbrew-dl: ${error.message}`);
                    }
                    if (stderr) {
                        console.error(`nxbrew-dl stderr: ${stderr}`);
                    }
                    console.log(`nxbrew-dl stdout: ${stdout}`);
                });
            }

            return json({
                success: true,
                message: 'nxbrew-dl uruchomiony pomyślnie'
            });
        } catch (launchError) {
            console.error('Error launching nxbrew-dl:', launchError);
            return json({
                success: false,
                message: `Błąd uruchamiania nxbrew-dl: ${launchError.message}`
            }, { status: 500 });
        }
    } catch (error) {
        console.error('Launch error:', error);
        return json({
            success: false,
            message: `Wystąpił błąd: ${error.message}`
        }, { status: 500 });
    }
}
