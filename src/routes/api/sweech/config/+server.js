// Disable prerendering for this API route
export const prerender = false;

import { json } from '@sveltejs/kit';
import { configureNxbrewDl } from '$lib/server/nxbrew-setup';
import fs from 'fs/promises';
import path from 'path';
import { DEFAULT_NXBREW_CONFIG } from '$lib/config/serverConfig.js';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const data = await request.json();

        // Save configuration
        const result = await configureNxbrewDl(data);

        if (!result.success) {
            return json({
                success: false,
                message: result.message
            }, { status: 500 });
        }

        return json({
            success: true,
            message: 'Configuration saved successfully'
        });
    } catch (error) {
        console.error('Config error:', error);
        return json({
            success: false,
            message: 'An error occurred while saving configuration'
        }, { status: 500 });
    }
}

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET() {
    try {
        // Try to read existing configuration
        const configPath = path.join(process.cwd(), '.config', 'nxbrew-dl', 'config.json');

        try {
            const configData = await fs.readFile(configPath, 'utf-8');
            const config = JSON.parse(configData);

            return json({
                success: true,
                config
            });
        } catch (readError) {
            // If file doesn't exist, return default config from environment variables
            return json({
                success: true,
                config: DEFAULT_NXBREW_CONFIG
            });
        }
    } catch (error) {
        console.error('Config error:', error);
        return json({
            success: false,
            message: 'An error occurred while reading configuration'
        }, { status: 500 });
    }
}
