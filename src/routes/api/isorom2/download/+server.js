/**
 * API endpoint for getting download links for ROM/ISO games via gameginie.com
 */

import { json } from '@sveltejs/kit';
import { getDownloadLinks } from '$lib/server/gameginie-service';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const body = await request.json();
        const { gameUrl } = body;

        if (!gameUrl) {
            return json({
                error: 'Game URL is required',
                success: false
            }, { status: 400 });
        }

        console.log(`[API] Getting download links from game URL: ${gameUrl}`);

        // Use our gameginie service to get the download links
        const result = await getDownloadLinks(gameUrl);

        if (result.error) {
            console.error('[API] Error getting download links:', result.error);
            return json({
                error: result.error,
                success: false
            }, { status: 500 });
        }

        if (!result.links || result.links.length === 0) {
            console.error('[API] No download links found');
            return json({
                error: 'No download links found',
                success: false
            }, { status: 404 });
        }

        console.log(`[API] Successfully retrieved ${result.links.length} download links of type ${result.hostingType}`);
        return json({
            success: true,
            title: result.title,
            links: result.links,
            hostingType: result.hostingType,
            imageUrl: result.imageUrl
        });
    } catch (error) {
        console.error('[API] Error processing download request:', error);
        return json({
            error: `An error occurred while processing download request: ${error.message}`,
            success: false,
            debug: {
                error: error.toString(),
                stack: error.stack,
                message: error.message
            }
        }, { status: 500 });
    }
}
