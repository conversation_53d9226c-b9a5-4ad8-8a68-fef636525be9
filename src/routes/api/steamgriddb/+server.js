/**
 * SteamGridDB API proxy route
 * Handles requests to the SteamGridDB API to bypass CORS restrictions
 */

import { STEAMGRIDDB_CONFIG } from '$lib/config/apiConfig';

// Disable prerendering for this API route
export const prerender = false;

// Get API configuration from centralized config
const { API_KEY, BASE_URL: API_BASE_URL } = STEAMGRIDDB_CONFIG;

/**
 * Handle GET requests to the SteamGridDB API
 */
export async function GET({ url }) {
    try {
        // During prerendering, return a mock response
        if (typeof url.searchParams === 'undefined') {
            return new Response(JSON.stringify({ prerendered: true, message: 'This is a prerendered response' }), {
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Get parameters from URL
        const endpoint = url.searchParams.get('endpoint');

        if (!endpoint) {
            return new Response(JSON.stringify({ error: 'Missing endpoint parameter' }), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }

        // Create URL to SteamGridDB API
        const apiUrl = `${API_BASE_URL}/${endpoint}`;
        console.log(`[SteamGridDB Proxy] Calling API: ${apiUrl}`);

        // Make request to SteamGridDB API
        const response = await fetch(apiUrl, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`
            }
        });

        // Check if response is OK
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[SteamGridDB Proxy] API Error (${response.status}):`, errorText);

            return new Response(JSON.stringify({
                error: `SteamGridDB API error: ${response.status}`,
                details: errorText
            }), {
                status: response.status,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }

        // Get data from response
        const data = await response.json();

        // Return response
        return new Response(JSON.stringify(data), {
            status: response.status,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error('[SteamGridDB Proxy] Error:', error);

        return new Response(JSON.stringify({
            error: 'Internal server error',
            message: error.message
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}
