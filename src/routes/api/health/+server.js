/**
 * Health check endpoint to verify the server is running
 */

import { json } from '@sveltejs/kit';

/**
 * Handle GET requests to the health check endpoint
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET() {
    return json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        host: process.env.HOST || 'unknown'
    });
}
