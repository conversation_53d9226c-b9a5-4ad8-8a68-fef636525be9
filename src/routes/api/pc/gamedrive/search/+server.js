// This route is not prerendered because it needs to run server-side code
export const prerender = false;

import { json } from '@sveltejs/kit';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';

const execAsync = promisify(exec);

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        const query = url.searchParams.get('query');

        if (!query || query.trim().length < 3) {
            return json({
                error: 'Query must be at least 3 characters long',
                results: []
            }, { status: 400 });
        }

        console.log(`Searching for PC games on GameDrive with query: ${query}`);

        // Use the gamedrive_search.py script
        const scriptPath = path.join(process.cwd(), 'gamedrive_search.py');

        // Execute the Python script
        const { stdout, stderr } = await execAsync(`python3 ${scriptPath} "${query}"`);

        if (stderr) {
            console.error('Debug info from Python script:', stderr);
        }

        // Parse the results
        const results = JSON.parse(stdout);
        console.log(`Found ${results.length} GameDrive results for query "${query}"`);
        
        return json({
            results: results
        });
    } catch (error) {
        console.error('Error searching GameDrive games:', error);
        return json({
            error: 'Failed to search for games',
            debug: error.toString(),
            results: []
        }, { status: 500 });
    }
}
