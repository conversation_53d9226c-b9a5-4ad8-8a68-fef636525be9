/**
 * Fanart.tv API proxy route
 * Handles requests to the Fanart.tv API to bypass CORS restrictions
 */

import { FANART_CONFIG } from '$lib/config/apiConfig';

// Disable prerendering for this API route
export const prerender = false;

/**
 * Handle GET requests to the Fanart.tv API
 */
export async function GET({ url }) {
    try {
        console.log('[Fanart Proxy] Received request');

        // Get the tmdbId from the query parameters
        const tmdbId = url.searchParams.get('tmdbId');

        if (!tmdbId) {
            console.error('[Fanart Proxy] Missing tmdbId parameter');
            return new Response(JSON.stringify({ error: 'Missing tmdbId parameter' }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Construct the fanart.tv API URL
        // Use project key as API key since the personal API key seems to be invalid
        const apiUrl = `${FANART_CONFIG.BASE_URL}/movies/${tmdbId}?api_key=${FANART_CONFIG.PROJECT_KEY}`;

        console.log(`[Fanart Proxy] Requesting: ${FANART_CONFIG.BASE_URL}/movies/${tmdbId}?api_key=***&client_key=***`);

        // Make the request to fanart.tv
        const response = await fetch(apiUrl);

        // Check if response is OK
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[Fanart Proxy] API Error (${response.status}):`, errorText);

            return new Response(JSON.stringify({
                error: `Fanart.tv API error: ${response.status}`,
                details: errorText
            }), {
                status: response.status,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Get data from response
        const data = await response.json();
        console.log(`[Fanart Proxy] Success: Got data for TMDB ID ${tmdbId}`);

        // Log the data structure for debugging
        console.log('[Fanart Proxy] Data structure:', JSON.stringify(data, null, 2));

        // Log specific artwork types if they exist
        if (data.hdmovielogo && data.hdmovielogo.length > 0) {
            console.log('[Fanart Proxy] Found HD movie logo:', data.hdmovielogo[0].url);
        }

        if (data.moviebackground && data.moviebackground.length > 0) {
            console.log('[Fanart Proxy] Found movie background:', data.moviebackground[0].url);
        }

        if (data.hdmovieclearart && data.hdmovieclearart.length > 0) {
            console.log('[Fanart Proxy] Found HD movie clearart:', data.hdmovieclearart[0].url);
        }

        // Return response
        return new Response(JSON.stringify(data), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        });
    } catch (error) {
        console.error('[Fanart Proxy] Error:', error);

        return new Response(JSON.stringify({
            error: 'Internal server error',
            message: error.message
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}
