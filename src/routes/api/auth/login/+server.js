/**
 * API endpoint for user authentication
 */

import { json } from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import { AUTH_CONFIG } from '$lib/config/authConfig';

/**
 * Handle POST requests for login
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        // Get credentials from request body
        const { username, password } = await request.json();

        // Check if credentials are provided
        if (!username || !password) {
            return json({
                success: false,
                message: 'Nazwa użytkownika i hasło są wymagane'
            }, { status: 400 });
        }

        // Check if environment variables are set
        if (!AUTH_CONFIG.USERNAME || !AUTH_CONFIG.PASSWORD) {
            console.error('Authentication credentials not set in environment variables');
            return json({
                success: false,
                message: 'Błąd konfiguracji serwera. Skontaktuj się z administratorem.'
            }, { status: 500 });
        }

        // Validate credentials
        if (username === AUTH_CONFIG.USERNAME && password === AUTH_CONFIG.PASSWORD) {
            // Generate JWT token
            const token = jwt.sign(
                { username },
                AUTH_CONFIG.JWT_SECRET,
                { expiresIn: AUTH_CONFIG.TOKEN_EXPIRATION }
            );

            // Return token
            return json({
                success: true,
                token,
                expiresIn: AUTH_CONFIG.TOKEN_EXPIRATION
            });
        } else {
            // Invalid credentials
            return json({
                success: false,
                message: 'Nieprawidłowa nazwa użytkownika lub hasło'
            }, { status: 401 });
        }
    } catch (error) {
        console.error('Login error:', error);
        return json({
            success: false,
            message: 'Wystąpił błąd podczas logowania'
        }, { status: 500 });
    }
}
