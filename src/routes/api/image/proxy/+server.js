import { error } from '@sveltejs/kit';

/**
 * Proxy for images to avoid CORS issues
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        // This endpoint is now public, no authentication required

        // Get the image URL from the query parameter
        const imageUrl = url.searchParams.get('url');

        if (!imageUrl) {
            throw error(400, 'Missing image URL parameter');
        }

        console.log(`[Image Proxy] Fetching image from: ${imageUrl}`);

        // Fetch the image
        const response = await fetch(imageUrl);

        if (!response.ok) {
            throw error(response.status, `Failed to fetch image: ${response.statusText}`);
        }

        // Get the image data as ArrayBuffer
        const imageData = await response.arrayBuffer();

        // Get the content type from the response
        const contentType = response.headers.get('content-type') || 'image/jpeg';

        // Return the image with appropriate headers
        return new Response(imageData, {
            status: 200,
            headers: {
                'Content-Type': contentType,
                'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
                'Access-Control-Allow-Origin': '*'
            }
        });
    } catch (err) {
        console.error('[Image Proxy] Error:', err);
        // Handle error with proper type checking
        let status = 500;
        let message = 'Image proxy error';

        if (err instanceof Error) {
            message = err.message;
            // Check if it has a status property
            if ('status' in err && typeof err.status === 'number') {
                status = err.status;
            }
        }

        throw error(status, message);
    }
}
