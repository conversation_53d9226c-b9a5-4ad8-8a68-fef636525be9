/**
 * API endpoint for downloading ROM/ISO games via blueroms.ws
 */

import { json } from '@sveltejs/kit';
import { getMagnetLink } from '$lib/server/blueroms-service';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ request }) {
    try {
        const body = await request.json();
        const { downloadUrl } = body;

        if (!downloadUrl) {
            return json({
                error: 'Download URL is required',
                success: false
            }, { status: 400 });
        }

        console.log(`[API] Getting magnet link from download URL: ${downloadUrl}`);

        // Use our blueroms service to get the magnet link
        const result = await getMagnetLink(downloadUrl);

        if (result.error) {
            console.error('[API] Error getting magnet link:', result.error);
            return json({
                error: result.error,
                success: false
            }, { status: 500 });
        }

        if (!result.magnetLink) {
            console.error('[API] No magnet link found');
            return json({
                error: 'No magnet link found',
                success: false
            }, { status: 404 });
        }

        console.log('[API] Successfully retrieved magnet link');
        return json({
            success: true,
            magnetLink: result.magnetLink
        });
    } catch (error) {
        console.error('[API] Error processing download request:', error);
        return json({
            error: `An error occurred while processing download request: ${error.message}`,
            success: false,
            debug: {
                error: error.toString(),
                stack: error.stack,
                message: error.message
            }
        }, { status: 500 });
    }
}
