/**
 * API endpoint for searching books via <PERSON>'s Archive
 */

import { json } from '@sveltejs/kit';
import { searchBooks } from '$lib/server/annas-archive-service';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        const query = url.searchParams.get('query');

        if (!query || query.trim().length < 3) {
            return json({
                error: 'Query must be at least 3 characters long',
                results: []
            }, { status: 400 });
        }

        console.log(`[API] Searching for books with query: ${query}`);

        // Use our Anna's Archive service to search for books
        const { results, error } = await searchBooks(query);

        if (error) {
            console.error('[API] Search error:', error);
            return json({
                error: `An error occurred during search: ${error}`,
                results: []
            }, { status: 500 });
        }

        if (!results || results.length === 0) {
            console.log('[API] No results found');
            return json({
                results: [],
                message: 'No results found'
            });
        }

        console.log(`[API] Found ${results.length} results`);
        return json({ results });
    } catch (error) {
        console.error('[API] Search error:', error);
        return json({
            error: `An error occurred during search: ${error.message}`,
            results: [],
            debug: {
                error: error.toString(),
                stack: error.stack,
                message: error.message
            }
        }, { status: 500 });
    }
}
