/**
 * API endpoint for getting book download links from <PERSON>'s Archive
 */

import { json } from '@sveltejs/kit';
import { getDownloadLinks } from '$lib/server/annas-archive-service';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        const md5 = url.searchParams.get('md5');

        if (!md5) {
            return json({
                error: 'Missing md5 parameter',
                downloadLinks: []
            }, { status: 400 });
        }

        console.log(`[API] Getting download links for book with MD5: ${md5}`);

        // Get download links
        const { title, author, coverImg, downloadLinks, error } = await getDownloadLinks(md5);

        if (error) {
            console.error('[API] Error getting download links:', error);
            return json({
                error: `An error occurred while getting download links: ${error}`,
                downloadLinks: []
            }, { status: 500 });
        }

        if (!downloadLinks || downloadLinks.length === 0) {
            console.log('[API] No download links found');
            return json({
                title,
                author,
                coverImg,
                error: 'No download links found',
                downloadLinks: []
            });
        }

        return json({
            title,
            author,
            coverImg,
            downloadLinks
        });
    } catch (error) {
        console.error('[API] Error getting download links:', error);
        return json({
            error: `An error occurred: ${error.message}`,
            downloadLinks: [],
            debug: {
                error: error.toString(),
                stack: error.stack,
                message: error.message
            }
        }, { status: 500 });
    }
}
