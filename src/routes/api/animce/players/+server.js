/**
 * API endpoint for getting episode players from docchi.pl
 */

import { json } from '@sveltejs/kit';
import { getPlayersList, getSkipTimes, getSeriesDetails } from '$lib/server/docchi-service';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        const slug = url.searchParams.get('slug');
        const episode = url.searchParams.get('episode');
        const skipInfo = url.searchParams.get('skipInfo') === 'true';

        if (!slug || !episode) {
            return json({
                error: 'Missing required parameters: slug and episode',
                players: []
            }, { status: 400 });
        }

        console.log(`[API] Getting players for anime ${slug}, episode ${episode}`);

        // Get players for the episode
        const { players, error: playersError } = await getPlayersList(slug, episode);

        if (playersError) {
            console.error('[API] Error getting players:', playersError);
            return json({
                error: `An error occurred while getting players: ${playersError}`,
                players: []
            }, { status: 500 });
        }

        // If skip info is requested, get the MAL ID and skip times
        let skipTimes = null;
        if (skipInfo) {
            // First get the series details to get the MAL ID
            const { details, error: detailsError } = await getSeriesDetails(slug);

            if (detailsError) {
                console.error('[API] Error getting series details for skip times:', detailsError);
            } else if (details.mal_id) {
                // Get skip times using the MAL ID
                const { skipTimes: times, error: skipError } = await getSkipTimes(details.mal_id, episode);

                if (skipError) {
                    console.error('[API] Error getting skip times:', skipError);
                } else {
                    skipTimes = times;
                }
            }
        }

        return json({
            players,
            skipTimes
        });
    } catch (error) {
        console.error('[API] Error getting episode players:', error);
        return json({
            error: `An error occurred: ${error.message}`,
            players: [],
            debug: {
                error: error.toString(),
                stack: error.stack,
                message: error.message
            }
        }, { status: 500 });
    }
}
