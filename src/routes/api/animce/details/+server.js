/**
 * API endpoint for getting anime details from docchi.pl
 */

import { json } from '@sveltejs/kit';
import { getSeriesDetails, getEpisodesCount } from '$lib/server/docchi-service';

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        const slug = url.searchParams.get('slug');

        if (!slug) {
            return json({
                error: 'Missing slug parameter',
                details: null
            }, { status: 400 });
        }

        console.log(`[API] Getting details for anime with slug: ${slug}`);

        // Get series details
        const { details, error: detailsError } = await getSeriesDetails(slug);

        if (detailsError) {
            console.error('[API] Error getting details:', detailsError);
            return json({
                error: `An error occurred while getting details: ${detailsError}`,
                details: null
            }, { status: 500 });
        }

        // Get episode count
        const { count, error: countError } = await getEpisodesCount(slug);

        if (countError) {
            console.error('[API] Error getting episode count:', countError);
            // Continue with details but note the error
            return json({
                details,
                episodeCount: null,
                error: `Could not get episode count: ${countError}`
            });
        }

        return json({
            details,
            episodeCount: count
        });
    } catch (error) {
        console.error('[API] Error getting anime details:', error);
        return json({
            error: `An error occurred: ${error.message}`,
            details: null,
            debug: {
                error: error.toString(),
                stack: error.stack,
                message: error.message
            }
        }, { status: 500 });
    }
}
