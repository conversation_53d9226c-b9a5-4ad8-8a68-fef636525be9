/**
 * GiantBomb API proxy route
 * Handles requests to the GiantBomb API to bypass CORS restrictions
 */

import { GIANTBOMB_CONFIG } from '$lib/config/apiConfig';

// Disable prerendering for this API route
export const prerender = false;

// Get API configuration from centralized config
const { API_KEY, BASE_URL: API_BASE_URL } = GIANTBOMB_CONFIG;

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
    try {
        // Get endpoint from query parameter
        const endpoint = url.searchParams.get('endpoint');
        
        if (!endpoint) {
            return new Response(JSON.stringify({
                error: 'Missing endpoint parameter'
            }), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        
        // Create URL to GiantBomb API
        const apiUrl = `${API_BASE_URL}/${endpoint}`;
        console.log(`[GiantBomb Proxy] Calling API: ${apiUrl}`);
        
        // Add format and API key to the URL
        const urlWithParams = new URL(apiUrl);
        urlWithParams.searchParams.append('api_key', API_KEY);
        urlWithParams.searchParams.append('format', 'json');
        
        // Copy all other query parameters from the original request
        for (const [key, value] of url.searchParams.entries()) {
            if (key !== 'endpoint') {
                urlWithParams.searchParams.append(key, value);
            }
        }
        
        // Make request to GiantBomb API
        const response = await fetch(urlWithParams.toString());
        
        // Check if response is OK
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[GiantBomb Proxy] API Error (${response.status}):`, errorText);
            
            return new Response(JSON.stringify({
                error: `GiantBomb API error: ${response.status}`,
                details: errorText
            }), {
                status: response.status,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        
        // Get response data
        const data = await response.json();
        
        // Return response
        return new Response(JSON.stringify(data), {
            headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error('[GiantBomb Proxy] Error:', error);
        
        return new Response(JSON.stringify({
            error: 'Internal server error',
            details: error.message
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}
