/**
 * OMDB API proxy route
 * Handles requests to the OMDB API to bypass CORS restrictions
 */

import { OMDB_CONFIG } from '$lib/config/apiConfig';

// Disable prerendering for this API route
export const prerender = false;

// Get API configuration from centralized config
const { API_KEY, BASE_URL: API_BASE_URL } = OMDB_CONFIG;

/**
 * Handle GET requests to the OMDB API
 */
export async function GET({ url }) {
    try {
        // During prerendering, return a mock response
        if (typeof url.searchParams === 'undefined') {
            return new Response(JSON.stringify({ prerendered: true, message: 'This is a prerendered response' }), {
                headers: { 'Content-Type': 'application/json' }
            });
        }

        // Get parameters from URL
        const title = url.searchParams.get('title');

        if (!title) {
            return new Response(JSON.stringify({ error: 'Missing title parameter' }), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }

        // Create URL to OMDB API
        const apiUrl = `${API_BASE_URL}?apikey=${API_KEY}&t=${encodeURIComponent(title)}`;
        console.log(`[OMDB Proxy] Calling API: ${apiUrl}`);

        // Make request to OMDB API
        const response = await fetch(apiUrl);

        // Check if response is OK
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[OMDB Proxy] API Error (${response.status}):`, errorText);

            return new Response(JSON.stringify({
                error: `OMDB API error: ${response.status}`,
                details: errorText
            }), {
                status: response.status,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }

        // Get data from response
        const data = await response.json();

        // Return response
        return new Response(JSON.stringify(data), {
            status: response.status,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error('[OMDB Proxy] Error:', error);

        return new Response(JSON.stringify({
            error: 'Internal server error',
            message: error.message
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}
