/**
 * API endpoint for extracting direct video URLs from various sources
 * Currently supports: CDA.pl
 */

import { json } from '@sveltejs/kit';
import { exec } from 'child_process';
import { promisify } from 'util';
import jwt from 'jsonwebtoken';
import { AUTH_CONFIG } from '$lib/config/authConfig';

// Convert exec to Promise-based
const execAsync = promisify(exec);

// Check if youtube-dl or yt-dlp is installed
let isYoutubeDlInstalled = false;
let isYtDlpInstalled = false;
let dlCommand = 'youtube-dl'; // Default command

async function checkDownloaders() {
    try {
        await execAsync('which youtube-dl');
        console.log('[Video Extract API] youtube-dl is installed');
        isYoutubeDlInstalled = true;
        dlCommand = 'youtube-dl';
    } catch (error) {
        console.error('[Video Extract API] youtube-dl is not installed:', error.message);
        isYoutubeDlInstalled = false;

        // Try yt-dlp as an alternative
        try {
            await execAsync('which yt-dlp');
            console.log('[Video Extract API] yt-dlp is installed');
            isYtDlpInstalled = true;
            dlCommand = 'yt-dlp';
        } catch (ytDlpError) {
            console.error('[Video Extract API] yt-dlp is not installed:', ytDlpError.message);
            isYtDlpInstalled = false;
        }
    }
}

// Check on module initialization
checkDownloaders();

// Disable prerendering for this API route
export const prerender = false;

/**
 * GET handler - Extract direct video URL from a source URL
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url, request }) {
    try {
        // This endpoint is now public, no authentication required
        console.log('[Video Extract API] Processing request');

        // Log all request headers for debugging
        console.log('[Video Extract API] Request headers:');
        for (const [key, value] of request.headers.entries()) {
            console.log(`  ${key}: ${key.toLowerCase() === 'authorization' ? 'Bearer [REDACTED]' : value}`);
        }

        const sourceUrl = url.searchParams.get('url');

        if (!sourceUrl) {
            return json({
                error: 'Missing URL parameter',
                directUrl: null
            }, { status: 400 });
        }

        console.log(`[Video Extract API] Extracting video URL from: ${sourceUrl}`);

        // Check if it's a CDA.pl URL
        if (sourceUrl.includes('cda.pl')) {
            return await handleCdaUrl(sourceUrl);
        }

        // Add support for other sources here in the future

        // If we don't support this source, return an error
        return json({
            error: 'Unsupported video source',
            directUrl: null
        }, { status: 400 });

    } catch (error) {
        console.error('[Video Extract API] Error:', error);
        return json({
            error: `An error occurred: ${error.message}`,
            directUrl: null
        }, { status: 500 });
    }
}

/**
 * Handle CDA.pl URL extraction
 * @param {string} cdaUrl - The CDA.pl URL
 * @returns {Promise<Response>} - JSON response with direct URL
 */
async function handleCdaUrl(cdaUrl) {
    try {
        // Check if any downloader is installed
        if (!isYoutubeDlInstalled && !isYtDlpInstalled) {
            // Try to install a downloader if not installed
            try {
                console.log('[Video Extract API] Attempting to install yt-dlp...');
                await execAsync('pip install yt-dlp');
                isYtDlpInstalled = true;
                dlCommand = 'yt-dlp';
                console.log('[Video Extract API] Successfully installed yt-dlp');
            } catch (ytDlpInstallError) {
                console.error('[Video Extract API] Failed to install yt-dlp:', ytDlpInstallError);

                try {
                    console.log('[Video Extract API] Attempting to install youtube-dl...');
                    await execAsync('pip install youtube-dl');
                    isYoutubeDlInstalled = true;
                    dlCommand = 'youtube-dl';
                    console.log('[Video Extract API] Successfully installed youtube-dl');
                } catch (installError) {
                    console.error('[Video Extract API] Failed to install youtube-dl:', installError);

                    // Return the original URL as fallback
                    return json({
                        error: 'No suitable downloader is installed and could not be installed automatically',
                        directUrl: null,
                        originalUrl: cdaUrl
                    });
                }
            }
        }

        // Use the appropriate downloader to extract the direct URL
        // The -g flag tells the downloader to just print the direct URL
        // The -f best flag tells the downloader to get the best quality
        console.log(`[Video Extract API] Using ${dlCommand} to extract URL`);
        const { stdout, stderr } = await execAsync(`${dlCommand} -g -f best "${cdaUrl}"`);

        if (stderr) {
            console.error(`[Video Extract API] ${dlCommand} stderr:`, stderr);
        }

        const directUrl = stdout.trim();

        if (!directUrl) {
            console.error(`[Video Extract API] No direct URL found in ${dlCommand} output`);

            // Return the original URL as fallback
            return json({
                error: 'Failed to extract direct URL',
                directUrl: null,
                originalUrl: cdaUrl
            });
        }

        console.log(`[Video Extract API] Successfully extracted direct URL from CDA.pl using ${dlCommand}`);

        // Check if it's an m3u8 stream
        const isHlsStream = directUrl.includes('.m3u8');

        return json({
            directUrl,
            isHlsStream
        });

    } catch (error) {
        console.error('[Video Extract API] Error extracting CDA.pl URL:', error);

        // Return the original URL as fallback
        return json({
            error: `Failed to extract CDA.pl URL: ${error.message}`,
            directUrl: null,
            originalUrl: cdaUrl
        });
    }
}
