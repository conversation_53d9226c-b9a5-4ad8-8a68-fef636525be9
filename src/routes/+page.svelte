<script lang="ts">
	import AddTorrent from '$lib/components/AddTorrent.svelte';
	import TorrentList from '$lib/components/TorrentList.svelte';
	import UnrestrictLinks from '$lib/components/UnrestrictLinks.svelte';
	import ZooSearch from '$lib/components/ZooSearch.svelte';
	import IsoRomSearch from '$lib/components/IsoRomSearch.svelte';
	import IsoRomSearch2 from '$lib/components/IsoRomSearch2.svelte';
	import SweechSearch from '$lib/components/SweechSearch.svelte';
	import WatchList from '$lib/components/WatchList.svelte';
	import CinemaPure from '$lib/components/CinemaPure.svelte';
	import Animce from '$lib/components/Animce.svelte';
	import Necromicon from '$lib/components/Necromicon.svelte';
	import GameCalendar from '$lib/components/GameCalendar.svelte';
	import TVMode from '$lib/components/TVMode.svelte';
	import LanguageSwitcher from '$lib/components/LanguageSwitcher.svelte';

	// Import Paraglide messages
	import * as m from '$lib/paraglide/messages';

	// Import icons from lucide-svelte
	import { PlusCircle, Download, Server, Search, Save, Gamepad, Eye, Film, Cat, Book, LogOut, Calendar } from 'lucide-svelte';
	import { authStore } from '$lib/stores/authStore';
	import { toastStore } from '$lib/stores/toastStore';

	// Function to handle logout
	function handleLogout() {
		authStore.logout();
		toastStore.add({
			type: 'success',
			message: 'Wylogowano pomyślnie!',
			duration: 3000
		});
		// Redirect to login page
		if (typeof window !== 'undefined') {
			window.location.href = '/login';
		}
	}

	// Stan aktywnej zakładki
	let activeTab = 'discover';
	// Zmienna do przechowywania magnet linku
	let magnetUrl = '';
	// Zmienna do przechowywania linków z hostingów
	let hostingLinks = '';
	// Zmienna do przechowywania pliku torrent dla przekazania do komponentu
	let droppedTorrentFile: File | null = null;
	// Stan przeciągania pliku
	let isPageDragging = false;
	// Liczba śledzonych elementów z nowymi wynikami
	let watchNewResultsCount = 0;

	// Funkcja zmieniająca aktywną zakładkę
	function setActiveTab(tab: string) {
		activeTab = tab;
	}

	// Expose setActiveTab to window for use in other components
	if (typeof window !== 'undefined') {
		(window as any).setActiveTab = setActiveTab;
	}

	// Funkcja do ustawienia magnet linku i przejścia do zakładki dodawania
	function setMagnetAndSwitchTab(magnetLink: string) {
		magnetUrl = magnetLink;
		setActiveTab('add');
	}

	// Funkcja do ustawienia linków z hostingów i przejścia do wybranej zakładki
	function switchTabWithLinks(tab: string, links: string) {
		hostingLinks = links;
		setActiveTab(tab);
	}

	// Obsługa przeciągania plików na całą stronę
	function handlePageDragOver(event: DragEvent) {
		event.preventDefault();
		event.stopPropagation();
		isPageDragging = true;
	}

	function handlePageDragLeave(event: DragEvent) {
		event.preventDefault();
		event.stopPropagation();

		// Sprawdzamy, czy kursor opuszcza stronę, a nie tylko przechodzi między elementami
		const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
		const x = event.clientX;
		const y = event.clientY;

		// Jeśli kursor jest poza granicami strony, ustawiamy isPageDragging na false
		if (x <= rect.left || x >= rect.right || y <= rect.top || y >= rect.bottom) {
			isPageDragging = false;
		}
	}

	function handlePageDragEnter(event: DragEvent) {
		event.preventDefault();
		event.stopPropagation();
		isPageDragging = true;
	}

	function handlePageDrop(event: DragEvent) {
		event.preventDefault();
		event.stopPropagation();

		const files = event.dataTransfer?.files;
		if (files && files.length > 0) {
			// Sprawdź czy plik ma rozszerzenie .torrent
			const file = files[0];
			if (file.name.endsWith('.torrent')) {
				// Zapisz plik do przekazania do komponentu AddTorrent
				droppedTorrentFile = file;

				// Dodaj małe opóźnienie, aby użytkownik zobaczył wizualne potwierdzenie upuszczenia pliku
				setTimeout(() => {
					// Ukryj komunikat o przeciąganiu
					isPageDragging = false;
					// Przejdź do zakładki dodawania torrenta
					setActiveTab('add');
				}, 300);
			} else {
				isPageDragging = false;
				if (typeof window !== 'undefined' && (window as any).showAlert) {
					(window as any).showAlert(m["modal.error"]() + ': ' + m["dropzone.message"](), 'error');
				}
			}
		} else {
			isPageDragging = false;
		}
	}
</script>

<div class="app-container {isPageDragging ? 'page-dragging' : ''}"
     on:dragover={handlePageDragOver}
     on:dragenter={handlePageDragEnter}
     on:dragleave={handlePageDragLeave}
     on:drop={handlePageDrop}
     role="region"
     aria-label="Strefa dodawania pliku torrent">

     {#if isPageDragging}
     <div class="page-dropzone-message">
        <p>{m["dropzone.message"]()}</p>
     </div>
     {/if}
	<!-- Menu boczne - widoczne tylko na desktopie -->
	<div class="p-5 pr-0 sidebar-container">
	<aside class="sidebar">
		<div class="sidebar-header">
			<div class="sidebar-logo">
				<img src="prosie4.png" alt="" class="mix-blend-lighten max-h-[96px]">
				<h1>ePiglet</h1>
				<h3 class="relative text-center -top-2">{m["app.name"]()}</h3>
			</div>
			<div class="language-switcher-container" style="display: none;">
				<LanguageSwitcher className="sidebar-language-switcher" />
			</div>
		</div>

		<nav>
			<ul class="nav-menu">
				<li class="nav-item">
					<button class="nav-link {activeTab === 'discover' ? 'active' : ''}" on:click={() => setActiveTab('discover')}>
						<Calendar size={18} strokeWidth={2} />
						<span>Kalendarz premier</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'add' ? 'active' : ''}" on:click={() => setActiveTab('add')}>
						<PlusCircle size={18} strokeWidth={2} />
						<span>{m["nav.addTorrent"]()}</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'hoster' ? 'active' : ''}" on:click={() => setActiveTab('hoster')}>
						<Server size={18} strokeWidth={2} />
						<span>{m["nav.addLink"]()}</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'downloads' ? 'active' : ''}" on:click={() => setActiveTab('downloads')}>
						<Download size={18} strokeWidth={2} />
						<span>{m["nav.downloads"]()}</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'watch' ? 'active' : ''}" on:click={() => setActiveTab('watch')}>
						<Eye size={18} strokeWidth={2} />
						<span>{m["nav.watch"]()}</span>
						{#if watchNewResultsCount > 0}
							<span class="nav-badge">{watchNewResultsCount}</span>
						{/if}
					</button>
				</li>
				<li class="nav-category">
					SZUKAJ GIER
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'zoo' ? 'active' : ''}" on:click={() => setActiveTab('zoo')}>
						<Search size={18} strokeWidth={2} />
						<span>Gry PC</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'isorom2' ? 'active' : ''}" on:click={() => setActiveTab('isorom2')}>
						<Save size={18} strokeWidth={2} />
						<span>Gry Konsole</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'sweech' ? 'active' : ''}" on:click={() => setActiveTab('sweech')}>
						<Gamepad size={18} strokeWidth={2} />
						<span>Gry Switch</span>
					</button>
				</li>

				<li class="nav-category nav-category-beta">
					<span>INNE</span>
					<span class="beta-badge">BETA</span>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'tv' ? 'active' : ''}" on:click={() => setActiveTab('tv')}>
						<Film size={18} strokeWidth={2} />
						<span>Widok TV (w budowie)</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'cinema' ? 'active' : ''}" on:click={() => setActiveTab('cinema')}>
						<Film size={18} strokeWidth={2} />
						<span>Czyste Kino</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'animce' ? 'active' : ''}" on:click={() => setActiveTab('animce')}>
						<Cat size={18} strokeWidth={2} />
						<span>Otaku</span>
					</button>
				</li>
				<li class="nav-item">
					<button class="nav-link {activeTab === 'necromicon' ? 'active' : ''}" on:click={() => setActiveTab('necromicon')}>
						<Book size={18} strokeWidth={2} />
						<span>Necromicon</span>
					</button>
				</li>

				<li class="mt-4 nav-item">
					<button class="nav-link logout-link" on:click={handleLogout}>
						<LogOut size={18} strokeWidth={2} />
						<span>Wyloguj się</span>
					</button>
				</li>
			</ul>
		</nav>
	</aside>
	</div>

	<!-- Mobilna nawigacja - widoczna tylko na urządzeniach mobilnych -->
	<nav class="mobile-nav">
		<div class="mobile-nav-menu">
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'add' ? 'active' : ''}"
					on:click={() => setActiveTab('add')}
					aria-label={m["nav.addTorrent"]()}
				>
					<PlusCircle size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'hoster' ? 'active' : ''}"
					on:click={() => setActiveTab('hoster')}
					aria-label={m["nav.addLink"]()}
				>
					<Server size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'downloads' ? 'active' : ''}"
					on:click={() => setActiveTab('downloads')}
					aria-label={m["nav.downloads"]()}
				>
					<Download size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'watch' ? 'active' : ''}"
					on:click={() => setActiveTab('watch')}
					aria-label={m["nav.watch"]()}
				>
					<Eye size={30} strokeWidth={1.8} />
					{#if watchNewResultsCount > 0}
						<span class="mobile-nav-badge">{watchNewResultsCount}</span>
					{/if}
				</button>
			</div>
			<div class="mobile-nav-divider"></div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'discover' ? 'active' : ''}"
					on:click={() => setActiveTab('discover')}
					aria-label="Kalendarz Premier"
				>
					<Calendar size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'zoo' ? 'active' : ''}"
					on:click={() => setActiveTab('zoo')}
					aria-label={m["nav.searchPC"]()}
				>
					<Search size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'isorom2' ? 'active' : ''}"
					on:click={() => setActiveTab('isorom2')}
					aria-label={m["nav.searchIsoRom2"]()}
				>
					<Save size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'sweech' ? 'active' : ''}"
					on:click={() => setActiveTab('sweech')}
					aria-label={m["nav.searchSwitch"]()}
				>
					<Gamepad size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'tv' ? 'active' : ''}"
					on:click={() => setActiveTab('tv')}
					aria-label="TV"
				>
					<Film size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'cinema' ? 'active' : ''}"
					on:click={() => setActiveTab('cinema')}
					aria-label={m["nav.cinema"]()}
				>
					<Film size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'animce' ? 'active' : ''}"
					on:click={() => setActiveTab('animce')}
					aria-label={m["nav.animce"]()}
				>
					<Cat size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link {activeTab === 'necromicon' ? 'active' : ''}"
					on:click={() => setActiveTab('necromicon')}
					aria-label="Necromicon"
				>
					<Book size={30} strokeWidth={1.8} />
				</button>
			</div>
			<div class="mobile-nav-divider"></div>
			<div class="mobile-nav-item">
				<button
					type="button"
					class="mobile-nav-link logout-link"
					on:click={handleLogout}
					aria-label="Wyloguj się"
				>
					<LogOut size={30} strokeWidth={1.8} />
				</button>
			</div>
		</div>
	</nav>

	<!-- Główna zawartość -->
	<main class="main-content">
		<!-- Zakładka Dodaj Torrent -->
		{#if activeTab === 'add'}
			<AddTorrent bind:magnetUrl={magnetUrl} switchTab={setActiveTab} droppedFile={droppedTorrentFile} />

		<!-- Zakładka Pobierania -->
		{:else if activeTab === 'downloads'}
			<TorrentList />
		<!-- Zakładka Hostingi -->
		{:else if activeTab === 'hoster'}
			<UnrestrictLinks initialLinks={hostingLinks} />
		<!-- Zakładka ZOO -->
		{:else if activeTab === 'zoo'}
			<ZooSearch setMagnetAndSwitchTab={setMagnetAndSwitchTab} />
		<!-- Zakładka ISO/ROM -->
		{:else if activeTab === 'isorom'}
			<IsoRomSearch setMagnetAndSwitchTab={setMagnetAndSwitchTab} />
		<!-- Zakładka ISO/ROM #2 -->
		{:else if activeTab === 'isorom2'}
			<IsoRomSearch2 switchTabWithLinks={switchTabWithLinks} />
		<!-- Zakładka Sweech -->
		{:else if activeTab === 'sweech'}
			<SweechSearch switchTab={setActiveTab} />
		<!-- Zakładka Watch -->
		{:else if activeTab === 'watch'}
			<WatchList switchTab={setActiveTab} bind:newResultsCount={watchNewResultsCount} />
		<!-- Zakładka Czyste Kino -->
		{:else if activeTab === 'cinema'}
			<CinemaPure setMagnetAndSwitchTab={setMagnetAndSwitchTab} />
		<!-- Zakładka Animce -->
		{:else if activeTab === 'animce'}
			<Animce />
		<!-- Zakładka Necromicon -->
		{:else if activeTab === 'necromicon'}
			<Necromicon />
		<!-- Zakładka Kalendarz premier -->
		{:else if activeTab === 'discover'}
			<GameCalendar />
		<!-- Zakładka TV -->
		{:else if activeTab === 'tv'}
			<TVMode />
		{/if}
	</main>
</div>

<style>
	.logout-link {
		color: #ef4444 !important;
		border-color: transparent !important;
	}

	.logout-link:hover {
		background-color: rgba(239, 68, 68, 0.1) !important;
		border-color: rgba(239, 68, 68, 0.2) !important;
	}

	.mobile-nav-link.logout-link {
		color: #ef4444 !important;
	}
</style>
