<script>
    import GameCalendar from '$lib/components/GameCalendar.svelte';
    import { authStore } from '$lib/stores/authStore';
    import { onMount } from 'svelte';

    // Check authentication on mount
    onMount(() => {
        // Check if token is expired
        authStore.checkExpiration();

        // If not authenticated, redirect to login
        if (!$authStore.isAuthenticated && typeof window !== 'undefined') {
            window.location.href = '/login';
        }
    });
</script>

<svelte:head>
    <title>Kalendarz premier | Prosiaczek Pobieraczek</title>
</svelte:head>

<div class="container mx-auto px-4 py-8">
    <GameCalendar />
</div>
