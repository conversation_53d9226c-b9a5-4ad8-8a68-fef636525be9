<script lang="ts">
	import '../app.css';
	import Modal from '$lib/components/Modal.svelte';
	import Toast from '$lib/components/Toast.svelte';
	import { modalStore } from '$lib/stores/modalStore';
	import { toastStore } from '$lib/stores/toastStore';
	import { authStore } from '$lib/stores/authStore';
	import * as m from '$lib/paraglide/messages';

	let { children } = $props();

	// Stan ładowania i sprawdzania autentykacji
	let isAuthChecking = $state(true);
	let currentPath = $state('');

	// Update current path when it changes
	$effect(() => {
		if (typeof window !== 'undefined') {
			currentPath = window.location.pathname;
		}
	});

	// Check if current route is login page
	$effect(() => {
		const isLoginPage = currentPath === '/login';

		// Sprawdź stan autentykacji
		authStore.checkExpiration();

		// Jeśli nie jesteśmy na stronie logowania i nie jesteśmy zalogowani, przekieruj na stronę logowania
		if (!isLoginPage && !$authStore.isAuthenticated) {
			if (typeof window !== 'undefined') {
				window.location.href = '/login';
			}
		}

		// Zakończ sprawdzanie autentykacji
		isAuthChecking = false;
	});

	// Funkcja do wyświetlania powiadomień
	function showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') {
		// Use the new toast system
		toastStore.add({
			type: type,
			message: message,
			duration: 5000,
			dismissible: true
		});
	}

	// Funkcja do wyświetlania alertów jako modali
	function showAlert(message: string, type: 'success' | 'error' | 'info' = 'error') {
		let title = m["modal.info"]();
		if (type === 'success') title = m["modal.success"]();
		else if (type === 'error') title = m["modal.error"]();

		// Show both modal and toast for important messages
		modalStore.alert(title, message);

		// Also show as toast for non-blocking notification
		toastStore.add({
			type: type,
			message: message,
			title: title,
			duration: 5000,
			dismissible: true
		});
	}

	// Funkcja do wyświetlania potwierdzeń jako modali
	function showConfirm(message: string, onConfirm: () => void) {
		modalStore.confirm(m["modal.confirm"](), message, onConfirm);
	}

	// Eksportujemy funkcje, aby były dostępne globalnie
	$effect(() => {
		if (typeof window !== 'undefined') {
			(window as any).showNotification = showNotification;
			(window as any).showAlert = showAlert;
			(window as any).showConfirm = showConfirm;

			// Dodaj globalny styl kursora świńskiego ryjka
			const style = document.createElement('style');
			style.textContent = `
				body {
					cursor: url('/pig-cursor.png'), auto;
				}
				button, input, textarea, select, a, .btn, .nav-link, .mobile-nav-link {
					cursor: url('/pig-cursor.png'), pointer !important;
				}
			`;
			document.head.appendChild(style);
		}
	});
</script>

{#if isAuthChecking && currentPath !== '/login'}
	<!-- Ekran ładowania podczas sprawdzania autentykacji -->
	<div class="auth-loading-screen">
		<div class="auth-loading-content">
			<img src="/prosie4.png" alt="ePiglet Logo" class="auth-loading-logo" />
			<div class="auth-loading-spinner"></div>
		</div>
	</div>
{:else}
	{@render children()}
{/if}

<!-- Legacy notification div (kept for backward compatibility) -->
<div id="notification" class="notification" style="display: none;"></div>

<!-- Toast notifications -->
<Toast position="top-right" max={5} />

<!-- Modal component -->
<Modal />

<style>
	.auth-loading-screen {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: var(--bg-color, #0f1115);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}

	.auth-loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1.5rem;
	}

	.auth-loading-logo {
		width: 120px;
		height: auto;
		opacity: 0.8;
	}

	.auth-loading-spinner {
		width: 40px;
		height: 40px;
		border: 3px solid rgba(255, 182, 193, 0.3);
		border-radius: 50%;
		border-top-color: var(--primary, #ffb6c1);
		animation: spin 1s ease-in-out infinite;
	}

	@keyframes spin {
		to {
			transform: rotate(360deg);
		}
	}
</style>
