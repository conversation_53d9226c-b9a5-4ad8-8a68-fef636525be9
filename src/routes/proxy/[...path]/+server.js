/**
 * Proxy server for Real-Debrid API
 * This endpoint forwards requests to the Real-Debrid API and handles CORS
 */

// Disable prerendering for this API route
export const prerender = false;

import { json } from '@sveltejs/kit';
import { REAL_DEBRID_CONFIG } from '$lib/config/apiConfig';

/**
 * Handle all HTTP methods (GET, POST, PUT, DELETE)
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ params, url, request }) {
    return handleProxyRequest(params, url, request);
}

export async function POST({ params, url, request }) {
    return handleProxyRequest(params, url, request);
}

export async function PUT({ params, url, request }) {
    return handleProxyRequest(params, url, request);
}

export async function DELETE({ params, url, request }) {
    return handleProxyRequest(params, url, request);
}

/**
 * Handle proxy request to Real-Debrid API
 * @param {Object} params Request parameters
 * @param {URL} url Request URL
 * @param {Request} request Original request
 * @returns {Response} Proxied response
 */
async function handleProxyRequest(params, url, request) {
    try {
        console.log(`[Proxy] Handling request for path: ${params.path}`);

        // Get the path from the params
        const path = params.path || '';

        // Build the target URL
        const targetUrl = new URL(path, 'https://api.real-debrid.com/rest/1.0/');
        console.log(`[Proxy] Target URL: ${targetUrl.toString()}`);

        // Copy query parameters
        url.searchParams.forEach((value, key) => {
            targetUrl.searchParams.append(key, value);
        });

        // Prepare headers
        const headers = new Headers();

        // Copy relevant headers from the original request
        for (const [key, value] of request.headers.entries()) {
            // Skip headers that shouldn't be forwarded
            if (!['host', 'origin', 'referer', 'authorization'].includes(key.toLowerCase())) {
                headers.append(key, value);
            }
        }

        // Always use Real-Debrid API key for authorization
        headers.set('Authorization', `Bearer ${REAL_DEBRID_CONFIG.API_KEY}`);

        console.log(`[Proxy] Request method: ${request.method}`);
        console.log(`[Proxy] Request headers:`, Object.fromEntries([...headers.entries()]));

        // Create fetch options
        const fetchOptions = {
            method: request.method,
            headers: headers,
            redirect: 'follow'
        };

        // Add body for non-GET requests
        if (request.method !== 'GET' && request.method !== 'HEAD') {
            const contentType = request.headers.get('content-type');

            if (contentType && contentType.includes('application/json')) {
                // Handle JSON body
                const body = await request.json();
                fetchOptions.body = JSON.stringify(body);
            } else {
                // Handle other body types
                const body = await request.text();
                if (body) {
                    fetchOptions.body = body;
                }
            }
        }

        // Make the request to Real-Debrid API
        console.log(`[Proxy] Sending request to: ${targetUrl.toString()}`);
        const response = await fetch(targetUrl.toString(), fetchOptions);
        console.log(`[Proxy] Response status: ${response.status}`);

        // Prepare response headers
        const responseHeaders = new Headers();

        // Add CORS headers
        responseHeaders.append('Access-Control-Allow-Origin', '*');
        responseHeaders.append('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        responseHeaders.append('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        // Copy relevant headers from the API response
        for (const [key, value] of response.headers.entries()) {
            // Skip headers that might cause issues
            if (!['content-encoding', 'content-length', 'connection', 'transfer-encoding'].includes(key.toLowerCase())) {
                responseHeaders.append(key, value);
            }
        }

        // Log response headers
        console.log(`[Proxy] Response headers:`, Object.fromEntries([...responseHeaders.entries()]));

        // Check if response is OK
        if (!response.ok) {
            console.error(`[Proxy] Error response from API: ${response.status} ${response.statusText}`);
            const errorText = await response.text();
            console.error(`[Proxy] Error details: ${errorText}`);

            // Return error response
            return json({
                error: `API Error: ${response.status} ${response.statusText}`,
                details: errorText
            }, {
                status: response.status,
                headers: responseHeaders
            });
        }

        // Get response body
        let responseBody;
        const contentType = response.headers.get('content-type');
        console.log(`[Proxy] Response content type: ${contentType}`);

        try {
            if (contentType && contentType.includes('application/json')) {
                // Get the response text first to check if it's empty
                const responseText = await response.text();
                console.log(`[Proxy] Raw response: ${responseText}`);

                // Only parse as JSON if there's actual content
                if (responseText && responseText.trim()) {
                    try {
                        responseBody = JSON.parse(responseText);
                        console.log(`[Proxy] JSON response parsed successfully`);
                    } catch (jsonError) {
                        console.error(`[Proxy] Error parsing JSON: ${jsonError.message}`);
                        console.error(`[Proxy] Raw response that failed to parse: ${responseText}`);

                        // Return the raw text if JSON parsing fails
                        return new Response(responseText, {
                            status: response.status,
                            headers: responseHeaders
                        });
                    }
                } else {
                    console.log(`[Proxy] Empty response received`);
                    responseBody = {};
                }

                // For status 204 (No Content), return empty response
                if (response.status === 204) {
                    return new Response(null, {
                        status: 204,
                        headers: responseHeaders
                    });
                }

                // Return JSON response
                return json(responseBody || {}, {
                    status: response.status,
                    headers: responseHeaders
                });
            } else {
                // Handle other response types
                responseBody = await response.text();
                console.log(`[Proxy] Text response received: ${responseBody ? responseBody.substring(0, 100) + '...' : 'empty'}`);

                // Return text response
                return new Response(responseBody || '', {
                    status: response.status,
                    headers: responseHeaders
                });
            }
        } catch (bodyError) {
            console.error(`[Proxy] Error processing response body: ${bodyError.message}`);
            console.error(`[Proxy] Error stack: ${bodyError.stack}`);

            // Return error response
            return json({
                error: 'Error processing response',
                message: bodyError.message,
                stack: bodyError.stack
            }, {
                status: 500,
                headers: responseHeaders
            });
        }
    } catch (error) {
        console.error('[Proxy] Error in proxy handler:', error);
        console.error('[Proxy] Error stack:', error.stack);

        // Return detailed error response
        return json({
            error: 'Proxy error',
            message: error.message,
            stack: error.stack,
            path: params.path,
            url: url.toString(),
            method: request.method
        }, {
            status: 500,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            }
        });
    }
}

/**
 * Handle OPTIONS requests for CORS preflight
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function OPTIONS() {
    return new Response(null, {
        status: 204,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '86400' // 24 hours
        }
    });
}
