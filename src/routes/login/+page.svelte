<script lang="ts">
    import { goto } from '$app/navigation';
    import { authStore } from '$lib/stores/authStore';
    import { onMount } from 'svelte';
    import { Lock, User } from 'lucide-svelte';
    import { toastStore } from '$lib/stores/toastStore';

    // Form data
    let username = $state('');
    let password = $state('');
    let isLoading = $state(false);
    let error = $state('');
    let isCheckingAuth = $state(true);

    // Check if already authenticated
    onMount(() => {
        // Sprawdź stan autentykacji
        authStore.checkExpiration();

        // Je<PERSON><PERSON> już zalogowany, przekieruj na stronę główną
        if ($authStore.isAuthenticated) {
            goto('/');
        }

        // Zakończ sprawdzanie autentykacji
        isCheckingAuth = false;
    });

    // Handle form submission
    async function handleLogin() {
        error = '';
        isLoading = true;

        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok && data.token) {
                // Login successful
                authStore.login(data.token, data.expiresIn);
                toastStore.add({
                    type: 'success',
                    message: 'Zalogowano pomyślnie!',
                    duration: 3000
                });
                goto('/');
            } else {
                // Login failed
                error = data.message || 'Nieprawidłowa nazwa użytkownika lub hasło';
                toastStore.add({
                    type: 'error',
                    message: error,
                    duration: 5000
                });
            }
        } catch (err) {
            console.error('Login error:', err);
            error = 'Wystąpił błąd podczas logowania. Spróbuj ponownie.';
            toastStore.add({
                type: 'error',
                message: error,
                duration: 5000
            });
        } finally {
            isLoading = false;
        }
    }
</script>

{#if isCheckingAuth}
<!-- Ekran ładowania podczas sprawdzania autentykacji -->
<div class="login-container">
    <div class="auth-loading-content">
        <img src="/prosie4.png" alt="ePiglet Logo" class="auth-loading-logo" />
        <div class="auth-loading-spinner"></div>
    </div>
</div>
{:else}
<div class="login-container">
    <div class="bg-image"></div>
    <div class="login-card">
        <div class="inner">
            <div class="login-header">
                <img src="/prosie4.png" alt="ePiglet Logo" class="login-logo mix-blend-lighten">
                <h1>ePiglet</h1>
                <h3>Prosiaczek Pobieraczek</h3>
            </div>

            <form onsubmit={(e) => { e.preventDefault(); handleLogin(); }} class="login-form">
                {#if error}
                    <div class="error-message">{error}</div>
                {/if}

                <div class="form-group">
                    <label for="username">
                        <User size={18} />
                        <span>Nazwa użytkownika</span>
                    </label>
                    <input
                        type="text"
                        id="username"
                        bind:value={username}
                        placeholder="Wprowadź nazwę użytkownika"
                        required
                        autocomplete="username"
                        disabled={isLoading}
                    />
                </div>

                <div class="form-group">
                    <label for="password">
                        <Lock size={18} />
                        <span>Hasło</span>
                    </label>
                    <input
                        type="password"
                        id="password"
                        bind:value={password}
                        placeholder="Wprowadź hasło"
                        required
                        autocomplete="current-password"
                        disabled={isLoading}
                    />
                </div>

                <button
                    type="submit"
                    class="login-button"
                    disabled={isLoading || !username || !password}
                >
                    {isLoading ? 'Logowanie...' : 'Zaloguj się'}
                </button>
            </form>
        </div>
    </div>
</div>
{/if}

<style>
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: var(--bg-color, #0f1115);
        padding: 1rem;
        position: relative;
        overflow: hidden;
    }

    /* Animowane tło ze zdjęciem gry */
    .login-container::before {
        content: "";
        position: fixed; /* Użyj fixed zamiast absolute */
        top: -10%;
        left: -10%;
        width: 120%;
        height: 120%;
        background-image: url('https://images8.alphacoders.com/136/thumb-1920-1366759.jpeg');
        background-size: cover;
        background-position: center;
        opacity: 1; /* Stała widoczność - zostawiamy 0.2 bo 1.0 byłoby za mocne */
        z-index: 0;
        animation: moveBackground 20s infinite linear !important;
    }


    @keyframes fadeInOut {
        0%, 100% { opacity: 0; }
        25%, 75% { opacity: 0.2; }
    }

    @keyframes fadeInOutReverse {
        0%, 100% { opacity: 0.2; }
        25%, 75% { opacity: 0; }
    }

    /* Przyciemnienie tła dla lepszej widoczności karty */
    .login-container::after {
        content: "";
        position: fixed; /* Użyj fixed zamiast absolute */
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 0;
    }

    @keyframes moveBackground {
        0% {
            transform: translateX(-5%);
        }
        100% {
            transform: translateX(5%);
        }
    }

    .login-card {
        position: relative;
        width: 100%;
        max-width: 400px;
        border-radius: 12px;
        padding: 1px; /* Przestrzeń na border - 1px */
        background: linear-gradient(90deg, #2a2e35, #555, #2a2e35);
        background-size: 200% 100%;
        animation: moveGradient 4s linear infinite;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
        z-index: 1; /* Upewniamy się, że karta jest nad tłem */
    }

    /* Wewnętrzna karta z zawartością */
    .login-card .inner {
        background-color: var(--card-bg, #1a1d24);
        border-radius: 11px;
        padding: 2rem;
        width: 100%;
        height: 100%;
    }

    /* Animacja przesuwającego się gradientu */
    @keyframes moveGradient {
        0% {
            background-position: 0% 50%;
        }
        100% {
            background-position: 200% 50%;
        }
    }

    .login-header {
        flex-direction: column;
        display: flex;
        align-items: center;
        text-align: center;
        margin-bottom: 2rem;
    }

    .login-logo {
        width: 100px;
        height: auto;
        margin-bottom: 0.5rem;
    }

    .login-header h1 {
        font-size: 1.8rem;
        color: var(--text-color, #ffffff);
        margin: 0;
    }

    .login-header h3 {
        font-size: 1rem;
        color: var(--text-secondary, #a0a0a0);
        margin: 0;
        position: relative;
        top: -0.25rem;
    }

    .login-form {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-group label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary, #a0a0a0);
        font-size: 0.9rem;
    }

    .form-group input {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        border: 1px solid var(--border, #2a2e35);
        background-color: var(--input-bg, #15181c);
        color: var(--text-color, #ffffff);
        font-size: 1rem;
        transition: border-color 0.2s ease;
    }

    .form-group input:focus {
        outline: none;
        border-color: var(--primary, #ff69b4);
    }

    .login-button {
        padding: 0.75rem 1rem;
        border-radius: 8px;
        border: none;
        background-color: var(--primary, #ff69b4);
        color: #000;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s ease;
        margin-top: 0.5rem;
    }

    .login-button:hover:not(:disabled) {
        background-color: var(--primary-dark, #e05aa0);
    }

    .login-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .error-message {
        background-color: rgba(220, 38, 38, 0.1);
        color: #ef4444;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        border-left: 3px solid #ef4444;
        font-size: 0.9rem;
    }

    /* Style dla ekranu ładowania */
    .auth-loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .auth-loading-logo {
        width: 120px;
        height: auto;
        opacity: 0.8;
    }

    .auth-loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 182, 193, 0.3);
        border-radius: 50%;
        border-top-color: var(--primary, #ffb6c1);
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
</style>
