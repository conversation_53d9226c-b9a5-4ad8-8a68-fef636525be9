// Funkcja do aktualizacji ścieżki napisów w odtwar<PERSON>zu
function updateSubtitlesTrack(url) {
    if (typeof window !== 'undefined') {
        const video = document.getElementById('videoPlayer');
        if (video) {
            // Funkcja do dodawania nowej ścieżki napisów
            const addTrackElement = (trackSrc) => {
                // Usuń istniejące ścieżki napisów
                while (video.textTracks.length > 0) {
                    const track = video.querySelector('track');
                    if (track) {
                        video.removeChild(track);
                    } else {
                        break;
                    }
                }
                
                if (trackSrc) {
                    // Dodaj nową ścieżkę napisów
                    const track = document.createElement('track');
                    track.kind = 'captions';
                    track.label = 'Polski';
                    track.srclang = 'pl';
                    track.src = trackSrc;
                    track.default = true;
                    
                    video.appendChild(track);
                    
                    // Włącz napisy
                    setTimeout(() => {
                        if (video.textTracks && video.textTracks.length > 0) {
                            video.textTracks[0].mode = 'showing';
                        }
                    }, 500);
                    
                    // Pokaż powiadomienie
                    if (typeof window !== 'undefined' && window.showNotification) {
                        window.showNotification('Napisy zostały załadowane', 'success');
                    }
                }
            };
            
            // Sprawdź, czy URL zaczyna się od /api/subtitles/proxy
            // Jeśli tak, przekieruj do publicznego endpointu
            if (url.startsWith('/api/subtitles/proxy')) {
                console.log('Converting proxy URL to public endpoint');
                
                // Pobierz oryginalny URL z parametru zapytania
                const originalUrl = new URL(url, window.location.origin).searchParams.get('url');
                if (originalUrl) {
                    // Użyj publicznego endpointu do pobrania napisów
                    const downloadUrl = `/api/subtitles/download?url=${encodeURIComponent(originalUrl)}`;
                    
                    // Pobierz napisy asynchronicznie
                    (async () => {
                        try {
                            const response = await fetch(downloadUrl);
                            if (response.ok) {
                                const data = await response.json();
                                if (data && data.url) {
                                    // Pobierz nazwę pliku z URL
                                    const fileName = data.url.split('/').pop();
                                    
                                    // Ustaw URL do publicznego endpointu
                                    const publicUrl = `/publicapi/subtitles/${fileName}`;
                                    console.log(`Switching to public subtitle endpoint: ${publicUrl}`);
                                    
                                    // Aktualizuj ścieżkę napisów
                                    addTrackElement(publicUrl);
                                }
                            }
                        } catch (error) {
                            console.error('Error fetching subtitles for track:', error);
                        }
                    })();
                }
            } else if (url) {
                // Użyj bezpośrednio podanego URL
                addTrackElement(url);
            }
        }
    }
}
