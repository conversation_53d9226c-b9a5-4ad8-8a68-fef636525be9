import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';

/**
 * Publiczny endpoint do serwowania plików napisów
 * Nie wymaga autoryzacji, aby element <track> mógł załadować napisy
 */
export async function GET({ params }) {
    try {
        const { id } = params;
        
        // Sprawdź, czy ID jest prawidłowe (tylko litery, cyfry i znaki -._ są dozwolone)
        if (!id || !/^[a-zA-Z0-9\-._]+\.vtt$/.test(id)) {
            throw error(400, 'Invalid subtitle ID');
        }
        
        // Ścieżka do pliku napisów
        const subtitlesDir = path.join(process.cwd(), 'static', 'subtitles');
        const filePath = path.join(subtitlesDir, id);
        
        // Sprawdź, czy plik istnieje
        if (!fs.existsSync(filePath)) {
            console.error(`[PublicAPI] Subtitle file not found: ${filePath}`);
            throw error(404, 'Subtitle file not found');
        }
        
        // Odczytaj plik
        const fileContent = fs.readFileSync(filePath, 'utf-8');
        
        // Zwróć plik jako WebVTT
        return new Response(fileContent, {
            headers: {
                'Content-Type': 'text/vtt',
                'Cache-Control': 'public, max-age=3600', // Cache na 1 godzinę
                'Access-Control-Allow-Origin': '*' // Pozwól na dostęp z dowolnej domeny
            }
        });
    } catch (err) {
        console.error(`[PublicAPI] Error serving subtitle:`, err);
        throw error(500, 'Internal server error');
    }
}
