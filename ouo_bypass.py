import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse

def recaptcha_v3():
    """Bypass reCAPTCHA v3 for ouo.io links"""
    ANCHOR_URL = 'https://www.google.com/recaptcha/api2/anchor?ar=1&k=6Lcr1ncUAAAAAH3cghg6cOTPGARa8adOf-y9zv2x&co=aHR0cHM6Ly9vdW8ucHJlc3M6NDQz&hl=en&v=pCoGBhjs9s8EhFOHJFe8cqis&size=invisible&cb=ahgyd1gkfkhe'
    url_base = 'https://www.google.com/recaptcha/'
    post_data = "v={}&reason=q&c={}&k={}&co={}"
    
    client = requests.Session()
    client.headers.update({
        'content-type': 'application/x-www-form-urlencoded'
    })
    
    matches = re.findall('(\[api2|enterprise\]+)\\/anchor\\?(.*)', ANCHOR_URL)[0]
    url_base += matches[0]+'/'
    params = matches[1]
    
    res = client.get(url_base+'anchor', params=params)
    token = re.findall(r'"recaptcha-token" value="(.*?)"', res.text)[0]
    
    params = dict(pair.split('=') for pair in params.split('&'))
    post_data = post_data.format(params["v"], token, params["k"], params["co"])
    
    res = client.post(url_base+'reload', params=f'k={params["k"]}', data=post_data)
    answer = re.findall(r'"rresp","(.*?)"', res.text)[0]
    
    return answer

def bypass_ouo_io(url):
    """Bypass ouo.io links to get the actual download link"""
    try:
        print(f"Bypassing ouo.io link: {url}")
        
        # Create a session with browser-like headers
        client = requests.Session()
        client.headers.update({
            'authority': 'ouo.io',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8',
            'cache-control': 'max-age=0',
            'referer': 'http://www.google.com/ig/adde?moduleurl=',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
        })
        
        # Normalize URL (convert ouo.press to ouo.io if needed)
        tempurl = url.replace("ouo.press", "ouo.io")
        p = urlparse(tempurl)
        id = tempurl.split('/')[-1]
        
        # First request to get the initial page
        res = client.get(tempurl)
        
        # Prepare for the next step
        next_url = f"{p.scheme}://{p.hostname}/go/{id}"
        
        # Go through the ouo.io steps (usually 1-2 steps)
        for _ in range(2):
            if res.headers.get('Location'): 
                break
                
            # Parse the page to get the form data
            bs4 = BeautifulSoup(res.content, 'html.parser')
            if not bs4.form:
                print("Could not find form in the page")
                return url
                
            inputs = bs4.form.findAll("input", {"name": re.compile(r"token$")})
            data = {input.get('name'): input.get('value') for input in inputs}
            
            # Add reCAPTCHA token
            data['x-token'] = recaptcha_v3()
            
            # Submit the form
            h = {
                'content-type': 'application/x-www-form-urlencoded'
            }
            res = client.post(next_url, data=data, headers=h, allow_redirects=False)
            
            # Update URL for the next step (if needed)
            next_url = f"{p.scheme}://{p.hostname}/xreallcygo/{id}"
        
        # Get the final URL from the Location header
        final_url = res.headers.get('Location')
        
        if final_url:
            print(f"Successfully bypassed ouo.io link: {url} -> {final_url}")
            return final_url
        else:
            print(f"Failed to bypass ouo.io link: {url}")
            return url
            
    except Exception as e:
        print(f"Error bypassing ouo.io link: {str(e)}")
        return url

# Example usage
if __name__ == "__main__":
    test_url = "https://ouo.io/RNMAV4"
    result = bypass_ouo_io(test_url)
    print(f"Original URL: {test_url}")
    print(f"Bypassed URL: {result}")
