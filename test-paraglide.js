// Simple test script to check if paraglide is working properly
import { deLocalizeUrl } from './src/lib/paraglide/runtime.js';

try {
  console.log('Testing paraglide deLocalizeUrl function');
  const testUrl = new URL('https://prosiaczek.pogorzelski.ovh/en/some-path');
  const result = deLocalizeUrl(testUrl);
  console.log('Result:', result);
} catch (error) {
  console.error('Error testing paraglide:', error);
}
