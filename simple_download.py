import sys
import json
import requests
from bs4 import BeautifulSoup

def get_game_info(url):
    """Get information about a game from its URL"""
    try:
        print(f"Fetching page: {url}", file=sys.stderr)
        response = requests.get(url, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Get title
        title = "Unknown Game"
        title_elem = soup.find('h1', class_='entry-title')
        if title_elem:
            title = title_elem.text.strip()
        
        # Get content
        content = soup.find('div', class_='entry-content')
        if not content:
            return {
                "success": False,
                "message": "Could not find game content",
                "title": title
            }
        
        # Find download links
        download_links = []
        download_sites = ['1fichier', 'mediafire', 'mega', 'uptobox', 'zippy', 'zippyshare',
                         'rapidgator', 'uploaded', 'uploadgig', 'nitroflare', 'turbobit',
                         'filefactory', 'katfile', 'alfafile', 'hitfile', 'upload']
        
        for a in content.find_all('a'):
            if 'href' in a.attrs:
                link = a['href']
                text = a.text.strip()
                
                if any(site in link.lower() for site in download_sites):
                    download_links.append({
                        'url': link,
                        'text': text
                    })
        
        return {
            "success": True,
            "title": title,
            "links": download_links,
            "links_count": len(download_links)
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Error: {str(e)}"
        }

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(json.dumps({"success": False, "message": "No URL provided"}))
        sys.exit(1)
    
    url = sys.argv[1]
    result = get_game_info(url)
    print(json.dumps(result))
