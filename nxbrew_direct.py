import sys
import json
import traceback
from urllib.parse import urlparse
import requests
from bs4 import BeautifulSoup

# Import ouo.io bypass function from the local file
try:
    from bypass_ouo import bypass_ouo
    HAVE_OUO_BYPASS = True
    print("bypass_ouo module found. ouo.io links will be bypassed.", file=sys.stderr)
except ImportError:
    print("bypass_ouo module not found. ouo.io links will not be bypassed.", file=sys.stderr)
    HAVE_OUO_BYPASS = False

def get_html_page(url):
    """Get an HTML page as a soup"""
    try:
        print(f"Fetching page: {url}", file=sys.stderr)
        response = requests.get(url, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        response.raise_for_status()
        print(f"Successfully fetched page ({len(response.text)} bytes)", file=sys.stderr)
        return BeautifulSoup(response.text, 'html.parser')
    except Exception as e:
        print(f"Error fetching page: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        return None

def get_languages(soup):
    """Parse languages from a soup"""
    if not soup:
        return []

    languages = []
    content = soup.find('div', class_='entry-content')
    if content:
        text = content.get_text()
        # Simple language detection - can be improved
        lang_patterns = {
            'English': ['english', 'eng'],
            'Japanese': ['japanese', 'jap'],
            'French': ['french', 'fra'],
            'German': ['german', 'ger'],
            'Spanish': ['spanish', 'spa'],
            'Italian': ['italian', 'ita'],
            'Dutch': ['dutch'],
            'Portuguese': ['portuguese', 'por'],
            'Russian': ['russian', 'rus'],
            'Korean': ['korean', 'kor'],
            'Chinese': ['chinese', 'chi']
        }

        for lang, patterns in lang_patterns.items():
            for pattern in patterns:
                if pattern.lower() in text.lower():
                    languages.append(lang)
                    break

    return languages

def get_download_links(soup, config):
    """Extract download links from the page, focusing on 1fichier links"""
    if not soup:
        return []

    content = soup.find('div', class_='entry-content')
    if not content:
        print("Could not find entry-content div", file=sys.stderr)
        # Try alternative class names that might be used
        content = soup.find('div', class_='post-content')
        if not content:
            content = soup.find('article')
            if not content:
                print("Could not find any content div", file=sys.stderr)
                return []
            else:
                print("Found article element instead", file=sys.stderr)
        else:
            print("Found post-content div instead", file=sys.stderr)

    print("Searching for download links...", file=sys.stderr)

    # Collect all links first
    raw_links = []
    seen_urls = set()  # To track unique URLs

    # Find DLC and Update sections
    dlc_sections = []
    update_sections = []

    # Look for wp-block-columns which often contain DLC and Update sections
    columns = content.find_all('div', class_='wp-block-columns')
    for column in columns:
        # Check if this is a DLC section
        if column.find(text=lambda t: t and ('dlc' in t.lower() or 'pack' in t.lower())):
            dlc_sections.append(column)
            print(f"Found DLC section", file=sys.stderr)
        # Check if this is an Update section
        elif column.find(text=lambda t: t and ('update' in t.lower() or 'patch' in t.lower())):
            update_sections.append(column)
            print(f"Found Update section", file=sys.stderr)

    # Process DLC sections
    for section in dlc_sections:
        a_tags = section.find_all('a')
        for a in a_tags:
            if 'href' in a.attrs and '1fichier' in a.text.lower():
                link = a['href']
                text = a.get_text().strip()

                # Handle ouo.io links if the bypass module is available
                if ('ouo.io' in link.lower() or 'ouo.press' in link.lower()) and HAVE_OUO_BYPASS:
                    try:
                        bypassed_link = bypass_ouo(link)
                        if bypassed_link and bypassed_link != link:
                            print(f"Bypassed ouo.io link for DLC: {link} -> {bypassed_link}", file=sys.stderr)
                            link = bypassed_link
                    except Exception as e:
                        print(f"Error bypassing ouo.io link: {str(e)}", file=sys.stderr)

                # Skip duplicate URLs
                if link in seen_urls:
                    continue

                seen_urls.add(link)
                raw_links.append({
                    'url': link,
                    'text': text.lower(),
                    'category': 'DLC'
                })
                print(f"Found DLC link: {text} -> {link}", file=sys.stderr)

    # Process Update sections
    for section in update_sections:
        a_tags = section.find_all('a')
        for a in a_tags:
            if 'href' in a.attrs and '1fichier' in a.text.lower():
                link = a['href']
                text = a.get_text().strip()

                # Handle ouo.io links if the bypass module is available
                if ('ouo.io' in link.lower() or 'ouo.press' in link.lower()) and HAVE_OUO_BYPASS:
                    try:
                        bypassed_link = bypass_ouo(link)
                        if bypassed_link and bypassed_link != link:
                            print(f"Bypassed ouo.io link for Update: {link} -> {bypassed_link}", file=sys.stderr)
                            link = bypassed_link
                    except Exception as e:
                        print(f"Error bypassing ouo.io link: {str(e)}", file=sys.stderr)

                # Skip duplicate URLs
                if link in seen_urls:
                    continue

                seen_urls.add(link)
                raw_links.append({
                    'url': link,
                    'text': text.lower(),
                    'category': 'Update'
                })
                print(f"Found Update link: {text} -> {link}", file=sys.stderr)

    # Find all paragraphs that might contain 1fichier links
    paragraphs = content.find_all('p')
    for p in paragraphs:
        # Check if this paragraph mentions 1fichier
        if p.find(text=lambda t: t and '1fichier' in t.lower()):
            a_tags = p.find_all('a')
            for a in a_tags:
                if 'href' in a.attrs:
                    link = a['href']
                    text = a.get_text().strip()

                    # Handle ouo.io links if the bypass module is available
                    if ('ouo.io' in link.lower() or 'ouo.press' in link.lower()) and HAVE_OUO_BYPASS:
                        try:
                            bypassed_link = bypass_ouo(link)
                            if bypassed_link and bypassed_link != link:
                                print(f"Bypassed ouo.io link: {link} -> {bypassed_link}", file=sys.stderr)
                                link = bypassed_link
                        except Exception as e:
                            print(f"Error bypassing ouo.io link: {str(e)}", file=sys.stderr)

                    # Skip duplicate URLs
                    if link in seen_urls:
                        continue

                    seen_urls.add(link)
                    # Assume it's a base game link if not already categorized
                    raw_links.append({
                        'url': link,
                        'text': text.lower(),
                        'category': 'Base Game'
                    })
                    print(f"Found Base Game link: {text} -> {link}", file=sys.stderr)

    # If we still don't have any 1fichier links, try a more general approach
    if not raw_links:
        # Find all links in the content
        a_tags = content.find_all('a')
        for a in a_tags:
            if 'href' in a.attrs:
                link = a['href']
                text = a.get_text().strip()

                # Skip image files and other non-downloadable content
                if any(ext in link.lower() for ext in ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg']):
                    continue

                # Only process 1fichier links or links that might lead to 1fichier
                if '1fichier.com' in link.lower() or ('ouo.io' in link.lower() and '1fichier' in text.lower()):
                    print(f"Found potential 1fichier link: {text} -> {link}", file=sys.stderr)

                    # Handle ouo.io links if the bypass module is available
                    if ('ouo.io' in link.lower() or 'ouo.press' in link.lower()) and HAVE_OUO_BYPASS:
                        try:
                            bypassed_link = bypass_ouo(link)
                            if bypassed_link and bypassed_link != link:
                                print(f"Bypassed ouo.io link: {link} -> {bypassed_link}", file=sys.stderr)
                                link = bypassed_link
                        except Exception as e:
                            print(f"Error bypassing ouo.io link: {str(e)}", file=sys.stderr)

                    # Skip duplicate URLs
                    if link in seen_urls:
                        print(f"Skipping duplicate link: {link}", file=sys.stderr)
                        continue

                    seen_urls.add(link)
                    raw_links.append({
                        'url': link,
                        'text': text.lower()
                    })

    print(f"Total raw links found: {len(raw_links)}", file=sys.stderr)

    # Categorize links that don't already have a category
    base_game_links = []
    update_links = []
    dlc_links = []

    for link in raw_links:
        # If the link already has a category, add it to the appropriate list
        if 'category' in link:
            if link['category'] == 'Update':
                update_links.append(link)
            elif link['category'] == 'DLC':
                dlc_links.append(link)
            else:  # Base Game or unknown
                base_game_links.append(link)
        else:
            # Otherwise, categorize based on URL and text
            url = link['url'].lower()
            text = link['text'].lower()

            # Check both URL and text for categorization
            # Check if it's an update
            if ('update' in url or 'patch' in url or
                'update' in text or 'patch' in text or
                'v1.' in url or 'v2.' in url or 'v3.' in url or
                '1.0.' in url or '2.0.' in url or '3.0.' in url):
                link['category'] = 'Update'
                update_links.append(link)
            # Check if it's DLC
            elif ('dlc' in url or 'addon' in url or 'add-on' in url or 'expansion' in url or
                  'dlc' in text or 'addon' in text or 'add-on' in text or 'expansion' in text or
                  'season pass' in text):
                link['category'] = 'DLC'
                dlc_links.append(link)
            # Check for base game indicators
            elif ('base' in url or 'base_game' in url or 'basegame' in url or
                  'base' in text or 'base game' in text or 'basegame' in text):
                link['category'] = 'Base Game'
                base_game_links.append(link)
            # Otherwise, assume it's the base game
            else:
                link['category'] = 'Base Game'
                base_game_links.append(link)

    print(f"Found {len(base_game_links)} base game links", file=sys.stderr)
    print(f"Found {len(update_links)} update links", file=sys.stderr)
    print(f"Found {len(dlc_links)} DLC links", file=sys.stderr)

    # Function to prioritize links
    def prioritize_links(links):
        # Only use 1fichier links as requested
        fichier_links = [link for link in links if '1fichier.com' in link['url'].lower()]

        # If no 1fichier links found, use all links
        if not fichier_links:
            return links

        return fichier_links

    # Prioritize each category
    prioritized_base_game = prioritize_links(base_game_links)
    prioritized_updates = prioritize_links(update_links)
    prioritized_dlc = prioritize_links(dlc_links)

    # Only include updates if config says so
    final_links = prioritized_base_game
    if config.get('download_updates', True) and prioritized_updates:
        final_links.extend(prioritized_updates)

    # Only include DLC if config says so
    if config.get('download_dlc', True) and prioritized_dlc:
        final_links.extend(prioritized_dlc)

    # Make sure all links have a category
    for link in final_links:
        if 'category' not in link:
            # Default to Base Game if no category is set
            link['category'] = 'Base Game'

    print(f"Final links count: {len(final_links)}", file=sys.stderr)
    return final_links

def download_game(url, config):
    """Get download links for a game from nxbrew"""
    try:
        # Get the page
        soup = get_html_page(url)
        if not soup:
            return {"success": False, "message": "Failed to fetch game page"}

        # Get game info
        title_elem = soup.find('h1', class_='entry-title')
        if not title_elem:
            print("Could not find game title", file=sys.stderr)
            title_text = "Unknown Game"
        else:
            title_text = title_elem.get_text().strip()
            print(f"Game title: {title_text}", file=sys.stderr)

        languages = get_languages(soup)
        print(f"Detected languages: {languages}", file=sys.stderr)

        # Get download links with prioritization
        links = get_download_links(soup, config)
        if not links:
            return {"success": False, "message": "No download links found"}

        # Group links by category for better display
        categorized_links = {
            "Base Game": [],
            "Update": [],
            "DLC": []
        }

        for link in links:
            category = link.get('category', 'Base Game')
            categorized_links[category].append(link)

        return {
            "success": True,
            "message": f"Found {len(links)} download links",
            "game_info": {
                "title": title_text,
                "languages": languages,
                "links": links,
                "categorized_links": categorized_links,
                "links_count": len(links)
            }
        }
    except Exception as e:
        print(f"Unexpected error in download_game: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        return {
            "success": False,
            "message": f"Unexpected error: {str(e)}",
            "error": traceback.format_exc()
        }

# Main execution
if __name__ == "__main__":
    try:
        if len(sys.argv) < 2:
            print(json.dumps({"success": False, "message": "No game URL provided"}))
            sys.exit(1)

        game_url = sys.argv[1]
        config_json = sys.argv[2] if len(sys.argv) > 2 else "{}"

        try:
            config = json.loads(config_json)
        except:
            print("Error parsing config JSON, using defaults", file=sys.stderr)
            config = {
                "preferred_format": "NSP",
                "download_updates": True,
                "download_dlc": True,
                "preferred_regions": ["USA", "EUR"],
                "preferred_languages": ["English"],
                "base_url": "https://nxbrew.net/"
            }

        result = download_game(game_url, config)
        print(json.dumps(result))
    except Exception as e:
        print(f"Unhandled exception: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        print(json.dumps({
            "success": False,
            "message": f"Unhandled exception: {str(e)}",
            "error": traceback.format_exc()
        }))
