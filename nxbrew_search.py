import sys
import json
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, quote_plus

def search_nxbrew(query):
    config = {"download_dir":"/Users/<USER>/Documents/Webstack/sites/prosiaczekpobieraczek/downloads","preferred_format":"NSP","download_updates":True,"download_dlc":True,"preferred_regions":["USA","EUR"],"preferred_languages":["English"],"base_url":"https://nxbrew.net","jdownloader":{"enabled":True,"device_name":"JDownloader@pp","username":"<EMAIL>","password":"xxxJD00!"}}
    base_url = config["base_url"]
    if not base_url.endswith('/'):
        base_url += '/'

    # First try direct search
    search_url = f"{base_url}?s={quote_plus(query)}"
    results = []

    try:
        # Get the search results page
        print(f"Searching at URL: {search_url}", file=sys.stderr)
        response = requests.get(search_url, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        # Find all game entries
        entries = soup.find_all('article')
        print(f"Found {len(entries)} entries on search page", file=sys.stderr)

        for entry in entries:
            title_elem = entry.find('h2', class_='entry-title')
            if not title_elem:
                continue

            link_elem = title_elem.find('a')
            if not link_elem:
                continue

            title = link_elem.text.strip()
            url = link_elem['href']

            # Get description
            desc = ""
            desc_elem = entry.find('div', class_='entry-content')
            if desc_elem:
                desc = desc_elem.text.strip()

            # Get date
            date = ""
            date_elem = entry.find('time', class_='entry-date')
            if date_elem:
                date = date_elem.text.strip()

            results.append({
                'title': title,
                'url': url,
                'description': desc,
                'date': date
            })

        # If we didn't find many results, also check the A-Z page
        if len(results) < 5:
            print("Not enough results, checking A-Z page", file=sys.stderr)
            az_results = search_az_page(base_url, query)

            # Merge results, avoiding duplicates
            existing_urls = {r['url'] for r in results}
            for az_result in az_results:
                if az_result['url'] not in existing_urls:
                    results.append(az_result)
                    existing_urls.add(az_result['url'])

        return results
    except Exception as e:
        print(f"Error searching nxbrew: {str(e)}", file=sys.stderr)
        return []

def search_az_page(base_url, query):
    """Search the A-Z page for games matching the query"""
    results = []
    try:
        # Get the A-Z page
        az_url = urljoin(base_url, "Index/game-index/")
        print(f"Checking A-Z page: {az_url}", file=sys.stderr)

        response = requests.get(az_url, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        # Find the content div (ei-items)
        content = soup.find('div', class_='ei-items')
        if not content:
            print("Could not find ei-items div", file=sys.stderr)
            return results

        # Find all list items with class ei-item
        items = content.find_all('li', class_='ei-item')
        if not items:
            print("Could not find ei-item list items", file=sys.stderr)
            return results

        # Extract links from the list items
        links = [item.find('a') for item in items if item.find('a')]
        print(f"Found {len(links)} links on A-Z page", file=sys.stderr)

        # Convert query to lowercase for case-insensitive matching
        query_lower = query.lower()

        # Check each link for a match with the query
        for link in links:
            title = link.text.strip()
            if query_lower in title.lower():
                url = link['href']

                results.append({
                    'title': title,
                    'url': url,
                    'description': 'Found in A-Z listing',
                    'date': ''
                })

        print(f"Found {len(results)} matches on A-Z page", file=sys.stderr)
        return results
    except Exception as e:
        print(f"Error searching A-Z page: {str(e)}", file=sys.stderr)
        return []

# Main execution
if __name__ == "__main__":
    query = sys.argv[1] if len(sys.argv) > 1 else ""
    results = search_nxbrew(query)
    print(json.dumps(results))
