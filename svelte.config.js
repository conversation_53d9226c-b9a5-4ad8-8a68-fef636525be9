import adapter from '@sveltejs/adapter-static';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://svelte.dev/docs/kit/integrations
	// for more information about preprocessors
	preprocess: vitePreprocess(),

	kit: {
		// Use adapter-static with fallback for SPA mode
		adapter: adapter({
			fallback: 'index.html', // Enable SPA mode
			strict: false // Ignore prerendering warnings
		}),

		// Configure prerendering
		prerender: {
			handleHttpError: ({ path, message }) => {
				// Ignore API routes during prerendering
				if (path.startsWith('/api/') || path.startsWith('/proxy/')) {
					return;
				}

				// Otherwise, fail the build
				throw new Error(message);
			},
			// Only prerender the root route
			entries: ['*']
		},

		// Konfiguracja dla Node.js modułów
		alias: {
			'discord.js': 'discord.js'
		}
	}
};

export default config;
