# Watch Feature

This feature allows users to add search queries to a watch list. The system will periodically check if there are new results for these queries and notify the user.

## How it works

1. Users can add search queries to the watch list through the "Watch" tab in the application.
2. A cron job runs daily at 22:00 to check if there are new results for the watched queries.
3. If new results are found, they are displayed in the "Watch" tab and a notification is sent via Discord bot.
4. Users can then click on the "Download" button to add the results to their downloads.

## Setting up the cron job

To set up the cron job, follow these steps:

1. Make sure the `cron-check-watched-items.sh` script is executable:
   ```bash
   chmod +x cron-check-watched-items.sh
   ```

2. Create a logs directory if it doesn't exist:
   ```bash
   mkdir -p logs
   ```

3. Add the cron job to your crontab:
   ```bash
   crontab -e
   ```

4. Add the following line to run the script daily at 22:00:
   ```
   0 22 * * * /path/to/your/app/cron-check-watched-items.sh
   ```

   Replace `/path/to/your/app/` with the actual path to your application directory.

5. Save and exit the editor.

## Manual check

You can also manually check for new results by clicking the "Check now" button in the "Watch" tab.

## Discord notifications

If the Discord bot is configured and running, it will send notifications when new results are found for watched queries.

## Files

- `src/lib/components/WatchList.svelte`: The UI component for the Watch tab
- `src/lib/server/watch-service.js`: Server-side logic for managing watched items
- `src/routes/api/watch/+server.js`: API endpoints for the Watch feature
- `check-watched-items.js`: Script that checks for new results
- `cron-check-watched-items.sh`: Shell script for running the check script via cron
