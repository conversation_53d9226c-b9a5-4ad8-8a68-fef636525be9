#!/usr/bin/env python3
import sys
import json
import re
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, quote_plus

def filter_links(links):
    """Filter out unwanted links"""
    filtered_links = []
    
    # Function to check if a link should be filtered
    def should_filter(link):
        filters = [
            "/category", "/dmca", "/dc", "/yt", "/2023", "/2022", "/2021", "/2020", 
            "/a-z", "/disclaimer", "/contact-us", "/privacy-policy", 
            "/business-with-us", "/discord", "/tg", "/page",
        ]
        
        # Check if the link contains any filter
        for filter_str in filters:
            if filter_str in link:
                return True
                
        # Check if the link ends with gamedrive.org
        if re.match(r"gamedrive\.org/?$", link):
            return True
            
        return False
    
    # Filter and remove links
    for link in links:
        if not should_filter(link):
            filtered_links.append(link)
            
    return filtered_links

def filter_links_by_game_name(links, game_name):
    """Filter links to only include those containing the game name"""
    filtered_links = []
    
    for link in links:
        # Check if the link contains the game name
        if game_name.lower() in link.lower():
            filtered_links.append(link)
            
    return filtered_links

def extract_game_name(url):
    """Extract game name from URL"""
    pattern = r"gamedrive\.org/([^/]+)/"
    match = re.search(pattern, url)
    if match:
        return match.group(1)
    return None

def extract_domain(url):
    """Extract domain from URL"""
    pattern = r"^[^:]+://([^/]+)"
    match = re.search(pattern, url)
    if match:
        return match.group(1)
    return None

def filter_download_links(links):
    """Filter to only include download links (not gamedrive.org links)"""
    filtered_links = []
    
    for link in links:
        if not "gamedrive.org" in link:
            filtered_links.append(link)
            
    return filtered_links

def find_attribute(html, tag, attr_name="", attr_value="", target_attr="href"):
    """Find all attributes in HTML (simplified version of HtmlWrapper.findAttribute)"""
    soup = BeautifulSoup(html, 'html.parser')
    
    if attr_name and attr_value:
        elements = soup.find_all(tag, {attr_name: attr_value})
    else:
        elements = soup.find_all(tag)
        
    results = []
    for element in elements:
        if element.has_attr(target_attr):
            results.append(element[target_attr])
            
    return results

def search_gamedrive(query):
    """Search for games on gamedrive.org"""
    # Prepare the search URL
    search_url = f"https://gamedrive.org/?s={quote_plus(query)}"
    
    # Prepare the game name for filtering (similar to the Lua implementation)
    game_name_mod = query.lower()
    game_name_mod = game_name_mod.replace(" ", "-")
    game_name_mod = game_name_mod.replace(":", "")
    game_name_mod = game_name_mod.replace("'", "")
    
    # Set up headers for the request
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    
    try:
        # Make the search request
        response = requests.get(search_url, headers=headers)
        response.raise_for_status()
        
        # Find all game links from the search results
        all_links = find_attribute(response.text, "a", "", "", "href")
        filtered_links = filter_links(all_links)
        game_links = filter_links_by_game_name(filtered_links, game_name_mod)
        
        # Process each game link to get download links
        game_results = []
        for link in game_links:
            try:
                game_response = requests.get(link, headers=headers)
                game_response.raise_for_status()
                
                # Extract game name from URL
                game_name = extract_game_name(link)
                if not game_name:
                    continue
                    
                # Create a result object
                game_result = {
                    "title": game_name.replace("-", " ").title(),
                    "url": link,
                    "source": "GameDrive",
                    "links": []
                }
                
                # Find download links
                download_links = filter_download_links(find_attribute(game_response.text, "a", "target", "_blank", "href"))
                
                # Process each download link
                for dl_link in download_links:
                    server_name = extract_domain(dl_link)
                    if server_name:
                        game_result["links"].append({
                            "name": server_name,
                            "url": dl_link
                        })
                
                # Only add games that have download links
                if game_result["links"]:
                    # Add a description based on the page content
                    soup = BeautifulSoup(game_response.text, 'html.parser')
                    description_elem = soup.find('div', class_='entry-content')
                    if description_elem:
                        # Get the first paragraph as description
                        paragraphs = description_elem.find_all('p')
                        if paragraphs:
                            game_result["description"] = paragraphs[0].get_text().strip()
                    
                    # Add the game to results
                    game_results.append(game_result)
                    
            except Exception as e:
                print(f"Error processing game link {link}: {str(e)}", file=sys.stderr)
                continue
        
        return game_results
        
    except Exception as e:
        print(f"Error searching gamedrive: {str(e)}", file=sys.stderr)
        return []

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python gamedrive_search.py <search_query>")
        sys.exit(1)
        
    query = sys.argv[1]
    results = search_gamedrive(query)
    print(json.dumps(results))
