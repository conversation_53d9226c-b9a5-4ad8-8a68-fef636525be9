/** @type {import('tailwindcss').Config} */
export default {
  content: ['./src/**/*.{html,js,svelte,ts}'],
  theme: {
    extend: {
      colors: {
        'bg-color': '#12110E',
        'card-bg': '#222222',
        'primary': '#ffb6c1',
        'secondary': '#D0E4E6',
        'error': '#ff6b6b',
        'text': '#fff5ee',
        'text-secondary': '#ffd8d8',
      },
      fontFamily: {
        'sans': ['Ubuntu', 'sans-serif'],
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
  ],
}
