import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Create build directory if it doesn't exist
if (!fs.existsSync(path.join(rootDir, 'build'))) {
  fs.mkdirSync(path.join(rootDir, 'build'), { recursive: true });
}

// Copy files from .svelte-kit/output/client to build directory
console.log('Copying files from .svelte-kit/output/client to build directory...');
copyDirectory(
  path.join(rootDir, '.svelte-kit', 'output', 'client'),
  path.join(rootDir, 'build')
);

// Copy index.html to build directory
console.log('Copying index.html to build directory...');
fs.copyFileSync(
  path.join(rootDir, '.svelte-kit', 'output', 'client', 'index.html'),
  path.join(rootDir, 'build', 'index.html')
);

console.log('Build preparation complete!');

/**
 * Copy a directory recursively
 * @param {string} source - Source directory
 * @param {string} destination - Destination directory
 */
function copyDirectory(source, destination) {
  // Create destination directory if it doesn't exist
  if (!fs.existsSync(destination)) {
    fs.mkdirSync(destination, { recursive: true });
  }

  // Get all files and directories in the source directory
  const entries = fs.readdirSync(source, { withFileTypes: true });

  // Copy each file and directory
  for (const entry of entries) {
    const sourcePath = path.join(source, entry.name);
    const destinationPath = path.join(destination, entry.name);

    if (entry.isDirectory()) {
      // Recursively copy directory
      copyDirectory(sourcePath, destinationPath);
    } else {
      // Copy file
      fs.copyFileSync(sourcePath, destinationPath);
    }
  }
}
