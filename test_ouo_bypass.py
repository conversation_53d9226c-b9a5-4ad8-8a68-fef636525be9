import sys
from bypass_ouo import bypass_ouo

def test_bypass():
    test_url = "https://ouo.io/RNMAV4"
    print(f"Testing bypass of ouo.io link: {test_url}")
    
    try:
        result = bypass_ouo(test_url)
        print(f"Original URL: {test_url}")
        print(f"Bypassed URL: {result}")
        return result
    except Exception as e:
        print(f"Error: {str(e)}")
        return None

if __name__ == "__main__":
    test_bypass()
