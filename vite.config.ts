import tailwindcss from '@tailwindcss/vite';
import { paraglideVitePlugin } from '@inlang/paraglide-js';
import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';

export default defineConfig({
	plugins: [
		tailwindcss(),
		sveltekit(),
		paraglideVitePlugin({
			project: './project.inlang',
			outdir: './src/lib/paraglide'
		})
	],
	server: {
		allowedHosts: ['prosiaczek.pogorzelski.ovh', 'localhost'],
		fs: {
			allow: ['.', 'node_modules']
		},
		host: true,
		strictPort: true
	},
	build: {
		rollupOptions: {
			external: ['discord.js', 'bufferutil', 'utf-8-validate', 'erlpack', 'zlib-sync']
		}
	},
	ssr: {
		external: ['discord.js', 'bufferutil', 'utf-8-validate', 'erlpack', 'zlib-sync']
	}
});
