import express from 'express';
import { fileURLToPath } from 'url';
import path from 'path';
import compression from 'compression';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { initializeBot } from './src/lib/server/discord-bot.js';
import {
  SERVER_PORT,
  REAL_DEBRID_PROXY_CONFIG,
  CORS_HEADERS
} from './src/lib/config/serverConfig.js';

// Set up file paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const app = express();

// Enable compression for all responses
app.use(compression());

// Parse JSON bodies for API requests
app.use(express.json());

// Serve static files from the build directory
app.use(express.static(path.join(__dirname, 'build')));

// Set cache headers for immutable assets
app.use('/_app/immutable', (req, res, next) => {
  res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
  next();
});

// Configure proxy for Real-Debrid API
app.use('/proxy', createProxyMiddleware(REAL_DEBRID_PROXY_CONFIG));

// Handle OPTIONS requests for CORS preflight
app.options('/proxy/*', (req, res) => {
  // Apply CORS headers from config
  Object.entries(CORS_HEADERS).forEach(([key, value]) => {
    res.header(key, value);
  });
  res.sendStatus(200);
});

// API routes handling

// API route for searching games
app.get('/api/sweech/search', async (req, res) => {
  try {
    const query = req.query.query;

    if (!query || query.trim().length < 3) {
      return res.status(400).json({
        error: 'Query must be at least 3 characters long',
        results: []
      });
    }

    console.log(`Searching for games with query: ${query}`);

    // Dynamically import and use searchGames
    const { searchGames } = await import('./src/lib/server/nxbrew-direct.js');
    const results = await searchGames(query);

    if (!results || results.length === 0) {
      console.log('No results found, using simulation');
      // You can implement a simulation function here if needed
      return res.json({
        results: [],
        simulation: true
      });
    }

    console.log(`Found ${results.length} results`);
    return res.json({ results });
  } catch (error) {
    console.error('Search error:', error);
    return res.status(500).json({
      error: `An error occurred during search: ${error.message}`,
      results: []
    });
  }
});

// API route for downloading games
app.post('/api/sweech/download', async (req, res) => {
  try {
    const { gameUrl } = req.body;

    if (!gameUrl) {
      return res.status(400).json({
        success: false,
        message: 'Game URL is required'
      });
    }

    console.log(`Downloading game from URL: ${gameUrl}`);

    // Dynamically import and use downloadGame
    const { downloadGame } = await import('./src/lib/server/nxbrew-direct.js');
    const result = await downloadGame(gameUrl);

    if (!result.success) {
      console.log('Download failed:', result.message);
      return res.status(500).json({
        success: false,
        message: result.message || 'Failed to download game'
      });
    }

    console.log('Download successful:', result.message);

    // Return success response with game info
    return res.json({
      success: true,
      message: result.message || 'Game added to JDownloader successfully',
      game_info: result.game_info || {}
    });
  } catch (error) {
    console.error('Download error:', error);
    return res.status(500).json({
      success: false,
      message: `An error occurred during download: ${error.message}`
    });
  }
});

// API route for getting configuration
app.get('/api/sweech/config', async (req, res) => {
  try {
    // Dynamically import and use getNxbrewConfig
    const { getNxbrewConfig } = await import('./src/lib/server/nxbrew-direct.js');
    const config = await getNxbrewConfig();
    return res.json({
      success: true,
      config
    });
  } catch (error) {
    console.error('Config error:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while getting configuration'
    });
  }
});

// API route for saving configuration
app.post('/api/sweech/config', async (req, res) => {
  try {
    const config = req.body;

    if (!config) {
      return res.status(400).json({
        success: false,
        message: 'Configuration data is required'
      });
    }

    // Dynamically import and use saveNxbrewConfig
    const { saveNxbrewConfig } = await import('./src/lib/server/nxbrew-direct.js');
    const result = await saveNxbrewConfig(config);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: result.message || 'Failed to save configuration'
      });
    }

    return res.json({
      success: true,
      message: 'Configuration saved successfully'
    });
  } catch (error) {
    console.error('Config save error:', error);
    return res.status(500).json({
      success: false,
      message: `An error occurred while saving configuration: ${error.message}`
    });
  }
});

// For all other routes, serve the index.html file (SPA fallback)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});

// Initialize the Discord bot
initializeBot().catch(error => {
  console.error('Failed to initialize Discord bot on server startup:', error);
  // Optionally, decide if the server should exit if the bot fails to initialize
  // process.exit(1);
});

// Start the server
app.listen(SERVER_PORT, () => {
  console.log(`Server running at http://localhost:${SERVER_PORT}`);
});
