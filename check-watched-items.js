/**
 * <PERSON><PERSON><PERSON> to check watched items for new results
 * This script is meant to be run by a cron job at 22:00 daily
 */

import { checkWatchedItems } from './src/lib/server/watch-service.js';
import { sendDiscordMessage } from './src/lib/server/discord-bot.js';
import { initializeBot } from './src/lib/server/discord-bot.js';

async function main() {
    try {
        console.log('Starting watched items check...');
        
        // Initialize Discord bot
        try {
            await initializeBot();
        } catch (botError) {
            console.error('Error initializing Discord bot:', botError);
            // Continue even if bot initialization fails
        }
        
        // Check watched items
        const result = await checkWatchedItems();
        
        if (!result.success) {
            console.error('Error checking watched items:', result.message);
            process.exit(1);
        }
        
        console.log(`Checked watched items. Found ${result.newResults.length} items with new results.`);
        
        // Send Discord notifications for new results
        if (result.newResults.length > 0) {
            console.log('Sending Discord notifications...');
            
            for (const { item, results } of result.newResults) {
                const message = `🔍 **Znaleziono nowe wyniki dla "${item.query}"!**\n\n` +
                    results.map((result, index) => 
                        `${index + 1}. **${result.title}** (${result.source})\n` +
                        `   Rozmiar: ${result.fileSize || 'Nieznany'}\n`
                    ).join('\n') +
                    '\n\nMożesz dodać te wyniki do pobierania w zakładce "Watch" na stronie.';
                
                await sendDiscordMessage(message);
            }
            
            console.log('Discord notifications sent.');
        }
        
        console.log('Watched items check completed successfully.');
        process.exit(0);
    } catch (error) {
        console.error('Error in watched items check script:', error);
        process.exit(1);
    }
}

// Run the script
main();
