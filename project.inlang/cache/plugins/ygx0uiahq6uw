var Ht=Object.create;var Lt=Object.defineProperty;var Qt=Object.getOwnPropertyDescriptor;var Xt=Object.getOwnPropertyNames;var Yt=Object.getPrototypeOf,tn=Object.prototype.hasOwnProperty;var nn=(l,s)=>()=>(s||l((s={exports:{}}).exports,s),s.exports);var rn=(l,s,g,u)=>{if(s&&typeof s=="object"||typeof s=="function")for(let f of Xt(s))!tn.call(l,f)&&f!==g&&Lt(l,f,{get:()=>s[f],enumerable:!(u=Qt(s,f))||u.enumerable});return l};var en=(l,s,g)=>(g=l!=null?Ht(Yt(l)):{},rn(s||!l||!l.__esModule?Lt(g,"default",{value:l,enumerable:!0}):g,l));var Pt=nn((Z,gt)=>{(function(l,s){typeof Z=="object"&&typeof gt=="object"?gt.exports=s():typeof define=="function"&&define.amd?define([],s):typeof Z=="object"?Z.Parsimmon=s():l.Parsimmon=s()})(typeof self<"u"?self:Z,function(){return function(l){var s={};function g(u){if(s[u])return s[u].exports;var f=s[u]={i:u,l:!1,exports:{}};return l[u].call(f.exports,f,f.exports,g),f.l=!0,f.exports}return g.m=l,g.c=s,g.d=function(u,f,C){g.o(u,f)||Object.defineProperty(u,f,{configurable:!1,enumerable:!0,get:C})},g.r=function(u){Object.defineProperty(u,"__esModule",{value:!0})},g.n=function(u){var f=u&&u.__esModule?function(){return u.default}:function(){return u};return g.d(f,"a",f),f},g.o=function(u,f){return Object.prototype.hasOwnProperty.call(u,f)},g.p="",g(g.s=0)}([function(l,s,g){"use strict";function u(t){if(!(this instanceof u))return new u(t);this._=t}var f=u.prototype;function C(t,n){for(var r=0;r<t;r++)n(r)}function L(t,n,r){return function(e,o){C(o.length,function(i){e(o[i],i,o)})}(function(e,o,i){n=t(n,e,o,i)},r),n}function _(t,n){return L(function(r,e,o,i){return r.concat([t(e,o,i)])},[],n)}function qt(t,n){var r={v:0,buf:n};return C(t,function(){var e;r={v:r.v<<1|(e=r.buf,e[0]>>7),buf:function(o){var i=L(function(a,c,p,v){return a.concat(p===v.length-1?Buffer.from([c,0]).readUInt16BE(0):v.readUInt16BE(p))},[],o);return Buffer.from(_(function(a){return(a<<1&65535)>>8},i))}(r.buf)}}),r}function ht(){return typeof Buffer<"u"}function K(){if(!ht())throw new Error("Buffer global does not exist; please use webpack if you need to parse Buffers in the browser.")}function dt(t){K();var n=L(function(i,a){return i+a},0,t);if(n%8!=0)throw new Error("The bits ["+t.join(", ")+"] add up to "+n+" which is not an even number of bytes; the total should be divisible by 8");var r,e=n/8,o=(r=function(i){return i>48},L(function(i,a){return i||(r(a)?a:i)},null,t));if(o)throw new Error(o+" bit range requested exceeds 48 bit (6 byte) Number max.");return new u(function(i,a){var c=e+a;return c>i.length?y(a,e.toString()+" bytes"):h(c,L(function(p,v){var m=qt(v,p.buf);return{coll:p.coll.concat(m.v),buf:m.buf}},{coll:[],buf:i.slice(a,c)},t).coll)})}function x(t,n){return new u(function(r,e){return K(),e+n>r.length?y(e,n+" bytes for "+t):h(e+n,r.slice(e,e+n))})}function V(t,n){if(typeof(r=n)!="number"||Math.floor(r)!==r||n<0||n>6)throw new Error(t+" requires integer length in range [0, 6].");var r}function H(t){return V("uintBE",t),x("uintBE("+t+")",t).map(function(n){return n.readUIntBE(0,t)})}function Q(t){return V("uintLE",t),x("uintLE("+t+")",t).map(function(n){return n.readUIntLE(0,t)})}function X(t){return V("intBE",t),x("intBE("+t+")",t).map(function(n){return n.readIntBE(0,t)})}function Y(t){return V("intLE",t),x("intLE("+t+")",t).map(function(n){return n.readIntLE(0,t)})}function W(t){return t instanceof u}function M(t){return{}.toString.call(t)==="[object Array]"}function T(t){return ht()&&Buffer.isBuffer(t)}function h(t,n){return{status:!0,index:t,value:n,furthest:-1,expected:[]}}function y(t,n){return M(n)||(n=[n]),{status:!1,index:-1,value:null,furthest:t,expected:n}}function w(t,n){if(!n||t.furthest>n.furthest)return t;var r=t.furthest===n.furthest?function(e,o){if(function(){if(u._supportsSet!==void 0)return u._supportsSet;var j=typeof Set<"u";return u._supportsSet=j,j}()&&Array.from){for(var i=new Set(e),a=0;a<o.length;a++)i.add(o[a]);var c=Array.from(i);return c.sort(),c}for(var p={},v=0;v<e.length;v++)p[e[v]]=!0;for(var m=0;m<o.length;m++)p[o[m]]=!0;var S=[];for(var b in p)({}).hasOwnProperty.call(p,b)&&S.push(b);return S.sort(),S}(t.expected,n.expected):n.expected;return{status:t.status,index:t.index,value:t.value,furthest:n.furthest,expected:r}}var tt={};function mt(t,n){if(T(t))return{offset:n,line:-1,column:-1};t in tt||(tt[t]={});for(var r=tt[t],e=0,o=0,i=0,a=n;a>=0;){if(a in r){e=r[a].line,i===0&&(i=r[a].lineStart);break}(t.charAt(a)===`
`||t.charAt(a)==="\r"&&t.charAt(a+1)!==`
`)&&(o++,i===0&&(i=a+1)),a--}var c=e+o,p=n-i;return r[n]={line:c,lineStart:i},{offset:n,line:c+1,column:p+1}}function q(t){if(!W(t))throw new Error("not a parser: "+t)}function nt(t,n){return typeof t=="string"?t.charAt(n):t[n]}function F(t){if(typeof t!="number")throw new Error("not a number: "+t)}function P(t){if(typeof t!="function")throw new Error("not a function: "+t)}function $(t){if(typeof t!="string")throw new Error("not a string: "+t)}var Ft=2,Rt=3,O=8,zt=5*O,Nt=4*O,vt="  ";function rt(t,n){return new Array(n+1).join(t)}function et(t,n,r){var e=n-t.length;return e<=0?t:rt(r,e)+t}function yt(t,n,r,e){return{from:t-n>0?t-n:0,to:t+r>e?e:t+r}}function Dt(t,n){var r,e,o,i,a,c=n.index,p=c.offset,v=1;if(p===t.length)return"Got the end of the input";if(T(t)){var m=p-p%O,S=p-m,b=yt(m,zt,Nt+O,t.length),j=_(function(d){return _(function(z){return et(z.toString(16),2,"0")},d)},function(d,z){var N=d.length,A=[],D=0;if(N<=z)return[d.slice()];for(var U=0;U<N;U++)A[D]||A.push([]),A[D].push(d[U]),(U+1)%z==0&&D++;return A}(t.slice(b.from,b.to).toJSON().data,O));i=function(d){return d.from===0&&d.to===1?{from:d.from,to:d.to}:{from:d.from/O,to:Math.floor(d.to/O)}}(b),e=m/O,r=3*S,S>=4&&(r+=1),v=2,o=_(function(d){return d.length<=4?d.join(" "):d.slice(0,4).join(" ")+"  "+d.slice(4).join(" ")},j),(a=(8*(i.to>0?i.to-1:i.to)).toString(16).length)<2&&(a=2)}else{var R=t.split(/\r\n|[\n\r\u2028\u2029]/);r=c.column-1,e=c.line-1,i=yt(e,Ft,Rt,R.length),o=R.slice(i.from,i.to),a=i.to.toString().length}var Vt=e-i.from;return T(t)&&(a=(8*(i.to>0?i.to-1:i.to)).toString(16).length)<2&&(a=2),L(function(d,z,N){var A,D=N===Vt,U=D?"> ":vt;return A=T(t)?et((8*(i.from+N)).toString(16),a,"0"):et((i.from+N+1).toString(),a," "),[].concat(d,[U+A+" | "+z],D?[vt+rt(" ",a)+" | "+et("",r," ")+rt("^",v)]:[])},[],o).join(`
`)}function bt(t,n){return[`
`,"-- PARSING FAILED "+rt("-",50),`

`,Dt(t,n),`

`,(r=n.expected,r.length===1?`Expected:

`+r[0]:`Expected one of the following: 

`+r.join(", ")),`
`].join("");var r}function Et(t){return t.flags!==void 0?t.flags:[t.global?"g":"",t.ignoreCase?"i":"",t.multiline?"m":"",t.unicode?"u":"",t.sticky?"y":""].join("")}function ut(){for(var t=[].slice.call(arguments),n=t.length,r=0;r<n;r+=1)q(t[r]);return u(function(e,o){for(var i,a=new Array(n),c=0;c<n;c+=1){if(!(i=w(t[c]._(e,o),i)).status)return i;a[c]=i.value,o=i.index}return w(h(o,a),i)})}function k(){var t=[].slice.call(arguments);if(t.length===0)throw new Error("seqMap needs at least one argument");var n=t.pop();return P(n),ut.apply(null,t).map(function(r){return n.apply(null,r)})}function ot(){var t=[].slice.call(arguments),n=t.length;if(n===0)return it("zero alternates");for(var r=0;r<n;r+=1)q(t[r]);return u(function(e,o){for(var i,a=0;a<t.length;a+=1)if((i=w(t[a]._(e,o),i)).status)return i;return i})}function wt(t,n){return st(t,n).or(I([]))}function st(t,n){return q(t),q(n),k(t,n.then(t).many(),function(r,e){return[r].concat(e)})}function G(t){$(t);var n="'"+t+"'";return u(function(r,e){var o=e+t.length,i=r.slice(e,o);return i===t?h(o,i):y(e,n)})}function B(t,n){(function(o){if(!(o instanceof RegExp))throw new Error("not a regexp: "+o);for(var i=Et(o),a=0;a<i.length;a++){var c=i.charAt(a);if(c!=="i"&&c!=="m"&&c!=="u"&&c!=="s")throw new Error('unsupported regexp flag "'+c+'": '+o)}})(t),arguments.length>=2?F(n):n=0;var r=function(o){return RegExp("^(?:"+o.source+")",Et(o))}(t),e=""+t;return u(function(o,i){var a=r.exec(o.slice(i));if(a){if(0<=n&&n<=a.length){var c=a[0],p=a[n];return h(i+c.length,p)}return y(i,"valid match group (0 to "+a.length+") in "+e)}return y(i,e)})}function I(t){return u(function(n,r){return h(r,t)})}function it(t){return u(function(n,r){return y(r,t)})}function at(t){if(W(t))return u(function(n,r){var e=t._(n,r);return e.index=r,e.value="",e});if(typeof t=="string")return at(G(t));if(t instanceof RegExp)return at(B(t));throw new Error("not a string, regexp, or parser: "+t)}function xt(t){return q(t),u(function(n,r){var e=t._(n,r),o=n.slice(r,e.index);return e.status?y(r,'not "'+o+'"'):h(r,null)})}function ft(t){return P(t),u(function(n,r){var e=nt(n,r);return r<n.length&&t(e)?h(r+1,e):y(r,"a character/byte matching "+t)})}function Bt(t,n){arguments.length<2&&(n=t,t=void 0);var r=u(function(e,o){return r._=n()._,r._(e,o)});return t?r.desc(t):r}function lt(){return it("fantasy-land/empty")}f.parse=function(t){if(typeof t!="string"&&!T(t))throw new Error(".parse must be called with a string or Buffer as its argument");var n,r=this.skip(pt)._(t,0);return n=r.status?{status:!0,value:r.value}:{status:!1,index:mt(t,r.furthest),expected:r.expected},delete tt[t],n},f.tryParse=function(t){var n=this.parse(t);if(n.status)return n.value;var r=bt(t,n),e=new Error(r);throw e.type="ParsimmonError",e.result=n,e},f.assert=function(t,n){return this.chain(function(r){return t(r)?I(r):it(n)})},f.or=function(t){return ot(this,t)},f.trim=function(t){return this.wrap(t,t)},f.wrap=function(t,n){return k(t,this,n,function(r,e){return e})},f.thru=function(t){return t(this)},f.then=function(t){return q(t),ut(this,t).map(function(n){return n[1]})},f.many=function(){var t=this;return u(function(n,r){for(var e=[],o=void 0;;){if(!(o=w(t._(n,r),o)).status)return w(h(r,e),o);if(r===o.index)throw new Error("infinite loop detected in .many() parser --- calling .many() on a parser which can accept zero characters is usually the cause");r=o.index,e.push(o.value)}})},f.tieWith=function(t){return $(t),this.map(function(n){if(function(o){if(!M(o))throw new Error("not an array: "+o)}(n),n.length){$(n[0]);for(var r=n[0],e=1;e<n.length;e++)$(n[e]),r+=t+n[e];return r}return""})},f.tie=function(){return this.tieWith("")},f.times=function(t,n){var r=this;return arguments.length<2&&(n=t),F(t),F(n),u(function(e,o){for(var i=[],a=void 0,c=void 0,p=0;p<t;p+=1){if(c=w(a=r._(e,o),c),!a.status)return c;o=a.index,i.push(a.value)}for(;p<n&&(c=w(a=r._(e,o),c),a.status);p+=1)o=a.index,i.push(a.value);return w(h(o,i),c)})},f.result=function(t){return this.map(function(){return t})},f.atMost=function(t){return this.times(0,t)},f.atLeast=function(t){return k(this.times(t),this.many(),function(n,r){return n.concat(r)})},f.map=function(t){P(t);var n=this;return u(function(r,e){var o=n._(r,e);return o.status?w(h(o.index,t(o.value)),o):o})},f.contramap=function(t){P(t);var n=this;return u(function(r,e){var o=n.parse(t(r.slice(e)));return o.status?h(e+r.length,o.value):o})},f.promap=function(t,n){return P(t),P(n),this.contramap(t).map(n)},f.skip=function(t){return ut(this,t).map(function(n){return n[0]})},f.mark=function(){return k(J,this,J,function(t,n,r){return{start:t,value:n,end:r}})},f.node=function(t){return k(J,this,J,function(n,r,e){return{name:t,value:r,start:n,end:e}})},f.sepBy=function(t){return wt(this,t)},f.sepBy1=function(t){return st(this,t)},f.lookahead=function(t){return this.skip(at(t))},f.notFollowedBy=function(t){return this.skip(xt(t))},f.desc=function(t){M(t)||(t=[t]);var n=this;return u(function(r,e){var o=n._(r,e);return o.status||(o.expected=t),o})},f.fallback=function(t){return this.or(I(t))},f.ap=function(t){return k(t,this,function(n,r){return n(r)})},f.chain=function(t){var n=this;return u(function(r,e){var o=n._(r,e);return o.status?w(t(o.value)._(r,o.index),o):o})},f.concat=f.or,f.empty=lt,f.of=I,f["fantasy-land/ap"]=f.ap,f["fantasy-land/chain"]=f.chain,f["fantasy-land/concat"]=f.concat,f["fantasy-land/empty"]=f.empty,f["fantasy-land/of"]=f.of,f["fantasy-land/map"]=f.map;var J=u(function(t,n){return h(n,mt(t,n))}),Ut=u(function(t,n){return n>=t.length?y(n,"any character/byte"):h(n+1,nt(t,n))}),Wt=u(function(t,n){return h(t.length,t.slice(n))}),pt=u(function(t,n){return n<t.length?y(n,"EOF"):h(n,null)}),Tt=B(/[0-9]/).desc("a digit"),$t=B(/[0-9]*/).desc("optional digits"),Gt=B(/[a-z]/i).desc("a letter"),Jt=B(/[a-z]*/i).desc("optional letters"),Zt=B(/\s*/).desc("optional whitespace"),Ct=B(/\s+/).desc("whitespace"),St=G("\r"),jt=G(`
`),_t=G(`\r
`),Ot=ot(_t,jt,St).desc("newline"),Kt=ot(Ot,pt);u.all=Wt,u.alt=ot,u.any=Ut,u.cr=St,u.createLanguage=function(t){var n={};for(var r in t)({}).hasOwnProperty.call(t,r)&&function(e){n[e]=Bt(function(){return t[e](n)})}(r);return n},u.crlf=_t,u.custom=function(t){return u(t(h,y))},u.digit=Tt,u.digits=$t,u.empty=lt,u.end=Kt,u.eof=pt,u.fail=it,u.formatError=bt,u.index=J,u.isParser=W,u.lazy=Bt,u.letter=Gt,u.letters=Jt,u.lf=jt,u.lookahead=at,u.makeFailure=y,u.makeSuccess=h,u.newline=Ot,u.noneOf=function(t){return ft(function(n){return t.indexOf(n)<0}).desc("none of '"+t+"'")},u.notFollowedBy=xt,u.of=I,u.oneOf=function(t){for(var n=t.split(""),r=0;r<n.length;r++)n[r]="'"+n[r]+"'";return ft(function(e){return t.indexOf(e)>=0}).desc(n)},u.optWhitespace=Zt,u.Parser=u,u.range=function(t,n){return ft(function(r){return t<=r&&r<=n}).desc(t+"-"+n)},u.regex=B,u.regexp=B,u.sepBy=wt,u.sepBy1=st,u.seq=ut,u.seqMap=k,u.seqObj=function(){for(var t,n={},r=0,e=(t=arguments,Array.prototype.slice.call(t)),o=e.length,i=0;i<o;i+=1){var a=e[i];if(!W(a)){if(M(a)&&a.length===2&&typeof a[0]=="string"&&W(a[1])){var c=a[0];if(Object.prototype.hasOwnProperty.call(n,c))throw new Error("seqObj: duplicate key "+c);n[c]=!0,r++;continue}throw new Error("seqObj arguments must be parsers or [string, parser] array pairs.")}}if(r===0)throw new Error("seqObj expects at least one named parser, found zero");return u(function(p,v){for(var m,S={},b=0;b<o;b+=1){var j,R;if(M(e[b])?(j=e[b][0],R=e[b][1]):(j=null,R=e[b]),!(m=w(R._(p,v),m)).status)return m;j&&(S[j]=m.value),v=m.index}return w(h(v,S),m)})},u.string=G,u.succeed=I,u.takeWhile=function(t){return P(t),u(function(n,r){for(var e=r;e<n.length&&t(nt(n,e));)e++;return h(e,n.slice(r,e))})},u.test=ft,u.whitespace=Ct,u["fantasy-land/empty"]=lt,u["fantasy-land/of"]=I,u.Binary={bitSeq:dt,bitSeqObj:function(t){K();var n={},r=0,e=_(function(i){if(M(i)){var a=i;if(a.length!==2)throw new Error("["+a.join(", ")+"] should be length 2, got length "+a.length);if($(a[0]),F(a[1]),Object.prototype.hasOwnProperty.call(n,a[0]))throw new Error("duplicate key in bitSeqObj: "+a[0]);return n[a[0]]=!0,r++,a}return F(i),[null,i]},t);if(r<1)throw new Error("bitSeqObj expects at least one named pair, got ["+t.join(", ")+"]");var o=_(function(i){return i[0]},e);return dt(_(function(i){return i[1]},e)).map(function(i){return L(function(a,c){return c[0]!==null&&(a[c[0]]=c[1]),a},{},_(function(a,c){return[a,i[c]]},o))})},byte:function(t){if(K(),F(t),t>255)throw new Error("Value specified to byte constructor ("+t+"=0x"+t.toString(16)+") is larger in value than a single byte.");var n=(t>15?"0x":"0x0")+t.toString(16);return u(function(r,e){var o=nt(r,e);return o===t?h(e+1,o):y(e,n)})},buffer:function(t){return x("buffer",t).map(function(n){return Buffer.from(n)})},encodedString:function(t,n){return x("string",n).map(function(r){return r.toString(t)})},uintBE:H,uint8BE:H(1),uint16BE:H(2),uint32BE:H(4),uintLE:Q,uint8LE:Q(1),uint16LE:Q(2),uint32LE:Q(4),intBE:X,int8BE:X(1),int16BE:X(2),int32BE:X(4),intLE:Y,int8LE:Y(1),int16LE:Y(2),int32LE:Y(4),floatBE:x("floatBE",4).map(function(t){return t.readFloatBE(0)}),floatLE:x("floatLE",4).map(function(t){return t.readFloatLE(0)}),doubleBE:x("doubleBE",8).map(function(t){return t.readDoubleBE(0)}),doubleLE:x("doubleLE",8).map(function(t){return t.readDoubleLE(0)})},l.exports=u}])})});var E=en(Pt(),1),un=()=>E.default.createLanguage({entry:l=>E.default.alt(l.findReference,E.default.any).many().map(s=>s.flatMap(g=>g)).map(s=>s.filter(g=>typeof g=="object").flat()),findReference:function(l){return E.default.seq(E.default.regex(/(import \* as m)|(import { m })/),l.findMessage.many())},findMessage:()=>E.default.seqMap(E.default.regex(/.*?(?<![a-zA-Z0-9/])m\./s),E.default.index,E.default.regex(/\w+/),E.default.index,E.default.regex(/\((?:[^()]|\([^()]*\))*\)/).or(E.default.succeed("")),(l,s,g,u,f)=>({messageId:`${g}`,position:{start:{line:s.line,character:s.column},end:{line:u.line,character:u.column+f.length}}}))});function kt(l){try{return un().entry.tryParse(l)}catch{return[]}}function ct(l){let s=l.trim().replace(/[^a-zA-Z0-9\s_.]/g,"").replace(/[\s.]+/g,"_");return/^[0-9]/.test(s)&&(s="_"+s),s}var It={messageReferenceMatchers:[async l=>kt(l.documentText)],extractMessageOptions:[{callback:l=>{let s=ct(l.bundleId);return{bundleId:s,messageReplacement:`{m.${s}()}`}}},{callback:l=>{let s=ct(l.bundleId);return{bundleId:s,messageReplacement:`m.${s}()`}}}],documentSelectors:[{language:"typescriptreact"},{language:"javascript"},{language:"typescript"},{language:"svelte"},{language:"astro"},{language:"vue"}]};var At="plugin.inlang.mFunctionMatcher",Mt={id:At,displayName:"Inlang M Function Matcher",description:"A plugin for the inlang SDK that uses a JSON file per language tag to store translations.",key:At,meta:{"app.inlang.ideExtension":It}};var yn=Mt;export{yn as default};
