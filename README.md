# Prosiaczek Pobieraczek (ePiglet)

A modern web application for managing downloads from various sources, including torrents, direct links, and game repositories. The application integrates with Real-Debrid for torrent processing and provides a user-friendly interface for searching and downloading content.

## Features

### Core Functionality

- **Torrent Management**: Add torrents via magnet links or .torrent files with drag-and-drop support
- **Real-Debrid Integration**: Process torrents through Real-Debrid to generate direct download links
- **Link Unrestriction**: Convert links from various file hosting services to direct download links
- **Download Management**: Track the status of your torrents and downloads

### Content Search

- **PC Games Search (ZOO)**: Search for PC games from multiple sources:
  - FitGirl Repacks
  - DODI Repacks
  - GOG Games
  - GameDrive
- **Nintendo Switch Games (Sweech)**: Search and download Nintendo Switch games from nxbrew.net
  - Support for NSP and XCI formats
  - Options to download game updates and DLC
  - Configurable region and language preferences

### Discord Bot Integration

- Search for PC games directly from Discord
- Automatically add torrents to Real-Debrid
- Return direct download links via Discord
- Support for slash commands and text commands

### UI Features

- Modern, responsive design with Tailwind CSS
- Dark theme with custom color scheme
- Custom UI components with Ubuntu font
- Mobile-friendly navigation with bottom bar
- Custom pig snout cursor

## Technology Stack

- **Frontend**:
  - Svelte 5 / SvelteKit 2
  - Tailwind CSS 4
  - Lucide icons
  - TypeScript

- **Backend**:
  - Node.js with Express
  - Python scripts for specific functionality (nxbrew integration)
  - Discord.js for bot functionality

- **APIs and Services**:
  - Real-Debrid API
  - SteamGridDB API (for game cover images)
  - OMDB API (for movie/TV show cover images)
  - nxbrew-dl integration for Nintendo Switch games

## Getting Started

### Prerequisites

- Node.js (recommended to use nvm for version management)
- Python 3 (for nxbrew integration)
- API keys for:
  - Real-Debrid
  - SteamGridDB (for game covers)
  - OMDB (for movie/TV covers)
  - Discord Bot (optional)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/prosiaczekpobieraczek.git
   cd prosiaczekpobieraczek
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure API keys and settings:
   - Create `.config` directory with appropriate configuration files
   - Set up Real-Debrid API key in the application

4. Start the development server:
   ```bash
   npm run dev
   ```

### Building for Production

```bash
npm run build:prod
```

This will create a production build in the `build` directory and prepare the server for deployment.

### Running in Production

```bash
npm run start
```

This will start the Express server that serves the built application and handles API requests.

## Configuration

### Real-Debrid

The application requires a Real-Debrid API key to function properly. This key is used to interact with the Real-Debrid API for torrent processing and link unrestriction.

### Discord Bot

To use the Discord bot functionality:

1. Create a bot in the Discord Developer Portal
2. Configure the bot with appropriate permissions
3. Add the bot token and other settings to the `.config/discord/config.json` file

### nxbrew-dl Integration

For Nintendo Switch game downloads:

1. Configure the nxbrew settings in the application
2. Ensure Python 3 is installed for running the nxbrew scripts

## Project Structure

- `/src`: Source code
  - `/lib`: Library code
    - `/components`: Svelte components
    - `/services`: Service modules for API interactions
    - `/server`: Server-side code
  - `/routes`: SvelteKit routes
- `/static`: Static assets
- `/build`: Production build output
- `/.config`: Configuration files
- `/scripts`: Utility scripts

## Main Features Explained

### Torrent Management

The application allows you to add torrents via magnet links or by uploading .torrent files. It supports drag-and-drop functionality for easy file uploads. Once added, torrents are processed through Real-Debrid, which handles the downloading and provides direct download links.

### Link Unrestriction

You can paste links from various file hosting services into the application, and it will use Real-Debrid to convert them into direct download links. This is useful for downloading files from services that have speed limitations or waiting times.

### PC Game Search (ZOO)

The ZOO feature allows you to search for PC games from multiple sources, including FitGirl Repacks, DODI Repacks, GOG Games, and GameDrive. Search results include game titles, file sizes, and magnet links that can be directly added to Real-Debrid for processing.

### Nintendo Switch Games (Sweech)

The Sweech feature integrates with nxbrew.net to provide a search interface for Nintendo Switch games. You can search for games and download them in either NSP or XCI format. The application also supports downloading game updates and DLC.

### Discord Bot

The Discord bot allows users to search for PC games directly from Discord. It supports both slash commands and text commands. When a user requests a game, the bot searches for it, adds the torrent to Real-Debrid, and returns direct download links.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
