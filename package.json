{"name": "prosiaczekpobieraczek", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "build:prod": "vite build && node scripts/prepare-server.js", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check .", "server": "node server.js", "start": "node server.js"}, "devDependencies": {"@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.2.5"}, "dependencies": {"@inlang/paraglide-js": "^2.0.0", "@sveltejs/adapter-static": "^3.0.8", "cheerio": "^1.0.0", "compression": "^1.8.0", "discord.js": "^14.18.0", "dotenv": "^16.5.0", "express": "^5.1.0", "hls.js": "^1.6.2", "http-proxy-middleware": "^3.0.5", "iconv-lite": "^0.6.3", "jschardet": "^3.1.4", "jsonwebtoken": "^9.0.2", "lucide-svelte": "^0.503.0", "node-fetch": "^3.3.2", "yauzl": "^3.2.0"}}