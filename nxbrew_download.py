import sys
import json
import os
import time
import traceback
from urllib.parse import urlparse
import requests
from bs4 import BeautifulSoup

# Import ouo.io bypass function from the local file
try:
    from bypass_ouo import bypass_ouo
    HAVE_OUO_BYPASS = True
    print("bypass_ouo module found. ouo.io links will be bypassed.", file=sys.stderr)
except ImportError:
    print("bypass_ouo module not found. ouo.io links will not be bypassed.", file=sys.stderr)
    HAVE_OUO_BYPASS = False

# Try to import myjdapi, with a helpful error if it's missing
try:
    import myjdapi
except ImportError:
    print("Error: myjdapi module not found. Installing...", file=sys.stderr)
    import subprocess
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "myjdapi"])
        import myjdapi
        print("Successfully installed myjdapi", file=sys.stderr)
    except Exception as e:
        print(f"Failed to install myjdapi: {str(e)}", file=sys.stderr)
        print(json.dumps({
            "success": False,
            "message": f"Failed to install required dependency: {str(e)}"
        }))
        sys.exit(1)

def get_html_page(url):
    """Get an HTML page as a soup"""
    try:
        print(f"Fetching page: {url}", file=sys.stderr)
        response = requests.get(url, headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        response.raise_for_status()
        print(f"Successfully fetched page ({len(response.text)} bytes)", file=sys.stderr)
        return BeautifulSoup(response.text, 'html.parser')
    except Exception as e:
        print(f"Error fetching page: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        return None

def get_languages(soup):
    """Parse languages from a soup"""
    if not soup:
        return []

    languages = []
    content = soup.find('div', class_='entry-content')
    if content:
        text = content.get_text()
        # Simple language detection - can be improved
        lang_patterns = {
            'English': ['english', 'eng'],
            'Japanese': ['japanese', 'jap'],
            'French': ['french', 'fra'],
            'German': ['german', 'ger'],
            'Spanish': ['spanish', 'spa'],
            'Italian': ['italian', 'ita'],
            'Dutch': ['dutch'],
            'Portuguese': ['portuguese', 'por'],
            'Russian': ['russian', 'rus'],
            'Korean': ['korean', 'kor'],
            'Chinese': ['chinese', 'chi']
        }

        for lang, patterns in lang_patterns.items():
            for pattern in patterns:
                if pattern.lower() in text.lower():
                    languages.append(lang)
                    break

    return languages

def get_download_links(soup):
    """Extract download links from the page"""
    if not soup:
        return []

    links = []
    content = soup.find('div', class_='entry-content')
    if not content:
        print("Could not find entry-content div", file=sys.stderr)
        # Try alternative class names that might be used
        content = soup.find('div', class_='post-content')
        if not content:
            content = soup.find('article')
            if not content:
                print("Could not find any content div", file=sys.stderr)
                return []
            else:
                print("Found article element instead", file=sys.stderr)
        else:
            print("Found post-content div instead", file=sys.stderr)

    print("Searching for download links...", file=sys.stderr)

    # Find all download sections - look for paragraphs and list items
    download_sections = content.find_all(['p', 'li', 'div'])
    print(f"Found {len(download_sections)} potential download sections", file=sys.stderr)

    # Common download sites to look for
    download_sites = ['1fichier', 'mediafire', 'mega', 'uptobox', 'zippy', 'zippyshare',
                     'rapidgator', 'uploaded', 'uploadgig', 'nitroflare', 'turbobit',
                     'filefactory', 'katfile', 'alfafile', 'hitfile', 'upload', 'ouo.io', 'ouo.press']

    for section in download_sections:
        # Look for links
        a_tags = section.find_all('a')
        for a in a_tags:
            if 'href' in a.attrs:
                link = a['href']
                text = a.get_text().strip()

                # Filter for common download sites
                if any(site in link.lower() for site in download_sites):
                    print(f"Found download link: {text} -> {link}", file=sys.stderr)

                    # Handle ouo.io links if the bypass module is available
                    if ('ouo.io' in link.lower() or 'ouo.press' in link.lower()) and HAVE_OUO_BYPASS:
                        try:
                            bypassed_link = bypass_ouo(link)
                            if bypassed_link and bypassed_link != link:
                                print(f"Bypassed ouo.io link: {link} -> {bypassed_link}", file=sys.stderr)
                                link = bypassed_link
                        except Exception as e:
                            print(f"Error bypassing ouo.io link: {str(e)}", file=sys.stderr)

                    links.append({
                        'url': link,
                        'text': text
                    })

    print(f"Total download links found: {len(links)}", file=sys.stderr)
    return links

def add_to_jdownloader(links, download_dir, title, config):
    """Add links to JDownloader"""
    if not links:
        print("No links to add to JDownloader", file=sys.stderr)
        return False

    try:
        # Connect to JDownloader
        print(f"Connecting to JDownloader with username: {config['jdownloader']['username']}", file=sys.stderr)
        print(f"Device name: {config['jdownloader']['device_name']}", file=sys.stderr)

        jd = myjdapi.Myjdapi()
        jd.set_app_key("nxbrewdl")
        jd.connect(config['jdownloader']['username'], config['jdownloader']['password'])

        print("Successfully connected to JDownloader", file=sys.stderr)

        device = jd.get_device(config['jdownloader']['device_name'])
        print(f"Successfully connected to device: {config['jdownloader']['device_name']}", file=sys.stderr)

        # Add links to JDownloader
        for i, link in enumerate(links):
            print(f"Adding link {i+1}/{len(links)}: {link['text']}", file=sys.stderr)

            device.linkgrabber.add_links([
                {
                    "autostart": True,
                    "links": link['url'],
                    "destinationFolder": download_dir,
                    "packageName": f"Switch Game - {title} - {link['text']}"
                }
            ])
            print(f"Successfully added link {i+1}", file=sys.stderr)

        print("All links added to JDownloader successfully", file=sys.stderr)
        return True
    except Exception as e:
        print(f"Error adding to JDownloader: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        return False

def download_game(url, config):
    """Download a game from nxbrew"""
    try:
        # Get the page
        soup = get_html_page(url)
        if not soup:
            return {"success": False, "message": "Failed to fetch game page"}

        # Get game info
        title_elem = soup.find('h1', class_='entry-title')
        if not title_elem:
            print("Could not find game title", file=sys.stderr)
            title_text = "Unknown Game"
        else:
            title_text = title_elem.get_text().strip()
            print(f"Game title: {title_text}", file=sys.stderr)

        languages = get_languages(soup)
        print(f"Detected languages: {languages}", file=sys.stderr)

        # Get download links
        links = get_download_links(soup)
        if not links:
            return {"success": False, "message": "No download links found"}

        # Add to JDownloader
        download_dir = config['download_dir']
        print(f"Using download directory: {download_dir}", file=sys.stderr)

        jd_success = add_to_jdownloader(links, download_dir, title_text, config)

        return {
            "success": jd_success,
            "message": "Game added to JDownloader successfully" if jd_success else "Failed to add game to JDownloader",
            "game_info": {
                "title": title_text,
                "languages": languages,
                "links_count": len(links)
            }
        }
    except Exception as e:
        print(f"Unexpected error in download_game: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        return {
            "success": False,
            "message": f"Unexpected error: {str(e)}",
            "error": traceback.format_exc()
        }

# Main execution
if __name__ == "__main__":
    try:
        if len(sys.argv) < 2:
            print(json.dumps({"success": False, "message": "No game URL provided"}))
            sys.exit(1)

        game_url = sys.argv[1]
        config_json = sys.argv[2] if len(sys.argv) > 2 else "{}"

        try:
            config = json.loads(config_json)
        except:
            print("Error parsing config JSON, using defaults", file=sys.stderr)
            config = {
                "download_dir": "/Users/<USER>/Downloads",
                "preferred_format": "NSP",
                "download_updates": True,
                "download_dlc": True,
                "preferred_regions": ["USA", "EUR"],
                "preferred_languages": ["English"],
                "base_url": "https://nxbrew.net/",
                "jdownloader": {
                    "enabled": True,
                    "device_name": "JDownloader@pp",
                    "username": "<EMAIL>",
                    "password": "xxxJD00!"
                }
            }

        result = download_game(game_url, config)
        print(json.dumps(result))
    except Exception as e:
        print(f"Unhandled exception: {str(e)}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        print(json.dumps({
            "success": False,
            "message": f"Unhandled exception: {str(e)}",
            "error": traceback.format_exc()
        }))
